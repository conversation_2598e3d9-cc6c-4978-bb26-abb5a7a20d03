# 当前项目特有
# 注意：加入开发环境配置文件

# Java IDE和编辑器文件
.idea/
.vscode/
*.swp
*.swo
*.iml
*.ipr
*.iws
.DS_Store
.DS_Store?
._*
.Trashes
ehthumbs.db
Thumbs.db
.gradle/
local.properties
app/build/
*.apk
*.aab
captures/
.cxx/
*.hprof

# Java相关
target/
build/
out/
bin/
logs/
dist/
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 环境配置
.env
.env.local
.env.*.local

# Maven相关
.mvn/
mvnw
mvnw.cmd
.flattened-pom.xml

# 临时文件
tmp/
temp/
*.tmp
*~

# Node.js相关
node_modules/
npm-debug.log
yarn-error.log
.npm/

# Python相关
__pycache__/
*.py[cod]
*.so
.Python
env/
venv/
.env/
.venv/

# Ruby
*.gem
*.rbc
.bundle/
vendor/bundle


# 前端相关-缓存文件
.npm-cache/
.yarn-cache/
.nuxt/
coverage/
.nyc_output/
.cache/
.eslintcache
.prettiercache

# 版本控制相关
.git/
.github/
.gitee/
.svn/
LICENSE

# 其他
*.bak
*.zip
*.tar.gz
*.rar
*.pdf
*.docx
*.pptx
*.xlsx