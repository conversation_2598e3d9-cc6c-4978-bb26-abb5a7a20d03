package com.shuyi.discipline.domain.monitor

import android.app.ActivityManager
import android.app.ApplicationExitInfo
import android.content.Context
import android.os.Build
import androidx.annotation.RequiresApi
import com.shuyi.discipline.data.repository.RuntimeRecordRepository
import timber.log.Timber

/**
 * ApplicationExitInfo 监控器
 * 适用于 Android 11+ (API 30+)
 */
@RequiresApi(Build.VERSION_CODES.R)
class ExitInfoMonitor(
    private val context: Context,
    private val repository: RuntimeRecordRepository,
    private val stateManager: PersistentAppStateManager
) {

    private val activityManager: ActivityManager by lazy {
        context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
    }

    /**
     * 启动监控
     */
    suspend fun startMonitoring() {
        try {
            Timber.d("启动 ApplicationExitInfo 监控 (API ${android.os.Build.VERSION.SDK_INT})")

            // 首先打印当前所有的退出信息用于调试
            printExitInfoStats()

            // 注意：不在启动时立即检查退出信息
            // 退出信息检查将在应用进入前台时进行，并传递正确的应用启动时间

        } catch (e: Exception) {
            Timber.e(e, "启动 ApplicationExitInfo 监控失败")
        }
    }

    /**
     * 停止监控
     */
    fun stopMonitoring() {
        Timber.d("停止 ApplicationExitInfo 监控")
        // ApplicationExitInfo 是被动查询，无需主动停止
    }

    /**
     * 检查并记录退出信息
     */
    suspend fun checkAndRecordExitInfo(appStartTime: Long = 0L) {
        try {
            Timber.d("开始检查 ApplicationExitInfo...")
            if (appStartTime > 0) {
                Timber.d("使用传入的应用启动时间: ${java.util.Date(appStartTime)}")
            }

            val exitInfos = getRecentExitInfos()

            Timber.d("找到 ${exitInfos.size} 条退出信息记录")

            if (exitInfos.isEmpty()) {
                Timber.d("未找到退出信息记录")
                return
            }

            val lastProcessedExitTime = repository.getLastProcessedExitTime()
            Timber.d("上次处理的退出时间: ${if (lastProcessedExitTime > 0) java.util.Date(lastProcessedExitTime) else "无"}")

            // 处理新的退出记录
            var processedCount = 0
            exitInfos.forEach { exitInfo ->
                Timber.d("检查退出记录: 时间=${java.util.Date(exitInfo.timestamp)}, 原因=${getExitReasonName(exitInfo.reason)}")
                if (exitInfo.timestamp > lastProcessedExitTime) {
                    processExitInfo(exitInfo, appStartTime)
                    processedCount++
                } else {
                    Timber.d("退出记录已处理过，跳过")
                }
            }

            Timber.d("本次处理了 $processedCount 条新的退出记录")

        } catch (e: Exception) {
            Timber.e(e, "检查退出信息失败")
        }
    }

    /**
     * 获取最近的退出信息（仅主进程）
     */
    private fun getRecentExitInfos(): List<ApplicationExitInfo> {
        return try {
            // 获取最近20条退出记录（增加数量以确保能找到主进程记录）
            val allExitInfos = activityManager.getHistoricalProcessExitReasons(context.packageName, 0, 20)

            // 过滤出主进程的退出信息
            val mainProcessExitInfos = allExitInfos.filter { exitInfo ->
                isMainProcess(exitInfo)
            }

            Timber.d("获取到 ${allExitInfos.size} 条总退出记录，其中主进程记录 ${mainProcessExitInfos.size} 条")

            // 返回最近10条主进程退出记录
            mainProcessExitInfos.take(10)
        } catch (e: Exception) {
            Timber.e(e, "获取退出信息失败")
            emptyList()
        }
    }

    /**
     * 判断是否为主进程的退出信息
     */
    private fun isMainProcess(exitInfo: ApplicationExitInfo): Boolean {
        val processName = exitInfo.processName
        val packageName = context.packageName

        // 主进程的进程名通常等于包名
        val isMainProcess = processName == packageName

        Timber.d("进程检查: $processName, 是否主进程: $isMainProcess")

        return isMainProcess
    }

    /**
     * 处理单个退出信息
     */
    private suspend fun processExitInfo(exitInfo: ApplicationExitInfo, appStartTime: Long = 0L) {
        try {
            val exitTime = exitInfo.timestamp
            val exitReason = mapExitReason(exitInfo.reason)
            val exitDescription = getExitDescription(exitInfo)

            Timber.d("[ExitInfoMonitor] 处理退出信息:")
            Timber.d("  退出时间: ${java.util.Date(exitTime)}")
            Timber.d("  退出原因: $exitReason")
            Timber.d("  详细描述: $exitDescription")
            Timber.d("  进程重要性: ${exitInfo.importance}")
            Timber.d("  内存使用: PSS=${exitInfo.pss}KB, RSS=${exitInfo.rss}KB")

            // 🎯 通知状态管理器应用已关闭
            val shouldRecord = stateManager.recordAppShutdown(exitTime, exitDescription)

            if (!shouldRecord) {
                Timber.d("[ExitInfoMonitor] 关闭时间已被处理过，跳过重复记录")
                return
            }

            // 🎯 使用状态管理器的启动时间
            val actualStartTime = if (appStartTime > 0 && appStartTime > exitTime) {
                Timber.d("  ✅ 使用传入的应用启动时间: ${java.util.Date(appStartTime)}")
                appStartTime
            } else if (stateManager.isValidTime()) {
                val stateManagerTime = stateManager.getAppStartTime()
                if (stateManagerTime > exitTime) {
                    Timber.d("  ✅ 使用状态管理器的启动时间: ${java.util.Date(stateManagerTime)}")
                    stateManagerTime
                } else {
                    Timber.w("  ⚠️ 状态管理器时间无效，跳过此退出记录的处理")
                    Timber.w("  退出时间: ${java.util.Date(exitTime)}")
                    Timber.w("  状态管理器时间: ${java.util.Date(stateManagerTime)}")
                    return
                }
            } else {
                Timber.w("  ⚠️ 无法获取有效的应用启动时间，跳过此退出记录的处理")
                Timber.w("  退出时间: ${java.util.Date(exitTime)}")
                Timber.w("  传入启动时间: ${if (appStartTime > 0) java.util.Date(appStartTime) else "未提供"}")
                Timber.w("  状态管理器调试信息:")
                Timber.w(stateManager.getDebugInfo())
                return
            }

            val downtime = actualStartTime - exitTime

            Timber.d("📊 [ExitInfoMonitor] 时间计算结果:")
            Timber.d("  最终确定的应用重启时间: ${java.util.Date(actualStartTime)}")
            Timber.d("  计算的未运行时长: ${downtime}ms (${downtime / 1000}秒)")

            // 🎯 添加合理性检查
            if (downtime < 1000) {
                Timber.w("⚠️ [ExitInfoMonitor] 警告：未运行时长过短 (${downtime}ms)，可能存在时间记录问题！")
            } else if (downtime > 24 * 60 * 60 * 1000) {
                Timber.w("⚠️ [ExitInfoMonitor] 警告：未运行时长过长 (${downtime / 1000 / 60}分钟)，请检查时间记录！")
            } else {
                Timber.d("✅ [ExitInfoMonitor] 未运行时长合理")
            }

            // 🎯 记录退出和未运行时长（由监控系统记录，与状态管理器协作）
            repository.recordExit(
                exitTime = exitTime,
                exitReason = exitReason,
                description = "ExitInfoMonitor检测: $exitDescription",
                downtimeStart = exitTime,
                downtimeEnd = actualStartTime,
                downtimeDuration = downtime
            )

            // 更新最后处理的退出时间
            repository.updateLastProcessedExitTime(exitTime)

            Timber.d("✅ [ExitInfoMonitor] 已记录未运行时长: ${downtime}ms (${downtime / 1000}秒)")

        } catch (e: Exception) {
            Timber.e(e, "[ExitInfoMonitor] 处理退出信息失败: ${exitInfo.timestamp}")
        }
    }



    /**
     * 映射退出原因
     */
    private fun mapExitReason(reason: Int): ExitReason {
        return when (reason) {
            ApplicationExitInfo.REASON_USER_REQUESTED -> ExitReason.USER_EXIT
            ApplicationExitInfo.REASON_USER_STOPPED -> ExitReason.USER_EXIT
            ApplicationExitInfo.REASON_CRASH -> ExitReason.APP_CRASH
            ApplicationExitInfo.REASON_CRASH_NATIVE -> ExitReason.APP_CRASH
            ApplicationExitInfo.REASON_ANR -> ExitReason.APP_CRASH
            ApplicationExitInfo.REASON_LOW_MEMORY -> ExitReason.SYSTEM_KILLED
            ApplicationExitInfo.REASON_EXCESSIVE_RESOURCE_USAGE -> ExitReason.SYSTEM_KILLED
            ApplicationExitInfo.REASON_OTHER -> ExitReason.SYSTEM_KILLED
            ApplicationExitInfo.REASON_SIGNALED -> ExitReason.SYSTEM_KILLED
            ApplicationExitInfo.REASON_UNKNOWN -> ExitReason.UNKNOWN
            else -> ExitReason.UNKNOWN
        }
    }

    /**
     * 获取退出描述
     */
    private fun getExitDescription(exitInfo: ApplicationExitInfo): String {
        val reasonName = getExitReasonName(exitInfo.reason)
        val importance = getImportanceName(exitInfo.importance)
        
        return buildString {
            append("退出原因: $reasonName")
            append(", 进程重要性: $importance")
            
            if (exitInfo.pss > 0) {
                append(", 内存使用: ${exitInfo.pss}KB")
            }
            
            exitInfo.description?.let { desc ->
                if (desc.isNotBlank()) {
                    append(", 详情: $desc")
                }
            }
        }
    }

    /**
     * 获取退出原因名称
     */
    private fun getExitReasonName(reason: Int): String {
        return when (reason) {
            ApplicationExitInfo.REASON_USER_REQUESTED -> "用户主动退出"
            ApplicationExitInfo.REASON_USER_STOPPED -> "用户在设置中停止"
            ApplicationExitInfo.REASON_CRASH -> "应用崩溃"
            ApplicationExitInfo.REASON_CRASH_NATIVE -> "Native崩溃"
            ApplicationExitInfo.REASON_ANR -> "ANR超时"
            ApplicationExitInfo.REASON_LOW_MEMORY -> "内存不足"
            ApplicationExitInfo.REASON_EXCESSIVE_RESOURCE_USAGE -> "资源使用过度"
            ApplicationExitInfo.REASON_OTHER -> "其他原因"
            ApplicationExitInfo.REASON_SIGNALED -> "信号杀死"
            ApplicationExitInfo.REASON_UNKNOWN -> "未知原因"
            ApplicationExitInfo.REASON_DEPENDENCY_DIED -> "依赖进程死亡"
            ApplicationExitInfo.REASON_INITIALIZATION_FAILURE -> "初始化失败"
            ApplicationExitInfo.REASON_PERMISSION_CHANGE -> "权限变化"
            ApplicationExitInfo.REASON_PACKAGE_STATE_CHANGE -> "包状态变化"
            ApplicationExitInfo.REASON_PACKAGE_UPDATED -> "包更新"
            else -> "未知原因($reason)"
        }
    }

    /**
     * 获取进程重要性名称
     */
    private fun getImportanceName(importance: Int): String {
        return when (importance) {
            ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND -> "前台"
            ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND_SERVICE -> "前台服务"
            ActivityManager.RunningAppProcessInfo.IMPORTANCE_TOP_SLEEPING -> "顶层休眠"
            ActivityManager.RunningAppProcessInfo.IMPORTANCE_VISIBLE -> "可见"
            ActivityManager.RunningAppProcessInfo.IMPORTANCE_PERCEPTIBLE -> "可感知"
            ActivityManager.RunningAppProcessInfo.IMPORTANCE_CANT_SAVE_STATE -> "无法保存状态"
            ActivityManager.RunningAppProcessInfo.IMPORTANCE_SERVICE -> "服务"
            ActivityManager.RunningAppProcessInfo.IMPORTANCE_CACHED -> "缓存"
            ActivityManager.RunningAppProcessInfo.IMPORTANCE_GONE -> "已消失"
            else -> "未知($importance)"
        }
    }

    /**
     * 获取最新的退出信息（用于调试）
     */
    fun getLatestExitInfo(): ApplicationExitInfo? {
        return try {
            getRecentExitInfos().firstOrNull()
        } catch (e: Exception) {
            Timber.e(e, "获取最新退出信息失败")
            null
        }
    }

    /**
     * 打印退出信息统计（用于调试）
     */
    fun printExitInfoStats() {
        try {
            // 获取所有退出信息（包括子进程）
            val allExitInfos = activityManager.getHistoricalProcessExitReasons(context.packageName, 0, 20)
            val mainProcessExitInfos = getRecentExitInfos()

            Timber.d("=== ApplicationExitInfo 统计 ===")
            Timber.d("所有进程退出记录数: ${allExitInfos.size}")
            Timber.d("主进程退出记录数: ${mainProcessExitInfos.size}")

            Timber.d("--- 所有进程退出记录 ---")
            allExitInfos.forEachIndexed { index, exitInfo ->
                Timber.d("退出记录 #${index + 1}:")
                Timber.d("  进程名: ${exitInfo.processName}")
                Timber.d("  时间: ${java.util.Date(exitInfo.timestamp)}")
                Timber.d("  原因: ${getExitReasonName(exitInfo.reason)}")
                Timber.d("  重要性: ${getImportanceName(exitInfo.importance)}")
                Timber.d("  是否主进程: ${isMainProcess(exitInfo)}")
            }

            Timber.d("--- 主进程退出记录 ---")
            mainProcessExitInfos.forEachIndexed { index, exitInfo ->
                Timber.d("主进程退出记录 #${index + 1}:")
                Timber.d("  时间: ${java.util.Date(exitInfo.timestamp)}")
                Timber.d("  原因: ${getExitReasonName(exitInfo.reason)}")
                Timber.d("  重要性: ${getImportanceName(exitInfo.importance)}")
            }

        } catch (e: Exception) {
            Timber.e(e, "打印退出信息统计失败")
        }
    }
}
