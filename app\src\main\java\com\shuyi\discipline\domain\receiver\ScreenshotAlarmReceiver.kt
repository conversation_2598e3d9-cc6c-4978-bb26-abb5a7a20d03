package com.shuyi.discipline.domain.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.shuyi.discipline.domain.service.ScreenshotService
import timber.log.Timber

/**
 * 截图闹钟广播接收器
 */
class ScreenshotAlarmReceiver : BroadcastReceiver() {
    
    override fun onReceive(context: Context, intent: Intent) {
        if (intent.action == ACTION_TAKE_SCREENSHOT) {
            Timber.d("收到截图闹钟广播")
            
            // 启动截图服务
            val serviceIntent = Intent(context, ScreenshotService::class.java).apply {
                action = ScreenshotService.ACTION_TAKE_SCREENSHOT
            }
            
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent)
            } else {
                context.startService(serviceIntent)
            }
        }
    }
    
    companion object {
        const val ACTION_TAKE_SCREENSHOT = "com.shuyi.discipline.ACTION_TAKE_SCREENSHOT"
    }
} 