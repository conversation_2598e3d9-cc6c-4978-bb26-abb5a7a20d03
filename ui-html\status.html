<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统状态 - 自动截屏</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="mobile-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-bar-time">9:41</div>
            <div class="status-bar-icons">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 10a6 6 0 0 0-12 0v11h12V10z"/><path d="M9 2v1"/><path d="M15 2v1"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z"/><path d="M5 10h14"/><path d="M9 16h6"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 4h14a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1z"/><path d="M4 8h16"/><path d="M8 4v16"/></svg>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold">系统状态</h1>
                <button class="p-2 bg-gray-100 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M2 12h6"/><path d="M22 12h-6"/><path d="M12 2v6"/><path d="M12 22v-6"/><path d="m4.93 4.93 4.24 4.24"/><path d="m14.83 14.83 4.24 4.24"/><path d="m14.83 9.17-4.24 4.24"/><path d="m4.93 19.07 4.24-4.24"/></svg>
                </button>
            </div>

            <!-- 时间范围选择 -->
            <div class="flex space-x-2 mb-6">
                <button class="px-4 py-2 bg-blue-500 text-white rounded-full text-sm font-medium">24小时</button>
                <button class="px-4 py-2 bg-gray-200 rounded-full text-sm font-medium">48小时</button>
                <button class="px-4 py-2 bg-gray-200 rounded-full text-sm font-medium">7天</button>
            </div>

            <!-- 后台运行状态 -->
            <div class="card mb-6">
                <h2 class="text-lg font-semibold mb-4">后台运行状态</h2>

                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                        <div class="status-indicator green"></div>
                        <span>当前状态</span>
                    </div>
                    <span class="text-green-600 font-medium">正常运行中</span>
                </div>

                <div class="mb-4">
                    <div class="flex justify-between text-sm mb-1">
                        <span>运行时间</span>
                        <span>23小时12分钟 / 24小时</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-bar-fill" style="width: 96%"></div>
                    </div>
                </div>

                <div class="p-3 bg-red-50 rounded-lg mb-4">
                    <div class="flex items-center mb-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-red-500 mr-2"><circle cx="12" cy="12" r="10"/><line x1="12" y1="8" x2="12" y2="12"/><line x1="12" y1="16" x2="12.01" y2="16"/></svg>
                        <span class="font-medium text-red-700">后台未运行时间</span>
                    </div>
                    <p class="text-red-700 text-sm">总计: 48分钟</p>
                </div>

                <!-- 时间线 -->
                <div class="timeline">
                    <div class="timeline-line"></div>

                    <div class="timeline-item">
                        <div class="timeline-dot bg-red-500"></div>
                        <div class="text-sm">
                            <div class="font-medium">后台服务停止</div>
                            <div class="text-gray-500">今天 03:12 - 03:42 (30分钟)</div>
                            <div class="mt-1 text-gray-600">系统自动休眠导致服务停止</div>
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-dot bg-red-500"></div>
                        <div class="text-sm">
                            <div class="font-medium">后台服务停止</div>
                            <div class="text-gray-500">昨天 23:45 - 00:03 (18分钟)</div>
                            <div class="mt-1 text-gray-600">应用被系统强制关闭</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 截屏功能状态 -->
            <div class="card mb-6">
                <h2 class="text-lg font-semibold mb-4">截屏功能状态</h2>

                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center">
                        <div class="status-indicator green"></div>
                        <span>当前状态</span>
                    </div>
                    <span class="text-green-600 font-medium">正常运行中</span>
                </div>

                <div class="mb-4">
                    <div class="flex justify-between text-sm mb-1">
                        <span>运行时间</span>
                        <span>22小时48分钟 / 24小时</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-bar-fill" style="width: 95%"></div>
                    </div>
                </div>

                <div class="p-3 bg-red-50 rounded-lg mb-4">
                    <div class="flex items-center mb-2">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-red-500 mr-2"><circle cx="12" cy="12" r="10"/><line x1="12" y1="8" x2="12" y2="12"/><line x1="12" y1="16" x2="12.01" y2="16"/></svg>
                        <span class="font-medium text-red-700">截屏功能未运行时间</span>
                    </div>
                    <p class="text-red-700 text-sm">总计: 1小时12分钟</p>
                </div>

                <!-- 时间线 -->
                <div class="timeline">
                    <div class="timeline-line"></div>

                    <div class="timeline-item">
                        <div class="timeline-dot bg-red-500"></div>
                        <div class="text-sm">
                            <div class="font-medium">截屏功能停止</div>
                            <div class="text-gray-500">今天 03:12 - 03:42 (30分钟)</div>
                            <div class="mt-1 text-gray-600">系统自动休眠导致服务停止</div>
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-dot bg-red-500"></div>
                        <div class="text-sm">
                            <div class="font-medium">截屏功能停止</div>
                            <div class="text-gray-500">昨天 23:45 - 00:03 (18分钟)</div>
                            <div class="mt-1 text-gray-600">应用被系统强制关闭</div>
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-dot bg-yellow-500"></div>
                        <div class="text-sm">
                            <div class="font-medium">截屏功能暂停</div>
                            <div class="text-gray-500">昨天 18:30 - 19:00 (30分钟)</div>
                            <div class="mt-1 text-gray-600">用户手动暂停</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航栏 -->
        <div class="tab-bar">
            <div class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9 22 9 12 15 12 15 22"/></svg>
                <span>首页</span>
            </div>
            <div class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><circle cx="9" cy="9" r="2"/><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/></svg>
                <span>截图</span>
            </div>
            <div class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><path d="M3 15h18"/><path d="M9 9h.01"/><path d="M15 9h.01"/></svg>
                <span>拼图</span>
            </div>
            <div class="tab-item active">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 20h9"/><path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"/></svg>
                <span>状态</span>
            </div>
            <div class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/></svg>
                <span>设置</span>
            </div>
        </div>
    </div>
</body>
</html>
