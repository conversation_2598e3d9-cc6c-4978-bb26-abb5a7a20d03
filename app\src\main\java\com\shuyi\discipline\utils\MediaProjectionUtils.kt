package com.shuyi.discipline.utils

import android.content.Context
import android.content.SharedPreferences
import timber.log.Timber

/**
 * MediaProjection权限相关工具类
 */
object MediaProjectionUtils {

    private const val PREFS_NAME = "media_projection_status"
    private const val KEY_HAS_PERMISSION = "has_permission"
    private const val KEY_PERMISSION_GRANTED_TIME = "permission_granted_time"
    private const val KEY_PERMISSION_REVOKED_TIME = "permission_revoked_time"

    /**
     * 保存权限状态
     */
    fun savePermissionStatus(context: Context, hasPermission: Boolean) {
        try {
            val prefs = getPrefs(context)
            val currentTime = System.currentTimeMillis()
            
            prefs.edit().apply {
                putBoolean(KEY_HAS_PERMISSION, hasPermission)
                if (hasPermission) {
                    putLong(KEY_PERMISSION_GRANTED_TIME, currentTime)
                    remove(KEY_PERMISSION_REVOKED_TIME)
                } else {
                    putLong(KEY_PERMISSION_REVOKED_TIME, currentTime)
                    remove(KEY_PERMISSION_GRANTED_TIME)
                }
                apply()
            }
            
            Timber.d("MediaProjection权限状态已保存: $hasPermission")
        } catch (e: Exception) {
            Timber.e(e, "保存MediaProjection权限状态失败")
        }
    }

    /**
     * 获取权限状态
     */
    fun hasPermission(context: Context): Boolean {
        return try {
            getPrefs(context).getBoolean(KEY_HAS_PERMISSION, false)
        } catch (e: Exception) {
            Timber.e(e, "获取MediaProjection权限状态失败")
            false
        }
    }

    /**
     * 获取权限授予时间
     */
    fun getPermissionGrantedTime(context: Context): Long {
        return try {
            getPrefs(context).getLong(KEY_PERMISSION_GRANTED_TIME, 0L)
        } catch (e: Exception) {
            Timber.e(e, "获取权限授予时间失败")
            0L
        }
    }

    /**
     * 获取权限撤销时间
     */
    fun getPermissionRevokedTime(context: Context): Long {
        return try {
            getPrefs(context).getLong(KEY_PERMISSION_REVOKED_TIME, 0L)
        } catch (e: Exception) {
            Timber.e(e, "获取权限撤销时间失败")
            0L
        }
    }

    /**
     * 清除权限状态
     */
    fun clearPermissionStatus(context: Context) {
        try {
            getPrefs(context).edit().clear().apply()
            Timber.d("MediaProjection权限状态已清除")
        } catch (e: Exception) {
            Timber.e(e, "清除MediaProjection权限状态失败")
        }
    }

    private fun getPrefs(context: Context): SharedPreferences {
        return context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }
}
