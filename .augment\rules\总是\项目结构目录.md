---
type: "manual"
---

如果需要查看项目结构，请参考下方的项目结构内容。

注意：此结构仅供参考。具体以项目现有结构为准，因为目录可能不会及时更新。

有需要时，也可以在此基础上，创建新的文件夹。

当需要创建的新文件时，请将文件放到合适的文件夹下，以保持项目结构的合理和清晰。

**项目结构：**

├── app/                           # 应用主模块
│   ├── src/
│   │   ├── main/
│   │   │   ├── java/              # Kotlin 代码目录
│   │   │   │   └── com/shuyi/discipline/
│   │   │   │       ├── data/      # 数据层
│   │   │   │       │   ├── model/ # 数据模型
│   │   │   │       │   ├── repository/ # 数据仓库
│   │   │   │       │   └── source/ # 数据源
│   │   │   │       ├── di/        # 依赖注入
│   │   │   │       ├── domain/    # 领域层（业务逻辑）
│   │   │   │       │   └── usecase/ # 用例
│   │   │   │       ├── ui/        # 界面层
│   │   │   │       │   ├── activity/ # Activity
│   │   │   │       │   └── viewmodel/ # ViewModel
│   │   │   │       └── util/      # 工具类
│   │   │   ├── res/               # 资源文件
│   │   │   └── AndroidManifest.xml
│   │   ├── test/                  # 单元测试
│   │   └── androidTest/           # UI测试
│   ├── build.gradle               # 模块级构建脚本
│   └── proguard-rules.pro         # 混淆规则
├── build.gradle                   # 项目级构建脚本
├── gradle/
├── gradle.properties
├── gradlew
├── gradlew.bat
└── settings.gradle