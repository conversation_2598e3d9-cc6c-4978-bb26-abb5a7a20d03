package com.shuyi.discipline.ui.screen.screenshots

import android.app.Application
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CalendarToday
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.shuyi.discipline.data.model.Screenshot
import com.shuyi.discipline.ui.components.BottomNavBar
import com.shuyi.discipline.ui.components.DatePickerDialog
import com.shuyi.discipline.ui.navigation.Screen
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import com.shuyi.discipline.utils.FileUtils

/**
 * 截图页面
 */
@Composable
fun ScreenshotsScreen(
    onNavigateBack: () -> Unit = {},
    onNavigateToHome: () -> Unit = {},
    onNavigateToReports: () -> Unit = {},
    onNavigateToConfig: () -> Unit = {},
    viewModel: ScreenshotsViewModel = viewModel(
        factory = ScreenshotsViewModelFactory(LocalContext.current.applicationContext as Application)
    ),
    navController: NavController = rememberNavController()
) {
    val uiState by viewModel.uiState.collectAsState()
    var selectedTabIndex by remember { mutableStateOf(1) } // 截图选项卡
    var showPreview by remember { mutableStateOf(false) }
    var previewScreenshotIndex by remember { mutableStateOf(0) }
    var showDatePicker by remember { mutableStateOf(false) }

    // 监听页面生命周期，控制数据监听
    val lifecycleOwner = LocalLifecycleOwner.current
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_RESUME -> {
                    // 页面可见时开启实时监听
                    viewModel.setPageVisible(true)
                }
                Lifecycle.Event.ON_PAUSE -> {
                    // 页面不可见时停止监听，节省资源
                    viewModel.setPageVisible(false)
                }
                else -> {}
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
            // 页面销毁时确保停止监听
            viewModel.setPageVisible(false)
        }
    }

    // 导航到预览页面
    fun navigateToPreview(index: Int) {
        previewScreenshotIndex = index
        showPreview = true
    }

    // 显示预览屏幕
    if (showPreview) {
        ScreenshotPreviewScreen(
            initialScreenshotIndex = previewScreenshotIndex,
            onNavigateBack = { showPreview = false }
        )
        return
    }

    Scaffold(
        containerColor = MaterialTheme.colorScheme.background,
        bottomBar = {
            BottomNavBar(
                selectedIndex = selectedTabIndex,
                onItemSelected = { index ->
                    selectedTabIndex = index
                    // 根据选中的标签执行相应的导航
                    when(index) {
                        0 -> onNavigateToHome()
                        // 1是截图页面，已经在此页面，无需导航
                        2 -> onNavigateToReports() // 导航到拼图页面
                        3 -> navController.navigate(Screen.Status.route) {
                            // 避免创建多个实例
                            launchSingleTop = true
                        }
                        4 -> onNavigateToConfig()
                    }
                },
                onNavigateToHome = onNavigateToHome,
                onNavigateToScreenshots = { /* 已在截图页面，无需导航 */ },
                onNavigateToCollages = onNavigateToReports,
                onNavigateToStatus = {
                    navController.navigate(Screen.Status.route) {
                        // 避免创建多个实例
                        launchSingleTop = true
                    }
                },
                onNavigateToConfig = onNavigateToConfig
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp)
        ) {
            // 标题栏
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "截图列表",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold
                )

                Row(
                    horizontalArrangement = Arrangement.End,
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.fillMaxWidth()
                ) {
                    // 日期选择按钮
                    Box(
                        modifier = Modifier
                            .size(40.dp)
                            .clip(CircleShape)
                            .background(MaterialTheme.colorScheme.surfaceVariant)
                            .clickable { showDatePicker = true },
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.CalendarToday,
                            contentDescription = "选择日期",
                            tint = MaterialTheme.colorScheme.onSurfaceVariant
                        )
                    }
                }
            }



            if (uiState.isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else {
                // 内容区域
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(bottom = 16.dp)  // 确保内容不会覆盖底部导航栏
                        .verticalScroll(rememberScrollState())
                ) {
                    // 截图统计卡片
                    ScreenshotStatsCard(
                        selectedDate = uiState.selectedDate,
                        count = uiState.totalScreenshots,
                        duplicateCount = uiState.duplicateCount
                    )

                    // 所有截图，按时间降序排列
                    if (uiState.allScreenshots.isNotEmpty()) {
                        ScreenshotGrid(
                            screenshots = uiState.allScreenshots,
                            onScreenshotClick = { index ->
                                navigateToPreview(index)
                            }
                        )
                    }

                    // 无数据提示
                    if (uiState.totalScreenshots == 0) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(200.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "当前日期没有截图",
                                color = MaterialTheme.colorScheme.onSecondaryContainer
                            )
                        }
                    }
                }
            }
        }
    }

    // 日期选择对话框
    DatePickerDialog(
        isVisible = showDatePicker,
        selectedDate = uiState.dateItems.getOrNull(uiState.selectedDateIndex)?.date ?: Date(),
        onDateSelected = { selectedDate ->
            viewModel.selectDateByDate(selectedDate)
        },
        onDismiss = { showDatePicker = false }
    )
}

/**
 * 日期选择器项
 */
@Composable
fun DateSelectorItem(
    dayOfWeek: String,
    day: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .padding(end = 12.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(if (isSelected) MaterialTheme.colorScheme.primary else Color.Transparent)
            .clickable(onClick = onClick)
            .padding(horizontal = 12.dp, vertical = 8.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = dayOfWeek,
            fontSize = 12.sp,
            color = if (isSelected) MaterialTheme.colorScheme.onPrimary else MaterialTheme.colorScheme.onSecondaryContainer
        )
        Text(
            text = day,
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = if (isSelected) MaterialTheme.colorScheme.onPrimary else MaterialTheme.colorScheme.onSurface
        )
    }
}

/**
 * 截图统计卡片
 */
@Composable
fun ScreenshotStatsCard(selectedDate: String, count: Int, duplicateCount: Int = 0) {
    // 解析日期并格式化为"几月几日"
    val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    val displayFormat = SimpleDateFormat("M月d日", Locale.getDefault())
    val todayFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    val today = todayFormat.format(Date())

    val displayDate = try {
        val date = dateFormat.parse(selectedDate)
        if (date != null) {
            displayFormat.format(date)
        } else {
            "未知日期"
        }
    } catch (e: Exception) {
        "未知日期"
    }

    val isToday = selectedDate == today

    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Text(
                    text = displayDate,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )

                if (isToday) {
                    Text(
                        text = "今日",
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.primary
                    )
                }
            }

            Column(
                horizontalAlignment = Alignment.End
            ) {
                Text(
                    text = "共计 $count 张截图",
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSecondaryContainer
                )

                // 由于重复截图已被删除，不再显示去重数量
                // 如果需要查看全局去重统计，可以在设置页面查看
            }
        }
    }
}

/**
 * 截图网格
 */
@Composable
fun ScreenshotGrid(
    screenshots: List<Screenshot>,
    onScreenshotClick: (index: Int) -> Unit = {}
) {
    val timeFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())

    LazyVerticalGrid(
        columns = GridCells.Fixed(2),
        modifier = Modifier
            .fillMaxWidth()
            .height((screenshots.size * 110).coerceAtLeast(200).coerceAtMost(600).dp),
        verticalArrangement = Arrangement.spacedBy(8.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        itemsIndexed(
            items = screenshots,
            key = { _, screenshot -> screenshot.id }
        ) { index, screenshot ->
            ScreenshotItem(
                filePath = screenshot.filePath,
                time = timeFormat.format(Date(screenshot.timestamp)),
                onClick = { onScreenshotClick(index) }
            )
        }
    }
}

/**
 * 截图项
 */
@Composable
fun ScreenshotItem(
    filePath: String,
    time: String,
    onClick: () -> Unit = {}
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(16f / 10f)  // 稍微调整比例，使其更紧凑
            .clip(RoundedCornerShape(8.dp))
            .background(MaterialTheme.colorScheme.surfaceVariant)
            .clickable(onClick = onClick)
    ) {
        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current)
                .data(File(filePath))
                .crossfade(true)
                .build(),
            contentDescription = "截图",
            contentScale = ContentScale.Crop,
            modifier = Modifier.fillMaxSize()
        )

        Box(
            modifier = Modifier
                .align(Alignment.BottomStart)
                .fillMaxWidth()
                .background(Color.Black.copy(alpha = 0.5f))
                .padding(8.dp)
        ) {
            Column {
                Text(
                    text = time,
                    color = Color.White,
                    style = MaterialTheme.typography.bodySmall
                )
                Text(
                    text = FileUtils.formatFileSize(filePath),
                    color = Color.White.copy(alpha = 0.8f),
                    style = MaterialTheme.typography.bodySmall.copy(fontSize = 10.sp)
                )
            }
        }
    }
}

/**
 * 预览
 */
@Preview(showBackground = true)
@Composable
fun ScreenshotsScreenPreview() {
    MaterialTheme {
        Surface {
            ScreenshotsScreen()
        }
    }
}