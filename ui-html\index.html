<!DOCTYPE html>
<html lang="zh-CN" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动截屏应用原型</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/lucide-static@latest/font/lucide.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gray-100 p-6 transition-colors duration-300">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-center">自动截屏应用 - 界面原型</h1>
        <div class="flex items-center">
            <span id="theme-text" class="mr-2 font-medium text-gray-700 dark:text-gray-300">浅色模式</span>
            <button id="theme-toggle" class="flex items-center px-4 py-2 rounded-lg bg-blue-500 hover:bg-blue-600 dark:bg-indigo-600 dark:hover:bg-indigo-700 text-white transition-colors duration-300 shadow-md">
                <i id="theme-icon" class="lucide-sun text-yellow-300 dark:hidden mr-2" style="width: 20px; height: 20px;"></i>
                <i id="theme-icon-dark" class="lucide-moon text-blue-200 hidden dark:inline-block mr-2" style="width: 20px; height: 20px;"></i>
                <span id="theme-button-text" class="text-sm">切换到深色模式</span>
            </button>
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- 首页/控制中心 -->
        <div class="prototype-container">
            <h2 class="text-lg font-semibold mb-2">首页/控制中心</h2>
            <div class="phone-frame">
                <iframe src="home.html" frameborder="0"></iframe>
            </div>
        </div>

        <!-- 截图列表页 -->
        <div class="prototype-container">
            <h2 class="text-lg font-semibold mb-2">截图列表</h2>
            <div class="phone-frame">
                <iframe src="screenshots.html" frameborder="0"></iframe>
            </div>
        </div>

        <!-- 拼图列表页 -->
        <div class="prototype-container">
            <h2 class="text-lg font-semibold mb-2">拼图列表</h2>
            <div class="phone-frame">
                <iframe src="collages.html" frameborder="0"></iframe>
            </div>
        </div>

        <!-- 系统状态页 -->
        <div class="prototype-container">
            <h2 class="text-lg font-semibold mb-2">系统状态</h2>
            <div class="phone-frame">
                <iframe src="status.html" frameborder="0"></iframe>
            </div>
        </div>

        <!-- 设置页面 -->
        <div class="prototype-container">
            <h2 class="text-lg font-semibold mb-2">设置</h2>
            <div class="phone-frame">
                <iframe src="settings.html" frameborder="0"></iframe>
            </div>
        </div>

        <!-- 权限管理页 -->
        <div class="prototype-container">
            <h2 class="text-lg font-semibold mb-2">权限管理</h2>
            <div class="phone-frame">
                <iframe src="permissions.html" frameborder="0"></iframe>
            </div>
        </div>
    </div>

    <script>
        // 主题切换功能
        const htmlElement = document.documentElement;
        const themeToggle = document.getElementById('theme-toggle');

        // 检查本地存储中的主题设置
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme === 'dark') {
            htmlElement.classList.add('dark');
            document.body.classList.remove('bg-gray-100');
            document.body.classList.add('bg-black');
            document.body.classList.add('text-white');
            updatePrototypeContainers(true);
        }

        // 切换主题
        themeToggle.addEventListener('click', () => {
            const isDark = htmlElement.classList.toggle('dark');

            // 更新背景颜色
            if (isDark) {
                document.body.classList.remove('bg-gray-100');
                document.body.classList.add('bg-black');
                document.body.classList.add('text-white');
                localStorage.setItem('theme', 'dark');
            } else {
                document.body.classList.add('bg-gray-100');
                document.body.classList.remove('bg-black');
                document.body.classList.remove('text-white');
                localStorage.setItem('theme', 'light');
            }

            // 更新原型容器样式
            updatePrototypeContainers(isDark);
        });

        // 更新原型容器样式
        function updatePrototypeContainers(isDark) {
            const containers = document.querySelectorAll('.prototype-container');
            containers.forEach(container => {
                if (isDark) {
                    container.classList.add('dark-mode');
                } else {
                    container.classList.remove('dark-mode');
                }
            });
        }

        // 初始化时检查当前主题并更新容器
        updatePrototypeContainers(htmlElement.classList.contains('dark'));
    </script>
</body>
</html>
