package com.shuyi.discipline.ui.screen.report

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Image
import androidx.compose.material.icons.filled.Dashboard
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.shuyi.discipline.ui.components.BottomNavBar
import com.shuyi.discipline.ui.components.PageTitleBar
import com.shuyi.discipline.ui.model.UiState
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import android.app.Application

/**
 * 报告列表屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ReportListScreen(
    onNavigateBack: () -> Unit,
    onNavigateToReportDetail: (String) -> Unit,
    onNavigateToConfig: () -> Unit = {},
    onNavigateToScreenshots: () -> Unit = {},
    viewModel: ReportListViewModel = viewModel(
        factory = ReportViewModelFactory(LocalContext.current.applicationContext as Application)
    )
) {
    val reportsState by viewModel.reports.collectAsState()
    val scrollState = rememberScrollState()
    var selectedTabIndex by remember { mutableStateOf(2) } // 报告选项卡

    LaunchedEffect(key1 = Unit) {
        viewModel.loadReports()
    }

    // 定义颜色
    val lightGray = Color(0xFFF8F8F8)
    val primaryBlue = Color(0xFF007AFF)

    Scaffold(
        containerColor = lightGray,
        bottomBar = {
            BottomNavBar(
                selectedIndex = 2, // 固定为报告标签
                onItemSelected = { index ->
                    // 根据选中的标签执行相应的导航
                    when(index) {
                        0 -> onNavigateBack() // 返回首页
                        1 -> onNavigateToScreenshots() // 直接导航到截图页面
                        // 2是报告页面，已经在此页面，无需导航
                        3 -> onNavigateToConfig() // 直接导航到设置页面
                    }
                },
                onNavigateToHome = onNavigateBack,
                onNavigateToScreenshots = onNavigateToScreenshots,
                onNavigateToCollages = { /* 已在报告页面，无需导航 */ },
                onNavigateToConfig = onNavigateToConfig
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(lightGray)
        ) {
            // 标题 - 直接从顶部开始，不再使用StatusBar
            PageTitleBar(
                title = "截图报告",
                showBackButton = true,
                onBackClick = onNavigateBack
            )

            // 内容区域
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp)
            ) {
            when (reportsState) {
                is UiState.Loading -> {
                    Text("加载中...")
                }
                is UiState.Empty -> {
                    Column(
                        modifier = Modifier.fillMaxSize(),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Text(
                            text = "暂无报告",
                            style = MaterialTheme.typography.titleMedium,
                            textAlign = TextAlign.Center
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(
                            text = "截图服务收集足够的数据后将自动生成报告",
                            style = MaterialTheme.typography.bodyMedium,
                            textAlign = TextAlign.Center
                        )
                    }
                }
                is UiState.Error -> {
                    Text("加载错误: ${(reportsState as UiState.Error).message}")
                }
                is UiState.Success -> {
                    val reports = (reportsState as UiState.Success<List<ReportInfo>>).data
                    LazyColumn(
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(reports) { report ->
                            Card(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 8.dp)
                                    .clickable { onNavigateToReportDetail(report.date) },
                                shape = RoundedCornerShape(16.dp),
                                colors = CardDefaults.cardColors(containerColor = Color.White),
                                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                            ) {
                                Row(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(16.dp),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Column(modifier = Modifier.weight(1f)) {
                                        Text(
                                            text = report.dateFormatted,
                                            fontSize = 16.sp,
                                            fontWeight = FontWeight.SemiBold
                                        )
                                        Spacer(modifier = Modifier.height(4.dp))
                                        Text(
                                            text = "共 ${report.screenshotCount} 张截图",
                                            fontSize = 14.sp,
                                            color = Color.Gray
                                        )
                                    }

                                    Box(
                                        modifier = Modifier
                                            .size(32.dp)
                                            .background(Color(0xFFE9E9EB), RoundedCornerShape(16.dp)),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        Icon(
                                            imageVector = Icons.Default.KeyboardArrowRight,
                                            contentDescription = "查看详情",
                                            tint = Color.Gray,
                                            modifier = Modifier.size(20.dp)
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
                else -> {
                    // 处理Idle状态
                    Text("准备加载报告列表...")
                }
            }
        }
    }
}



@Preview(showBackground = true)
@Composable
fun ReportListScreenPreview() {
    MaterialTheme {
        ReportListScreen(
            onNavigateBack = {},
            onNavigateToReportDetail = {},
            onNavigateToConfig = {},
            onNavigateToScreenshots = {}
        )
    }
}
}