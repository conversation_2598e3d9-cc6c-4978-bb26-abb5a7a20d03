package com.shuyi.discipline.domain.service

import android.accessibilityservice.AccessibilityService
import android.accessibilityservice.AccessibilityServiceInfo
import android.content.Intent
import android.view.accessibility.AccessibilityEvent
import timber.log.Timber

/**
 * 自律助手无障碍服务
 *
 * 该服务用于监听应用使用情况，以便在特定场景下自动操作
 */
class DisciplineAccessibilityService : AccessibilityService() {

    companion object {
        // 服务是否已启用
        private var isServiceEnabled = false

        /**
         * 检查服务是否已启用
         */
        fun isEnabled(): Boolean {
            return isServiceEnabled
        }
    }

    override fun onServiceConnected() {
        super.onServiceConnected()
        Timber.d("无障碍服务已连接")

        // 使用XML配置文件中的设置，不在代码中重复配置
        // 仅更新服务状态
        isServiceEnabled = true

        // 发送广播通知服务已启用
        sendBroadcast(Intent("com.shuyi.discipline.ACCESSIBILITY_SERVICE_ENABLED"))
    }

    override fun onAccessibilityEvent(event: AccessibilityEvent) {
        // 处理无障碍事件
        // 为了减少系统负载，这里不做任何处理
        // 仅在窗口状态变化时记录包名，用于调试
        if (event.eventType == AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED) {
            // 限制日志记录频率，只记录包名变化
            val packageName = event.packageName?.toString() ?: "unknown"
            if (packageName != lastPackageName) {
                lastPackageName = packageName
                Timber.d("窗口切换到: $packageName")
            }
        }
    }

    // 记录上一个包名，用于减少重复日志
    private var lastPackageName: String = ""

    override fun onInterrupt() {
        Timber.d("无障碍服务被中断")
    }

    override fun onUnbind(intent: Intent?): Boolean {
        Timber.d("无障碍服务已解绑")
        isServiceEnabled = false

        // 发送广播通知服务已禁用
        sendBroadcast(Intent("com.shuyi.discipline.ACCESSIBILITY_SERVICE_DISABLED"))

        return super.onUnbind(intent)
    }
}
