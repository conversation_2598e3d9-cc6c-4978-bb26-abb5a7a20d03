<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页 - 自动截屏</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="mobile-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-bar-time">9:41</div>
            <div class="status-bar-icons">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 10a6 6 0 0 0-12 0v11h12V10z"/><path d="M9 2v1"/><path d="M15 2v1"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z"/><path d="M5 10h14"/><path d="M9 16h6"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 4h14a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1z"/><path d="M4 8h16"/><path d="M8 4v16"/></svg>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold">自动截屏</h1>
                <div class="toggle">
                    <input type="checkbox" checked>
                    <span class="toggle-slider"></span>
                </div>
            </div>

            <!-- 状态卡片 -->
            <div class="card">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold">当前状态</h2>
                    <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">正在运行</span>
                </div>

                <div class="flex items-center mb-3">
                    <div class="status-indicator green"></div>
                    <span>后台服务正常</span>
                </div>

                <div class="flex items-center mb-3">
                    <div class="status-indicator green"></div>
                    <span>截屏功能已开启</span>
                </div>

                <div class="flex items-center mb-3">
                    <div class="status-indicator green"></div>
                    <span>所有权限已授予</span>
                </div>

                <div class="mt-4">
                    <div class="flex justify-between text-sm mb-1">
                        <span>今日已截图</span>
                        <span>32 / 144</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-bar-fill" style="width: 22%"></div>
                    </div>
                </div>
            </div>

            <!-- 今日统计 -->
            <div class="card">
                <h2 class="text-lg font-semibold mb-4">24小时状态</h2>

                <div class="space-y-4">
                    <!-- 后台未运行时长 -->
                    <div>
                        <div class="flex justify-between items-center mb-2">
                            <div class="flex items-center">
                                <div class="status-indicator red mr-2"></div>
                                <span>后台未运行时长</span>
                            </div>
                            <span class="text-red-600 font-medium">48分钟</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-bar-fill bg-red-500" style="width: 3.3%"></div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1 text-right">占比: 3.3%</div>
                    </div>

                    <!-- 截图功能未开启时长 -->
                    <div>
                        <div class="flex justify-between items-center mb-2">
                            <div class="flex items-center">
                                <div class="status-indicator yellow mr-2"></div>
                                <span>截图功能未开启时长</span>
                            </div>
                            <span class="text-yellow-600 font-medium">1小时12分钟</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-bar-fill bg-yellow-500" style="width: 5%"></div>
                        </div>
                        <div class="text-xs text-gray-500 mt-1 text-right">占比: 5%</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航栏 -->
        <div class="tab-bar">
            <div class="tab-item active">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9 22 9 12 15 12 15 22"/></svg>
                <span>首页</span>
            </div>
            <div class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><circle cx="9" cy="9" r="2"/><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/></svg>
                <span>截图</span>
            </div>
            <div class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><path d="M3 15h18"/><path d="M9 9h.01"/><path d="M15 9h.01"/></svg>
                <span>拼图</span>
            </div>
            <div class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 20h9"/><path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"/></svg>
                <span>状态</span>
            </div>
            <div class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/></svg>
                <span>设置</span>
            </div>
        </div>
    </div>
</body>
</html>
