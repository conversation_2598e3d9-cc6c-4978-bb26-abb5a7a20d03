package com.shuyi.discipline.data.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import com.shuyi.discipline.data.repository.RecordType
import com.shuyi.discipline.domain.monitor.ExitReason

/**
 * 运行时记录实体
 */
@Entity(
    tableName = "runtime_records",
    indices = [
        Index(value = ["timestamp"]),
        Index(value = ["type"]),
        Index(value = ["session_id"])
    ]
)
data class RuntimeRecordEntity(
    @PrimaryKey
    @ColumnInfo(name = "id")
    val id: String,

    @ColumnInfo(name = "timestamp")
    val timestamp: Long,

    @ColumnInfo(name = "type")
    val type: RecordType,

    @ColumnInfo(name = "session_id")
    val sessionId: String?,

    @ColumnInfo(name = "exit_reason")
    val exitReason: ExitReason?,

    @ColumnInfo(name = "duration")
    val duration: Long,

    @ColumnInfo(name = "description")
    val description: String,

    @ColumnInfo(name = "end_time")
    val endTime: Long? = null,

    @ColumnInfo(name = "created_at")
    val createdAt: Long = System.currentTimeMillis()
)
