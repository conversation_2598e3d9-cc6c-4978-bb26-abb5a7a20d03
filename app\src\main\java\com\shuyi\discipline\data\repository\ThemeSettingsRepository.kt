package com.shuyi.discipline.data.repository

import com.shuyi.discipline.data.model.ThemeSettings
import kotlinx.coroutines.flow.Flow

/**
 * 主题设置仓库接口
 */
interface ThemeSettingsRepository {
    
    /**
     * 获取主题设置
     */
    fun getThemeSettings(): Flow<ThemeSettings>
    
    /**
     * 同步获取主题设置
     */
    suspend fun getThemeSettingsSync(): ThemeSettings
    
    /**
     * 保存主题设置
     */
    suspend fun saveThemeSettings(themeSettings: ThemeSettings)
}
