package com.shuyi.discipline.ui.navigation

/**
 * 应用导航路由定义
 */
sealed class Screen(val route: String) {
    /**
     * 首页
     */
    object Home : Screen("home")

    /**
     * 配置页
     */
    object Config : Screen("config")

    /**
     * 免打扰时段页
     */
    object QuietPeriod : Screen("quiet_period")

    /**
     * 拼图列表页
     */
    object Collage : Screen("collage")

    /**
     * 拼图详情页
     */
    object CollageDetail : Screen("collage_detail/{date}") {
        /**
         * 创建带参数的路由
         */
        fun createRoute(date: String): String {
            return "collage_detail/$date"
        }
    }

    /**
     * 截图页面
     */
    object Screenshots : Screen("screenshots")

    /**
     * 拼图预览页面
     */
    object CollagePreview : Screen("collage_preview/{date}/{index}") {
        /**
         * 创建带参数的路由
         */
        fun createRoute(date: String, index: Int = 0): String {
            return "collage_preview/$date/$index"
        }
    }

    /**
     * 系统状态页面
     */
    object Status : Screen("status")

    /**
     * 权限加固页面
     */
    object Permissions : Screen("permissions")

    /**
     * 字体大小设置页面
     */
    object FontSize : Screen("font_size")

    /**
     * 外观设置页面
     */
    object AppearanceSettings : Screen("appearance_settings")

    /**
     * 截图设置页面
     */
    object ScreenshotSettings : Screen("screenshot_settings")

    /**
     * 拼图设置页面
     */
    object CollageSettings : Screen("collage_settings")

    /**
     * 通知设置页面
     */
    object NotificationSettings : Screen("notification_settings")

    /**
     * 关于页面
     */
    object About : Screen("about")
}