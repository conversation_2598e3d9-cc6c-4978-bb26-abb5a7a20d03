package com.shuyi.discipline.data.repository.impl

import android.content.Context
import android.graphics.Bitmap
import androidx.core.content.ContextCompat
import com.shuyi.discipline.data.model.Screenshot
import com.shuyi.discipline.data.repository.ScreenshotRepository
import com.shuyi.discipline.data.source.database.ScreenshotDao
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.UUID
import javax.inject.Inject

/**
 * 截图仓库实现类
 */
class ScreenshotRepositoryImpl @Inject constructor(
    private val context: Context,
    private val screenshotDao: ScreenshotDao
) : ScreenshotRepository {

    private val screenshotDir: File by lazy {
        // 直接使用内部存储，避免外部存储问题
        val baseDir = context.filesDir
        File(baseDir, "screenshots").apply {
            if (!exists()) {
                val created = mkdirs()
                Timber.d("创建截图目录结果: $created")
            }
            Timber.d("截图目录路径: ${absolutePath}")
        }
    }

    override suspend fun saveScreenshot(screenshot: Screenshot) {
        screenshotDao.insertScreenshot(screenshot)
    }

    override fun getAllScreenshots(): Flow<List<Screenshot>> {
        return screenshotDao.getAllScreenshots()
    }

    override fun getUnprocessedScreenshots(): Flow<List<Screenshot>> {
        return screenshotDao.getUnprocessedScreenshots()
    }

    override suspend fun getScreenshotsForDay(date: String): List<Screenshot> {
        try {
            // 解析日期字符串，格式为 yyyy-MM-dd
            val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            val parsedDate = dateFormat.parse(date) ?: return emptyList()

            // 计算当天开始和结束的时间戳
            val calendar = Calendar.getInstance()
            calendar.time = parsedDate
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfDay = calendar.timeInMillis

            calendar.set(Calendar.HOUR_OF_DAY, 23)
            calendar.set(Calendar.MINUTE, 59)
            calendar.set(Calendar.SECOND, 59)
            calendar.set(Calendar.MILLISECOND, 999)
            val endOfDay = calendar.timeInMillis

            Timber.d("查询日期 ${date} 的截图，时间范围: ${Date(startOfDay)} 到 ${Date(endOfDay)}")

            val screenshots = screenshotDao.getScreenshotsForDay(startOfDay, endOfDay)
            Timber.d("从数据库中获取到 ${screenshots.size} 张截图")

            return screenshots
        } catch (e: Exception) {
            Timber.e(e, "获取日期 ${date} 的截图失败")
            return emptyList()
        }
    }

    override suspend fun markScreenshotsAsProcessed(screenshots: List<Screenshot>) {
        val ids = screenshots.map { it.id }
        screenshotDao.markAsProcessed(ids)
    }

    override suspend fun deleteOldScreenshots(daysToKeep: Int) {
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_YEAR, -daysToKeep)
        val cutoffTime = calendar.timeInMillis

        screenshotDao.deleteScreenshotsOlderThan(cutoffTime)
    }

    override suspend fun getReviewedScreenshots(): Flow<List<Screenshot>> {
        // 此方法在当前DAO中可能没有实现，返回空流作为临时解决方案
        Timber.w("getReviewedScreenshots方法在当前DAO中未实现")
        return emptyFlow()
    }

    override suspend fun getUnreviewedScreenshots(): Flow<List<Screenshot>> {
        // 此方法在当前DAO中可能没有实现，返回空流作为临时解决方案
        Timber.w("getUnreviewedScreenshots方法在当前DAO中未实现")
        return emptyFlow()
    }

    override suspend fun getScreenshotsByPackageName(packageName: String): Flow<List<Screenshot>> {
        // 此方法在当前DAO中可能没有实现，返回空流作为临时解决方案
        Timber.w("getScreenshotsByPackageName方法在当前DAO中未实现")
        return emptyFlow()
    }

    override suspend fun getScreenshotsByCategory(category: String): Flow<List<Screenshot>> {
        // 此方法在当前DAO中可能没有实现，返回空流作为临时解决方案
        Timber.w("getScreenshotsByCategory方法在当前DAO中未实现")
        return emptyFlow()
    }

    override suspend fun getScreenshotsByDate(startDate: Date, endDate: Date): Flow<List<Screenshot>> {
        // 将Date转换为时间戳
        val startTimestamp = startDate.time
        val endTimestamp = endDate.time

        // 假设DAO中有一个方法可以根据时间戳范围查询
        // 如果没有，可能需要添加这个方法或使用其他方式实现
        Timber.w("getScreenshotsByDate方法在当前DAO中未实现")
        return emptyFlow()
    }

    override suspend fun deleteScreenshotById(id: String): Int {
        // 假设DAO中有一个方法可以根据ID删除截图
        // 如果删除成功返回1，否则返回0
        try {
            // 这里需要实现实际的删除逻辑
            Timber.w("deleteScreenshotById方法在当前DAO中未实现")
            return 0
        } catch (e: Exception) {
            Timber.e(e, "删除截图失败: id=$id")
            return 0
        }
    }

    override suspend fun deleteScreenshotByPath(filePath: String): Int {
        return try {
            val deletedCount = screenshotDao.deleteScreenshotByPath(filePath)
            Timber.d("根据路径删除截图记录: $filePath, 删除数量: $deletedCount")
            deletedCount
        } catch (e: Exception) {
            Timber.e(e, "根据路径删除截图失败: filePath=$filePath")
            0
        }
    }

    override suspend fun saveBitmapToFile(bitmap: Bitmap, quality: Int, resolution: Int, blur: Int): File = withContext(Dispatchers.IO) {
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val fileName = "screenshot_${timeStamp}_${UUID.randomUUID().toString().substring(0, 8)}.jpg"
        val file = File(screenshotDir, fileName)

        try {
            var processedBitmap = bitmap

            // 1. 分辨率缩放处理
            if (resolution < 100) {
                val scaleFactor = resolution / 100f
                val newWidth = (bitmap.width * scaleFactor).toInt().coerceAtLeast(10)
                val newHeight = (bitmap.height * scaleFactor).toInt().coerceAtLeast(10)

                Timber.d("分辨率缩放：原始尺寸 ${bitmap.width}x${bitmap.height}，缩放到 ${newWidth}x${newHeight} (${resolution}%)")
                val scaledBitmap = Bitmap.createScaledBitmap(bitmap, newWidth, newHeight, true)

                // 如果之前有处理过的bitmap且不是原始bitmap，需要回收
                if (processedBitmap != bitmap) {
                    processedBitmap.recycle()
                }
                processedBitmap = scaledBitmap
            }

            // 2. 高斯模糊处理
            if (blur > 0) {
                val blurredBitmap = applyGaussianBlur(processedBitmap, blur)

                // 如果之前有处理过的bitmap且不是原始bitmap，需要回收
                if (processedBitmap != bitmap) {
                    processedBitmap.recycle()
                }
                processedBitmap = blurredBitmap
            }

            // 3. 保存最终处理后的bitmap
            FileOutputStream(file).use { out ->
                processedBitmap.compress(Bitmap.CompressFormat.JPEG, quality, out)
            }

            // 清理资源：如果创建了处理后的bitmap，需要回收它
            if (processedBitmap != bitmap) {
                processedBitmap.recycle()
            }

            Timber.d("截图保存成功: ${file.absolutePath}")
            file
        } catch (e: Exception) {
            Timber.e(e, "截图保存失败")
            throw e
        }
    }

    /**
     * 应用高斯模糊效果（优化版本）
     * @param bitmap 原始bitmap
     * @param blurPercent 模糊百分比（0-100）
     * @return 模糊后的bitmap
     */
    private fun applyGaussianBlur(bitmap: Bitmap, blurPercent: Int): Bitmap {
        return try {
            // 将百分比转换为模糊半径（0-15f，降低最大值以提高性能）
            val blurRadius = (blurPercent / 100f * 15f).coerceIn(0.1f, 15f)

            Timber.d("开始应用高斯模糊，半径: $blurRadius, 图片尺寸: ${bitmap.width}x${bitmap.height}")
            val startTime = System.currentTimeMillis()

            // 使用优化的模糊算法
            val result = applyOptimizedBlur(bitmap, blurRadius)

            val endTime = System.currentTimeMillis()
            Timber.d("高斯模糊完成，耗时: ${endTime - startTime}ms")

            result
        } catch (e: Exception) {
            Timber.e(e, "应用高斯模糊失败")
            // 如果模糊失败，返回原始bitmap的副本
            bitmap.copy(bitmap.config ?: Bitmap.Config.ARGB_8888, false)
        }
    }

    /**
     * 优化的模糊算法实现
     * 使用分离式高斯模糊和尺寸优化来提高性能
     */
    private fun applyOptimizedBlur(bitmap: Bitmap, radius: Float): Bitmap {
        val radiusInt = radius.toInt().coerceAtLeast(1)

        // 如果半径太小，直接返回原图副本
        if (radiusInt <= 1) {
            return bitmap.copy(bitmap.config ?: Bitmap.Config.ARGB_8888, false)
        }

        // 性能优化：对于大图片，先缩小再模糊再放大
        val originalWidth = bitmap.width
        val originalHeight = bitmap.height
        val maxDimension = 800 // 截图处理的最大尺寸（比拼图更小）

        val shouldScale = originalWidth > maxDimension || originalHeight > maxDimension
        val scaledBitmap = if (shouldScale) {
            val scale = minOf(maxDimension.toFloat() / originalWidth, maxDimension.toFloat() / originalHeight)
            val scaledWidth = (originalWidth * scale).toInt()
            val scaledHeight = (originalHeight * scale).toInt()
            val scaledRadius = radius * scale

            Timber.d("缩放截图进行模糊处理: ${originalWidth}x${originalHeight} -> ${scaledWidth}x${scaledHeight}, 缩放半径: $scaledRadius")

            val scaled = Bitmap.createScaledBitmap(bitmap, scaledWidth, scaledHeight, true)

            // 根据图片大小和模糊强度选择算法
            val blurred = if (scaledWidth * scaledHeight > 300000 || scaledRadius > 8) {
                Timber.d("使用快速模糊算法（截图）")
                applyFastBlur(scaled, scaledRadius)
            } else {
                Timber.d("使用高质量分离式模糊算法（截图）")
                applySeparableBlur(scaled, scaledRadius)
            }

            scaled.recycle() // 释放缩放后的临时bitmap

            // 将模糊结果放大回原始尺寸
            val result = Bitmap.createScaledBitmap(blurred, originalWidth, originalHeight, true)
            blurred.recycle() // 释放模糊后的临时bitmap
            result
        } else {
            // 直接对原图进行模糊，根据图片大小选择算法
            if (originalWidth * originalHeight > 800000 || radius > 8) {
                Timber.d("使用快速模糊算法（截图原图）")
                applyFastBlur(bitmap, radius)
            } else {
                Timber.d("使用高质量分离式模糊算法（截图原图）")
                applySeparableBlur(bitmap, radius)
            }
        }

        return scaledBitmap
    }

    /**
     * 分离式高斯模糊实现
     * 先水平模糊，再垂直模糊，复杂度从O(r²)降低到O(r)
     */
    private fun applySeparableBlur(bitmap: Bitmap, radius: Float): Bitmap {
        val radiusInt = radius.toInt().coerceAtLeast(1)
        val width = bitmap.width
        val height = bitmap.height

        // 创建高斯权重数组
        val weights = createGaussianWeights(radiusInt)

        // 获取原始像素
        val pixels = IntArray(width * height)
        bitmap.getPixels(pixels, 0, width, 0, 0, width, height)

        // 第一步：水平模糊
        val horizontalBlurred = IntArray(width * height)
        for (y in 0 until height) {
            for (x in 0 until width) {
                var red = 0f
                var green = 0f
                var blue = 0f
                var alpha = 0f
                var totalWeight = 0f

                for (i in -radiusInt..radiusInt) {
                    val nx = (x + i).coerceIn(0, width - 1)
                    val pixel = pixels[y * width + nx]
                    val weight = weights[i + radiusInt]

                    alpha += ((pixel shr 24) and 0xFF) * weight
                    red += ((pixel shr 16) and 0xFF) * weight
                    green += ((pixel shr 8) and 0xFF) * weight
                    blue += (pixel and 0xFF) * weight
                    totalWeight += weight
                }

                // 归一化
                alpha /= totalWeight
                red /= totalWeight
                green /= totalWeight
                blue /= totalWeight

                horizontalBlurred[y * width + x] =
                    (alpha.toInt() shl 24) or (red.toInt() shl 16) or (green.toInt() shl 8) or blue.toInt()
            }
        }

        // 第二步：垂直模糊
        val verticalBlurred = IntArray(width * height)
        for (x in 0 until width) {
            for (y in 0 until height) {
                var red = 0f
                var green = 0f
                var blue = 0f
                var alpha = 0f
                var totalWeight = 0f

                for (i in -radiusInt..radiusInt) {
                    val ny = (y + i).coerceIn(0, height - 1)
                    val pixel = horizontalBlurred[ny * width + x]
                    val weight = weights[i + radiusInt]

                    alpha += ((pixel shr 24) and 0xFF) * weight
                    red += ((pixel shr 16) and 0xFF) * weight
                    green += ((pixel shr 8) and 0xFF) * weight
                    blue += (pixel and 0xFF) * weight
                    totalWeight += weight
                }

                // 归一化
                alpha /= totalWeight
                red /= totalWeight
                green /= totalWeight
                blue /= totalWeight

                verticalBlurred[y * width + x] =
                    (alpha.toInt() shl 24) or (red.toInt() shl 16) or (green.toInt() shl 8) or blue.toInt()
            }
        }

        // 创建结果bitmap
        val result = Bitmap.createBitmap(width, height, bitmap.config ?: Bitmap.Config.ARGB_8888)
        result.setPixels(verticalBlurred, 0, width, 0, 0, width, height)
        return result
    }

    /**
     * 创建高斯权重数组
     */
    private fun createGaussianWeights(radius: Int): FloatArray {
        val size = radius * 2 + 1
        val weights = FloatArray(size)
        val sigma = radius / 3.0f // 标准差
        val twoSigmaSquared = 2 * sigma * sigma

        for (i in 0 until size) {
            val x = i - radius
            weights[i] = kotlin.math.exp(-(x * x) / twoSigmaSquared)
        }

        return weights
    }

    /**
     * 快速模糊算法（用于截图的极大图片或高模糊值的情况）
     * 使用简化的盒式滤波器，性能更好但质量稍低
     */
    private fun applyFastBlur(bitmap: Bitmap, radius: Float): Bitmap {
        val radiusInt = radius.toInt().coerceAtLeast(1)
        val width = bitmap.width
        val height = bitmap.height

        val pixels = IntArray(width * height)
        bitmap.getPixels(pixels, 0, width, 0, 0, width, height)

        // 简化的盒式滤波器，只进行一次水平和垂直模糊
        val blurred = IntArray(width * height)

        // 水平模糊
        for (y in 0 until height) {
            for (x in 0 until width) {
                var r = 0
                var g = 0
                var b = 0
                var a = 0
                var count = 0

                val start = maxOf(0, x - radiusInt)
                val end = minOf(width - 1, x + radiusInt)

                for (i in start..end) {
                    val pixel = pixels[y * width + i]
                    a += (pixel shr 24) and 0xFF
                    r += (pixel shr 16) and 0xFF
                    g += (pixel shr 8) and 0xFF
                    b += pixel and 0xFF
                    count++
                }

                blurred[y * width + x] = ((a / count) shl 24) or
                                        ((r / count) shl 16) or
                                        ((g / count) shl 8) or
                                        (b / count)
            }
        }

        // 垂直模糊
        val result = IntArray(width * height)
        for (x in 0 until width) {
            for (y in 0 until height) {
                var r = 0
                var g = 0
                var b = 0
                var a = 0
                var count = 0

                val start = maxOf(0, y - radiusInt)
                val end = minOf(height - 1, y + radiusInt)

                for (i in start..end) {
                    val pixel = blurred[i * width + x]
                    a += (pixel shr 24) and 0xFF
                    r += (pixel shr 16) and 0xFF
                    g += (pixel shr 8) and 0xFF
                    b += pixel and 0xFF
                    count++
                }

                result[y * width + x] = ((a / count) shl 24) or
                                       ((r / count) shl 16) or
                                       ((g / count) shl 8) or
                                       (b / count)
            }
        }

        val resultBitmap = Bitmap.createBitmap(width, height, bitmap.config ?: Bitmap.Config.ARGB_8888)
        resultBitmap.setPixels(result, 0, width, 0, 0, width, height)
        return resultBitmap
    }

    override fun observeScreenshotsForDay(date: String): Flow<List<Screenshot>> {
        return try {
            // 解析日期字符串，格式为 yyyy-MM-dd
            val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
            val parsedDate = dateFormat.parse(date) ?: return flowOf(emptyList())

            // 计算当天开始和结束的时间戳
            val calendar = Calendar.getInstance()
            calendar.time = parsedDate
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfDay = calendar.timeInMillis

            calendar.set(Calendar.HOUR_OF_DAY, 23)
            calendar.set(Calendar.MINUTE, 59)
            calendar.set(Calendar.SECOND, 59)
            calendar.set(Calendar.MILLISECOND, 999)
            val endOfDay = calendar.timeInMillis

            Timber.d("监听日期 ${date} 的截图变化，时间范围: ${Date(startOfDay)} 到 ${Date(endOfDay)}")

            screenshotDao.observeScreenshotsForDay(startOfDay, endOfDay)
        } catch (e: Exception) {
            Timber.e(e, "监听日期 ${date} 的截图失败")
            flowOf(emptyList())
        }
    }
}