package com.shuyi.discipline.domain.monitor

import android.content.Context
import android.content.Intent
import android.media.projection.MediaProjection
import com.shuyi.discipline.data.model.MonitorStatus
import com.shuyi.discipline.data.model.MonitorType
import com.shuyi.discipline.data.repository.ScheduleRepository
import com.shuyi.discipline.data.repository.SystemMonitorRepository
import com.shuyi.discipline.domain.service.ScreenshotService
import com.shuyi.discipline.utils.MediaProjectionUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * MediaProjection权限监控管理器
 * 监听屏幕录制权限的变化，当权限被撤销时自动更新服务状态和记录时长统计
 */
@Singleton
class MediaProjectionPermissionMonitor @Inject constructor(
    private val context: Context,
    private val scheduleRepository: ScheduleRepository,
    private val systemMonitorRepository: SystemMonitorRepository,
    private val applicationScope: CoroutineScope
) {

    // MediaProjection回调，用于监听权限状态变化
    private val mediaProjectionCallback = object : MediaProjection.Callback() {
        override fun onStop() {
            super.onStop()
            Timber.w("🚨 MediaProjection权限被撤销，开始处理权限丢失")
            handlePermissionRevoked()
        }
    }

    // 当前监控的MediaProjection对象
    private var currentMediaProjection: MediaProjection? = null
    
    // 权限撤销时间记录
    private var permissionRevokedTime: Long = 0L
    
    // 异常记录ID，用于跟踪权限丢失的时长
    private var abnormalRecordId: String? = null

    /**
     * 开始监控MediaProjection权限
     * @param mediaProjection 要监控的MediaProjection对象
     */
    fun startMonitoring(mediaProjection: MediaProjection) {
        try {
            Timber.d("🔍 开始监控MediaProjection权限状态")
            
            // 停止之前的监控
            stopMonitoring()
            
            // 设置新的监控
            currentMediaProjection = mediaProjection
            mediaProjection.registerCallback(mediaProjectionCallback, null)

            // 保存权限状态
            MediaProjectionUtils.savePermissionStatus(context, true)

            // 如果之前有权限丢失记录，现在权限恢复了，结束异常记录
            abnormalRecordId?.let { recordId ->
                applicationScope.launch {
                    finishAbnormalRecord(recordId, "权限已恢复")
                }
                abnormalRecordId = null
            }

            Timber.d("✅ MediaProjection权限监控已启动")
        } catch (e: Exception) {
            Timber.e(e, "启动MediaProjection权限监控失败")
        }
    }

    /**
     * 停止监控MediaProjection权限
     */
    fun stopMonitoring() {
        try {
            currentMediaProjection?.let { projection ->
                projection.unregisterCallback(mediaProjectionCallback)
                Timber.d("🛑 已停止MediaProjection权限监控")
            }
            currentMediaProjection = null
        } catch (e: Exception) {
            Timber.e(e, "停止MediaProjection权限监控失败")
        }
    }

    /**
     * 处理权限被撤销的情况
     */
    private fun handlePermissionRevoked() {
        applicationScope.launch {
            try {
                permissionRevokedTime = System.currentTimeMillis()
                Timber.w("⚠️ 处理权限撤销，时间: ${java.util.Date(permissionRevokedTime)}")

                // 0. 保存权限状态
                MediaProjectionUtils.savePermissionStatus(context, false)

                // 1. 更新截图服务状态为停止
                updateScreenshotServiceStatus(false)

                // 2. 发送服务状态广播，通知UI更新
                sendServiceStatusBroadcast(isRunning = false, hasPermission = false)

                // 3. 发送清理MediaProjection的广播，通知服务清理无效的MediaProjection对象
                sendClearMediaProjectionBroadcast()

                // 4. 开始记录权限丢失的时长统计
                startAbnormalRecord()

                Timber.i("✅ 权限撤销处理完成")
            } catch (e: Exception) {
                Timber.e(e, "处理权限撤销失败")
            }
        }
    }

    /**
     * 更新截图服务状态
     */
    private suspend fun updateScreenshotServiceStatus(isEnabled: Boolean) {
        try {
            scheduleRepository.updateServiceStatus(isEnabled)
            Timber.d("📝 截图服务状态已更新为: $isEnabled")
        } catch (e: Exception) {
            Timber.e(e, "更新截图服务状态失败")
        }
    }

    /**
     * 发送服务状态广播
     */
    private fun sendServiceStatusBroadcast(isRunning: Boolean, hasPermission: Boolean) {
        try {
            val statusIntent = Intent(ScreenshotService.ACTION_SERVICE_STATUS).apply {
                putExtra("is_running", isRunning)
                putExtra("has_permission", hasPermission)
                putExtra("permission_revoked_by_user", true) // 标记是用户主动撤销权限
            }
            context.sendBroadcast(statusIntent)
            Timber.d("📡 已发送服务状态广播: 运行=$isRunning, 权限=$hasPermission")
        } catch (e: Exception) {
            Timber.e(e, "发送服务状态广播失败")
        }
    }

    /**
     * 发送清理MediaProjection的广播
     */
    private fun sendClearMediaProjectionBroadcast() {
        try {
            val clearIntent = Intent("com.shuyi.discipline.CLEAR_MEDIA_PROJECTION")
            context.sendBroadcast(clearIntent)
            Timber.d("📡 已发送清理MediaProjection的广播")
        } catch (e: Exception) {
            Timber.e(e, "发送清理MediaProjection广播失败")
        }
    }

    /**
     * 开始记录异常状态（权限丢失）
     */
    private suspend fun startAbnormalRecord() {
        try {
            abnormalRecordId = systemMonitorRepository.recordAbnormalStart(
                type = MonitorType.SCREENSHOT_FUNCTION,
                description = "屏幕录制权限被用户撤销",
                isDeviceShutdown = false
            )
            Timber.d("📊 开始记录权限丢失异常，记录ID: $abnormalRecordId")
        } catch (e: Exception) {
            Timber.e(e, "记录权限丢失异常失败")
        }
    }

    /**
     * 结束异常记录
     */
    private suspend fun finishAbnormalRecord(recordId: String, reason: String) {
        try {
            val currentTime = System.currentTimeMillis()
            val duration = if (permissionRevokedTime > 0) {
                currentTime - permissionRevokedTime
            } else {
                0L
            }

            systemMonitorRepository.finishRecord(
                id = recordId,
                endTime = currentTime,
                duration = duration,
                status = MonitorStatus.RECOVERED
            )

            Timber.d("📊 权限丢失异常记录已结束，记录ID: $recordId, 持续时长: ${duration}ms, 原因: $reason")
        } catch (e: Exception) {
            Timber.e(e, "结束权限丢失异常记录失败")
        }
    }

    /**
     * 检查当前权限状态
     * @return true表示有权限，false表示无权限
     */
    fun hasPermission(): Boolean {
        return currentMediaProjection != null
    }

    /**
     * 获取权限撤销时间
     */
    fun getPermissionRevokedTime(): Long {
        return permissionRevokedTime
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        stopMonitoring()
        permissionRevokedTime = 0L
        abnormalRecordId = null
    }
}
