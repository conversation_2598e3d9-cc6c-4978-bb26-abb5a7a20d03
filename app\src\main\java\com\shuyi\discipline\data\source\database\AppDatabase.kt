package com.shuyi.discipline.data.source.database

import android.content.Context
import androidx.room.AutoMigration
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.shuyi.discipline.data.database.converter.RuntimeTypeConverters
import com.shuyi.discipline.data.database.dao.RuntimeMetadataDao
import com.shuyi.discipline.data.database.dao.RuntimeRecordDao
import com.shuyi.discipline.data.database.dao.RuntimeSessionDao
import com.shuyi.discipline.data.database.entity.RuntimeMetadataEntity
import com.shuyi.discipline.data.database.entity.RuntimeRecordEntity
import com.shuyi.discipline.data.database.entity.RuntimeSessionEntity
import com.shuyi.discipline.data.model.CollageReport
import com.shuyi.discipline.data.model.CollageSettings
import com.shuyi.discipline.data.model.NotificationSettings
import com.shuyi.discipline.data.model.QuietPeriod
import com.shuyi.discipline.data.model.ScheduleRule
import com.shuyi.discipline.data.model.Screenshot
import com.shuyi.discipline.data.model.ScreenshotDeduplication
import com.shuyi.discipline.data.model.SystemMonitorRecord
import com.shuyi.discipline.data.model.ThemeSettings

/**
 * 应用数据库
 */
@Database(
    entities = [
        Screenshot::class,
        ScheduleRule::class,
        QuietPeriod::class,
        CollageReport::class,
        CollageSettings::class,
        NotificationSettings::class,
        ThemeSettings::class,
        SystemMonitorRecord::class,
        RuntimeRecordEntity::class,
        RuntimeSessionEntity::class,
        RuntimeMetadataEntity::class,
        ScreenshotDeduplication::class
    ],
    version = 14,
    exportSchema = false
)
@TypeConverters(Converters::class, RuntimeTypeConverters::class)
abstract class AppDatabase : RoomDatabase() {

    abstract fun screenshotDao(): ScreenshotDao
    abstract fun scheduleRuleDao(): ScheduleRuleDao
    abstract fun quietPeriodDao(): QuietPeriodDao
    abstract fun collageReportDao(): CollageReportDao
    abstract fun collageSettingsDao(): CollageSettingsDao
    abstract fun notificationSettingsDao(): NotificationSettingsDao
    abstract fun themeSettingsDao(): ThemeSettingsDao
    abstract fun systemMonitorDao(): SystemMonitorDao
    abstract fun runtimeRecordDao(): RuntimeRecordDao
    abstract fun runtimeSessionDao(): RuntimeSessionDao
    abstract fun runtimeMetadataDao(): RuntimeMetadataDao
    abstract fun screenshotDeduplicationDao(): ScreenshotDeduplicationDao

    companion object {
        private const val DATABASE_NAME = "screenguardian_db"

        /**
         * 从版本1到版本2的迁移
         * 添加了CollageSettings表
         */
        private val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 创建CollageSettings表
                database.execSQL(
                    """
                    CREATE TABLE IF NOT EXISTS `collage_settings` (
                        `id` INTEGER NOT NULL,
                        `isAutoCollageEnabled` INTEGER NOT NULL,
                        `collageHour` INTEGER NOT NULL,
                        `collageMinute` INTEGER NOT NULL,
                        `collageLayout` TEXT NOT NULL,
                        `collageQuality` INTEGER NOT NULL,
                        `lastCollageTime` INTEGER NOT NULL,
                        PRIMARY KEY(`id`)
                    )
                    """
                )
            }
        }

        /**
         * 从版本2到版本3的迁移
         * 添加了NotificationSettings表
         */
        private val MIGRATION_2_3 = object : Migration(2, 3) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 创建NotificationSettings表
                database.execSQL(
                    """
                    CREATE TABLE IF NOT EXISTS `notification_settings` (
                        `id` INTEGER NOT NULL,
                        `isScreenshotNotificationEnabled` INTEGER NOT NULL,
                        `isCollageNotificationEnabled` INTEGER NOT NULL,
                        PRIMARY KEY(`id`)
                    )
                    """
                )
            }
        }

        /**
         * 从版本3到版本4的迁移
         * 添加了CollageReport表的collagePaths字段
         */
        private val MIGRATION_3_4 = object : Migration(3, 4) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 添加collagePaths字段
                database.execSQL("ALTER TABLE collage_reports ADD COLUMN collagePaths TEXT NOT NULL DEFAULT '[]'")
            }
        }

        /**
         * 从版本4到版本5的迁移
         * 添加了ThemeSettings表
         */
        private val MIGRATION_4_5 = object : Migration(4, 5) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 创建ThemeSettings表
                database.execSQL(
                    """
                    CREATE TABLE IF NOT EXISTS `theme_settings` (
                        `id` INTEGER NOT NULL,
                        `themeMode` TEXT NOT NULL,
                        `isDynamicColorEnabled` INTEGER NOT NULL,
                        `createdAt` INTEGER NOT NULL,
                        `updatedAt` INTEGER NOT NULL,
                        PRIMARY KEY(`id`)
                    )
                    """
                )
            }
        }

        /**
         * 从版本5到版本6的迁移
         * 添加了SystemMonitorRecord表
         */
        private val MIGRATION_5_6 = object : Migration(5, 6) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 创建SystemMonitorRecord表
                database.execSQL(
                    """
                    CREATE TABLE IF NOT EXISTS `system_monitor_records` (
                        `id` TEXT NOT NULL,
                        `timestamp` INTEGER NOT NULL,
                        `monitorType` TEXT NOT NULL,
                        `status` TEXT NOT NULL,
                        `startTime` INTEGER NOT NULL,
                        `endTime` INTEGER,
                        `duration` INTEGER NOT NULL,
                        `description` TEXT NOT NULL,
                        `isDeviceShutdown` INTEGER NOT NULL,
                        PRIMARY KEY(`id`)
                    )
                    """
                )
            }
        }

        /**
         * 从版本6到版本7的迁移
         * 添加了ThemeSettings表的fontSize字段
         */
        private val MIGRATION_6_7 = object : Migration(6, 7) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 添加fontSize字段，默认值为STANDARD(1)
                database.execSQL("ALTER TABLE theme_settings ADD COLUMN fontSize TEXT NOT NULL DEFAULT 'STANDARD'")
            }
        }

        /**
         * 从版本7到版本8的迁移
         * 添加了运行时监控相关表
         */
        private val MIGRATION_7_8 = object : Migration(7, 8) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 创建运行时记录表
                database.execSQL(
                    """
                    CREATE TABLE IF NOT EXISTS `runtime_records` (
                        `id` TEXT NOT NULL,
                        `timestamp` INTEGER NOT NULL,
                        `type` TEXT NOT NULL,
                        `session_id` TEXT,
                        `exit_reason` TEXT,
                        `duration` INTEGER NOT NULL,
                        `description` TEXT NOT NULL,
                        `created_at` INTEGER NOT NULL,
                        PRIMARY KEY(`id`)
                    )
                    """
                )

                // 创建运行时会话表
                database.execSQL(
                    """
                    CREATE TABLE IF NOT EXISTS `runtime_sessions` (
                        `session_id` TEXT NOT NULL,
                        `start_time` INTEGER NOT NULL,
                        `end_time` INTEGER,
                        `status` TEXT NOT NULL,
                        `exit_reason` TEXT,
                        `total_duration` INTEGER NOT NULL,
                        `foreground_duration` INTEGER NOT NULL,
                        `background_duration` INTEGER NOT NULL,
                        `created_at` INTEGER NOT NULL,
                        `updated_at` INTEGER NOT NULL,
                        PRIMARY KEY(`session_id`)
                    )
                    """
                )

                // 创建运行时元数据表
                database.execSQL(
                    """
                    CREATE TABLE IF NOT EXISTS `runtime_metadata` (
                        `key` TEXT NOT NULL,
                        `value` TEXT NOT NULL,
                        `updated_at` INTEGER NOT NULL,
                        PRIMARY KEY(`key`)
                    )
                    """
                )

                // 创建索引以提高查询性能
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_runtime_records_timestamp` ON `runtime_records` (`timestamp`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_runtime_records_type` ON `runtime_records` (`type`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_runtime_records_session_id` ON `runtime_records` (`session_id`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_runtime_sessions_start_time` ON `runtime_sessions` (`start_time`)")
                database.execSQL("CREATE INDEX IF NOT EXISTS `index_runtime_sessions_status` ON `runtime_sessions` (`status`)")
            }
        }

        /**
         * 从版本8到版本9的迁移
         * 为runtime_records表添加end_time字段
         */
        private val MIGRATION_8_9 = object : Migration(8, 9) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 为runtime_records表添加end_time字段
                database.execSQL("ALTER TABLE runtime_records ADD COLUMN end_time INTEGER")
            }
        }

        /**
         * 从版本9到版本10的迁移
         * 为schedule_rules表添加intervalSeconds字段
         */
        private val MIGRATION_9_10 = object : Migration(9, 10) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 为schedule_rules表添加intervalSeconds字段，默认值为0
                database.execSQL("ALTER TABLE schedule_rules ADD COLUMN intervalSeconds INTEGER NOT NULL DEFAULT 0")
            }
        }

        /**
         * 从版本10到版本11的迁移
         * 为schedule_rules表添加randomRangeMinutes和randomRangeSeconds字段
         */
        private val MIGRATION_10_11 = object : Migration(10, 11) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 为schedule_rules表添加随机范围分钟和秒数字段，默认值为0
                database.execSQL("ALTER TABLE schedule_rules ADD COLUMN randomRangeMinutes INTEGER NOT NULL DEFAULT 0")
                database.execSQL("ALTER TABLE schedule_rules ADD COLUMN randomRangeSeconds INTEGER NOT NULL DEFAULT 0")
            }
        }

        /**
         * 从版本11到版本12的迁移
         * 为schedule_rules表添加resolutionSetting和blurSetting字段
         */
        private val MIGRATION_11_12 = object : Migration(11, 12) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 为schedule_rules表添加resolutionSetting字段，默认值为100（100%分辨率）
                database.execSQL("ALTER TABLE schedule_rules ADD COLUMN resolutionSetting INTEGER NOT NULL DEFAULT 100")
                // 为schedule_rules表添加blurSetting字段，默认值为0（无模糊）
                database.execSQL("ALTER TABLE schedule_rules ADD COLUMN blurSetting INTEGER NOT NULL DEFAULT 0")
            }
        }

        /**
         * 从版本12到版本13的迁移
         * 为collage_settings表添加collageResolution和collageBlur字段
         */
        private val MIGRATION_12_13 = object : Migration(12, 13) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 为collage_settings表添加collageResolution字段，默认值为100（100%分辨率）
                database.execSQL("ALTER TABLE collage_settings ADD COLUMN collageResolution INTEGER NOT NULL DEFAULT 100")
                // 为collage_settings表添加collageBlur字段，默认值为0（无模糊）
                database.execSQL("ALTER TABLE collage_settings ADD COLUMN collageBlur INTEGER NOT NULL DEFAULT 0")
            }
        }

        /**
         * 从版本13到版本14的迁移
         * 添加截图去重表
         */
        private val MIGRATION_13_14 = object : Migration(13, 14) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 创建截图去重表
                database.execSQL(
                    """
                    CREATE TABLE IF NOT EXISTS `screenshot_deduplication` (
                        `id` INTEGER NOT NULL,
                        `lastScreenshotHash` INTEGER NOT NULL,
                        `lastScreenshotPath` TEXT NOT NULL,
                        `isEnabled` INTEGER NOT NULL,
                        `sensitivityLevel` INTEGER NOT NULL,
                        `duplicateCount` INTEGER NOT NULL,
                        `spaceSavedMb` REAL NOT NULL,
                        `lastUpdatedTime` INTEGER NOT NULL,
                        `totalProcessedCount` INTEGER NOT NULL,
                        PRIMARY KEY(`id`)
                    )
                    """
                )

                // 插入默认设置
                database.execSQL(
                    """
                    INSERT INTO screenshot_deduplication
                    (id, lastScreenshotHash, lastScreenshotPath, isEnabled, sensitivityLevel, duplicateCount, spaceSavedMb, lastUpdatedTime, totalProcessedCount)
                    VALUES (1, 0, '', 1, 3, 0, 0.0, ${System.currentTimeMillis()}, 0)
                    """
                )
            }
        }

        @Volatile
        private var INSTANCE: AppDatabase? = null

        fun getInstance(context: Context): AppDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    AppDatabase::class.java,
                    DATABASE_NAME
                )
                    .addMigrations(MIGRATION_1_2, MIGRATION_2_3, MIGRATION_3_4, MIGRATION_4_5, MIGRATION_5_6, MIGRATION_6_7, MIGRATION_7_8, MIGRATION_8_9, MIGRATION_9_10, MIGRATION_10_11, MIGRATION_11_12, MIGRATION_12_13, MIGRATION_13_14)
                    .fallbackToDestructiveMigration()
                    .build()
                INSTANCE = instance
                instance
            }
        }
    }
}