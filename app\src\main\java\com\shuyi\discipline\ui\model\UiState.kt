package com.shuyi.discipline.ui.model

/**
 * 通用UI状态密封类，用于表示数据加载的各种状态
 */
sealed class UiState<out T> {
    /**
     * 加载中状态
     */
    object Loading : UiState<Nothing>()
    
    /**
     * 成功状态，包含数据
     */
    data class Success<T>(val data: T) : UiState<T>()
    
    /**
     * 错误状态，包含错误信息
     */
    data class Error(val message: String) : UiState<Nothing>()
    
    /**
     * 空数据状态
     */
    object Empty : UiState<Nothing>()
    
    /**
     * 初始/空闲状态
     */
    object Idle : UiState<Nothing>()
} 