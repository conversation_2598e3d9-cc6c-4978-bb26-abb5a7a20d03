package com.shuyi.discipline.ui.screen.config

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.shuyi.discipline.data.model.CollageLayout
import com.shuyi.discipline.data.model.CollageQuality
import com.shuyi.discipline.data.model.CollageSettings
import com.shuyi.discipline.data.repository.CollageSettingsRepository
import com.shuyi.discipline.data.repository.impl.CollageSettingsRepositoryImpl
import com.shuyi.discipline.data.source.database.AppDatabase
import com.shuyi.discipline.domain.usecase.ScheduleDailyCollageUseCase
import com.shuyi.discipline.ui.model.UiState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import timber.log.Timber
import java.text.SimpleDateFormat
import java.util.Locale

/**
 * 拼图设置ViewModel
 */
class CollageSettingsViewModel(
    private val application: Application,
    private val collageSettingsRepository: CollageSettingsRepository,
    private val scheduleDailyCollageUseCase: ScheduleDailyCollageUseCase
) : AndroidViewModel(application) {
    
    // 拼图设置状态
    private val _collageSettings = MutableStateFlow<UiState<CollageSettings>>(UiState.Loading)
    val collageSettings: StateFlow<UiState<CollageSettings>> = _collageSettings
    
    // 拼图布局选项
    val layoutOptions = CollageLayout.values().map { it.displayName }
    
    // 拼图质量选项
    val qualityOptions = CollageQuality.values().map { it.displayName }
    
    // 初始化
    init {
        loadCollageSettings()
    }
    
    /**
     * 加载拼图设置
     */
    private fun loadCollageSettings() {
        viewModelScope.launch {
            _collageSettings.value = UiState.Loading
            
            collageSettingsRepository.getCollageSettings()
                .catch { e ->
                    Timber.e(e, "加载拼图设置失败")
                    _collageSettings.value = UiState.Error(e.message ?: "未知错误")
                }
                .collect { settings ->
                    _collageSettings.value = UiState.Success(settings)
                }
        }
    }
    
    /**
     * 更新自动拼图启用状态
     */
    fun updateAutoCollageEnabled(enabled: Boolean) {
        updateSettings { currentSettings ->
            currentSettings.copy(isAutoCollageEnabled = enabled)
        }
        
        // 如果启用了自动拼图，则调度拼图任务
        if (enabled) {
            scheduleDailyCollageUseCase.schedule()
        }
    }
    
    /**
     * 更新拼图时间
     */
    fun updateCollageTime(timeString: String) {
        try {
            val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
            val date = timeFormat.parse(timeString) ?: return
            val calendar = java.util.Calendar.getInstance()
            calendar.time = date
            
            val hour = calendar.get(java.util.Calendar.HOUR_OF_DAY)
            val minute = calendar.get(java.util.Calendar.MINUTE)
            
            updateSettings { currentSettings ->
                currentSettings.copy(collageHour = hour, collageMinute = minute)
            }
            
            // 重新调度拼图任务
            scheduleDailyCollageUseCase.schedule()
        } catch (e: Exception) {
            Timber.e(e, "解析时间失败: $timeString")
        }
    }
    
    /**
     * 更新拼图布局
     */
    fun updateCollageLayout(layoutName: String) {
        val layout = CollageLayout.fromDisplayName(layoutName)
        updateSettings { currentSettings ->
            currentSettings.copy(collageLayout = layout)
        }
    }
    
    /**
     * 更新拼图质量（通过名称）
     */
    fun updateCollageQuality(qualityName: String) {
        val quality = CollageQuality.fromDisplayName(qualityName)
        updateSettings { currentSettings ->
            currentSettings.copy(collageQuality = quality.value)
        }
    }

    /**
     * 更新拼图质量（通过数值）
     */
    fun updateCollageQuality(qualityValue: Int) {
        updateSettings { currentSettings ->
            currentSettings.copy(collageQuality = qualityValue)
        }
    }

    /**
     * 更新拼图分辨率
     */
    fun updateCollageResolution(resolutionValue: Int) {
        updateSettings { currentSettings ->
            currentSettings.copy(collageResolution = resolutionValue)
        }
    }

    /**
     * 更新拼图高斯模糊
     */
    fun updateCollageBlur(blurValue: Int) {
        updateSettings { currentSettings ->
            currentSettings.copy(collageBlur = blurValue)
        }
    }
    
    /**
     * 更新设置
     */
    private fun updateSettings(update: (CollageSettings) -> CollageSettings) {
        val currentState = _collageSettings.value
        
        if (currentState is UiState.Success) {
            val updatedSettings = update(currentState.data)
            _collageSettings.value = UiState.Success(updatedSettings)
            
            viewModelScope.launch {
                try {
                    collageSettingsRepository.saveCollageSettings(updatedSettings)
                } catch (e: Exception) {
                    Timber.e(e, "保存拼图设置失败")
                }
            }
        }
    }
}

/**
 * 拼图设置ViewModel工厂
 */
class CollageSettingsViewModelFactory(private val application: Application) : ViewModelProvider.Factory {
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(CollageSettingsViewModel::class.java)) {
            val database = AppDatabase.getInstance(application)
            val collageSettingsRepository = CollageSettingsRepositoryImpl(database.collageSettingsDao())
            val scheduleDailyCollageUseCase = ScheduleDailyCollageUseCase(application)
            
            return CollageSettingsViewModel(
                application,
                collageSettingsRepository,
                scheduleDailyCollageUseCase
            ) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}
