package com.shuyi.discipline.domain.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.shuyi.discipline.domain.service.ScreenshotService
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 开机启动广播接收器
 */
class BootReceiver : BroadcastReceiver() {
    
    @OptIn(DelicateCoroutinesApi::class)
    override fun onReceive(context: Context, intent: Intent) {
        if (intent.action == Intent.ACTION_BOOT_COMPLETED) {
            Timber.d("收到开机广播")
            
            GlobalScope.launch {
                try {
                    // 获取应用数据库实例
                    val database = com.shuyi.discipline.data.source.database.AppDatabase.getInstance(context)
                    
                    // 检查服务是否启用
                    val scheduleRule = database.scheduleRuleDao().getScheduleRule()
                    
                    if (scheduleRule?.isEnabled == true) {
                        Timber.d("开机自启动服务")
                        
                        // 延迟启动服务，确保系统完全启动
                        kotlinx.coroutines.delay(10000)
                        
                        // 启动截图服务
                        val serviceIntent = Intent(context, ScreenshotService::class.java).apply {
                            action = ScreenshotService.ACTION_START_SERVICE
                        }
                        
                        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                            context.startForegroundService(serviceIntent)
                        } else {
                            context.startService(serviceIntent)
                        }
                    }
                } catch (e: Exception) {
                    Timber.e(e, "开机自启动失败")
                }
            }
        }
    }
} 