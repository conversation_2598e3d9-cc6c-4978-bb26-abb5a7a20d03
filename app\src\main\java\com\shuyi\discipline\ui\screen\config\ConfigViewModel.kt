package com.shuyi.discipline.ui.screen.config

import android.app.Application
import android.content.Intent
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.shuyi.discipline.data.model.ScheduleRule
import com.shuyi.discipline.data.repository.ScheduleRepository
import com.shuyi.discipline.data.repository.impl.ScheduleRepositoryImpl
import com.shuyi.discipline.data.source.database.AppDatabase
import com.shuyi.discipline.domain.service.ScreenshotService
import com.shuyi.discipline.ui.model.UiState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 配置ViewModel
 */
class ConfigViewModel(application: Application) : AndroidViewModel(application) {
    
    private val context = getApplication<Application>()
    
    // 初始化仓库
    private val database = AppDatabase.getInstance(context)
    private val scheduleRepository: ScheduleRepository = ScheduleRepositoryImpl(
        database.scheduleRuleDao()
    )
    
    // 调度规则状态
    private val _scheduleRule = MutableStateFlow<UiState<ScheduleRuleUi>>(UiState.Loading)
    val scheduleRule: StateFlow<UiState<ScheduleRuleUi>> = _scheduleRule
    
    /**
     * 加载调度规则
     */
    fun loadScheduleRule() {
        viewModelScope.launch {
            try {
                val rule = scheduleRepository.getScheduleRule()
                
                if (rule != null) {
                    _scheduleRule.value = UiState.Success(
                        ScheduleRuleUi(
                            id = rule.id,
                            intervalMinutes = rule.intervalMinutes,
                            intervalSeconds = rule.intervalSeconds,
                            isRandom = rule.isRandom,
                            randomRange = rule.randomRange,
                            randomRangeMinutes = rule.randomRangeMinutes,
                            randomRangeSeconds = rule.randomRangeSeconds,
                            qualitySetting = rule.qualitySetting,
                            resolutionSetting = rule.resolutionSetting,
                            blurSetting = rule.blurSetting,
                            lastCaptureTime = rule.lastCaptureTime,
                            isEnabled = rule.isEnabled
                        )
                    )
                } else {
                    _scheduleRule.value = UiState.Error("未找到调度规则")
                }
            } catch (e: Exception) {
                Timber.e(e, "加载调度规则失败")
                _scheduleRule.value = UiState.Error(e.message ?: "未知错误")
            }
        }
    }
    
    /**
     * 更新间隔分钟
     */
    fun updateIntervalMinutes(minutes: Int) {
        val currentState = _scheduleRule.value
        if (currentState is UiState.Success) {
            val currentSeconds = currentState.data.intervalSeconds
            // 确保分钟和秒数不能同时为0
            val validMinutes = if (minutes == 0 && currentSeconds == 0) 1 else minutes
            updateScheduleRule { it.copy(intervalMinutes = validMinutes) }
        }
    }

    /**
     * 更新间隔秒数
     */
    fun updateIntervalSeconds(seconds: Int) {
        val currentState = _scheduleRule.value
        if (currentState is UiState.Success) {
            val currentMinutes = currentState.data.intervalMinutes
            // 确保分钟和秒数不能同时为0
            val validSeconds = if (currentMinutes == 0 && seconds == 0) 1 else seconds
            updateScheduleRule { it.copy(intervalSeconds = validSeconds) }
        }
    }

    /**
     * 更新随机模式
     */
    fun updateIsRandom(isRandom: Boolean) {
        updateScheduleRule { it.copy(isRandom = isRandom) }
    }
    
    /**
     * 更新随机范围
     */
    fun updateRandomRange(range: Int) {
        updateScheduleRule { it.copy(randomRange = range) }
    }

    /**
     * 更新随机范围分钟
     */
    fun updateRandomRangeMinutes(minutes: Int) {
        val currentState = _scheduleRule.value
        if (currentState is UiState.Success) {
            val currentSeconds = currentState.data.randomRangeSeconds
            // 确保分钟和秒数不能同时为0
            val validMinutes = if (minutes == 0 && currentSeconds == 0) 0 else minutes.coerceIn(0, 60)
            updateScheduleRule { it.copy(randomRangeMinutes = validMinutes) }
        }
    }

    /**
     * 更新随机范围秒数
     */
    fun updateRandomRangeSeconds(seconds: Int) {
        val currentState = _scheduleRule.value
        if (currentState is UiState.Success) {
            val currentMinutes = currentState.data.randomRangeMinutes
            // 确保分钟和秒数不能同时为0（但随机范围可以都为0）
            val validSeconds = seconds.coerceIn(0, 59)
            updateScheduleRule { it.copy(randomRangeSeconds = validSeconds) }
        }
    }
    
    /**
     * 更新质量设置
     */
    fun updateQualitySetting(quality: Int) {
        updateScheduleRule { it.copy(qualitySetting = quality) }
    }

    /**
     * 更新分辨率设置
     */
    fun updateResolutionSetting(resolution: Int) {
        updateScheduleRule { it.copy(resolutionSetting = resolution) }
    }

    /**
     * 更新高斯模糊设置
     */
    fun updateBlurSetting(blur: Int) {
        updateScheduleRule { it.copy(blurSetting = blur) }
    }
    
    /**
     * 更新调度规则
     */
    private fun updateScheduleRule(update: (ScheduleRuleUi) -> ScheduleRuleUi) {
        val currentState = _scheduleRule.value
        
        if (currentState is UiState.Success) {
            val updatedRule = update(currentState.data)
            _scheduleRule.value = UiState.Success(updatedRule)
            
            viewModelScope.launch {
                try {
                    // 保存到数据库
                    scheduleRepository.saveScheduleRule(
                        ScheduleRule(
                            id = updatedRule.id,
                            intervalMinutes = updatedRule.intervalMinutes,
                            intervalSeconds = updatedRule.intervalSeconds,
                            isRandom = updatedRule.isRandom,
                            randomRange = updatedRule.randomRange,
                            randomRangeMinutes = updatedRule.randomRangeMinutes,
                            randomRangeSeconds = updatedRule.randomRangeSeconds,
                            qualitySetting = updatedRule.qualitySetting,
                            resolutionSetting = updatedRule.resolutionSetting,
                            blurSetting = updatedRule.blurSetting,
                            lastCaptureTime = updatedRule.lastCaptureTime,
                            isEnabled = updatedRule.isEnabled
                        )
                    )
                    
                    // 通知服务更新
                    if (updatedRule.isEnabled) {
                        val serviceIntent = Intent(context, ScreenshotService::class.java).apply {
                            action = ScreenshotService.ACTION_UPDATE_SCHEDULE
                        }
                        context.startService(serviceIntent)
                    }
                    
                } catch (e: Exception) {
                    Timber.e(e, "保存调度规则失败")
                }
            }
        }
    }
}

/**
 * 调度规则UI模型
 */
data class ScheduleRuleUi(
    val id: Int = 1,
    val intervalMinutes: Int = 15,
    val intervalSeconds: Int = 0,
    val isRandom: Boolean = false,
    val randomRange: Int = 5, // 保留兼容性
    val randomRangeMinutes: Int = 0,
    val randomRangeSeconds: Int = 5,
    val qualitySetting: Int = 70,
    val resolutionSetting: Int = 100, // 分辨率设置（1-100%）
    val blurSetting: Int = 0, // 高斯模糊设置（0-100%）
    val lastCaptureTime: Long = 0L,
    val isEnabled: Boolean = false
)