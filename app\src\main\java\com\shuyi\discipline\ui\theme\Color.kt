package com.shuyi.discipline.ui.theme

import androidx.compose.ui.graphics.Color

// 主色系
val PrimaryBlue = Color(0xFF2E3A8C) // 深蓝色 - 主色
val PrimaryVariantBlue = Color(0xFF1E2761) // 深蓝色变体
val OnPrimary = Color(0xFFFFFFFF) // 主色上的文字颜色

// 辅助色系
val Green = Color(0xFF27AE60) // 绿色 - 用于表示正常运行
val Orange = Color(0xFFF2994A) // 橙色 - 用于警告
val Red = Color(0xFFEB5757) // 红色 - 用于错误

// 中性色系 - 浅色模式
val TextPrimary = Color(0xFF333333) // 主要文字颜色
val TextSecondary = Color(0xFF666666) // 次要文字颜色
val BackgroundGray = Color(0xFFF8F9FA) // 背景灰色
val DividerColor = Color(0xFFEEEEEE) // 分隔线颜色

// 中性色系 - 深色模式
val TextPrimaryDark = Color(0xFFE1E1E1) // 深色模式主要文字颜色
val TextSecondaryDark = Color(0xFFB3B3B3) // 深色模式次要文字颜色
val BackgroundDark = Color(0xFF000000) // 深色模式背景色 - 纯黑色
val SurfaceDark = Color(0xFF1C1C1E) // 深色模式表面色 - 稍微亮一点的黑色
val DividerColorDark = Color(0xFF2C2C2E) // 深色模式分隔线颜色
val NavigationBarDark = Color(0xFF1C1C1E) // 深色模式导航栏背景

// 卡片和组件颜色
val CardBackgroundLight = Color(0xFFFFFFFF) // 浅色模式卡片背景
val CardBackgroundDark = Color(0xFF1C1C1E) // 深色模式卡片背景 - 与表面色一致