package com.shuyi.discipline.ui.screen.config

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.shuyi.discipline.data.model.NotificationSettings
import com.shuyi.discipline.data.repository.NotificationSettingsRepository
import com.shuyi.discipline.data.repository.impl.NotificationSettingsRepositoryImpl
import com.shuyi.discipline.data.source.database.AppDatabase
import com.shuyi.discipline.ui.model.UiState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 通知设置ViewModel
 */
class NotificationSettingsViewModel(
    private val application: Application,
    private val notificationSettingsRepository: NotificationSettingsRepository
) : AndroidViewModel(application) {
    
    // 通知设置状态
    private val _notificationSettings = MutableStateFlow<UiState<NotificationSettings>>(UiState.Loading)
    val notificationSettings: StateFlow<UiState<NotificationSettings>> = _notificationSettings
    
    // 初始化
    init {
        loadNotificationSettings()
    }
    
    /**
     * 加载通知设置
     */
    private fun loadNotificationSettings() {
        viewModelScope.launch {
            _notificationSettings.value = UiState.Loading
            
            notificationSettingsRepository.getNotificationSettings()
                .catch { e ->
                    Timber.e(e, "加载通知设置失败")
                    _notificationSettings.value = UiState.Error(e.message ?: "未知错误")
                }
                .collect { settings ->
                    _notificationSettings.value = UiState.Success(settings)
                }
        }
    }
    
    /**
     * 更新截屏通知启用状态
     */
    fun updateScreenshotNotificationEnabled(enabled: Boolean) {
        updateSettings { currentSettings ->
            currentSettings.copy(isScreenshotNotificationEnabled = enabled)
        }
    }
    
    /**
     * 更新拼图通知启用状态
     */
    fun updateCollageNotificationEnabled(enabled: Boolean) {
        updateSettings { currentSettings ->
            currentSettings.copy(isCollageNotificationEnabled = enabled)
        }
    }
    
    /**
     * 更新设置
     */
    private fun updateSettings(update: (NotificationSettings) -> NotificationSettings) {
        val currentState = _notificationSettings.value
        
        if (currentState is UiState.Success) {
            val updatedSettings = update(currentState.data)
            _notificationSettings.value = UiState.Success(updatedSettings)
            
            viewModelScope.launch {
                try {
                    notificationSettingsRepository.saveNotificationSettings(updatedSettings)
                } catch (e: Exception) {
                    Timber.e(e, "保存通知设置失败")
                }
            }
        }
    }
}

/**
 * 通知设置ViewModel工厂
 */
class NotificationSettingsViewModelFactory(private val application: Application) : ViewModelProvider.Factory {
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(NotificationSettingsViewModel::class.java)) {
            val database = AppDatabase.getInstance(application)
            val notificationSettingsRepository = NotificationSettingsRepositoryImpl(database.notificationSettingsDao())
            
            return NotificationSettingsViewModel(
                application,
                notificationSettingsRepository
            ) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}
