package com.shuyi.discipline.ui.screen.collage

import android.app.Application
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import coil.compose.AsyncImage
import coil.request.CachePolicy
import coil.request.ImageRequest
import com.shuyi.discipline.data.repository.impl.CollageReportRepositoryImpl
import com.shuyi.discipline.data.repository.impl.ScreenshotRepositoryImpl
import com.shuyi.discipline.data.source.database.AppDatabase
import com.shuyi.discipline.ui.components.ZoomableImage
import com.shuyi.discipline.ui.model.UiState
import timber.log.Timber
import java.io.File

/**
 * 拼图预览屏幕
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun CollagePreviewScreen(
    date: String,
    initialIndex: Int = 0,
    onNavigateBack: () -> Unit,
    viewModel: CollageDetailViewModel = viewModel(
        factory = CollageDetailViewModelFactory(LocalContext.current.applicationContext as Application)
    )
) {
    // 加载拼图
    LaunchedEffect(date) {
        try {
            // 检查日期格式是否包含索引（如 "2023-05-17/0"）
            if (date.contains("/")) {
                // 如果包含索引，只取日期部分
                val datePart = date.split("/")[0]
                Timber.d("拼图预览：提取日期部分 $datePart 从 $date")
                // 加载指定日期的所有拼图
                viewModel.loadAllCollagesForDate(datePart)
            } else {
                // 直接使用日期
                Timber.d("拼图预览：使用完整日期 $date")
                viewModel.loadAllCollagesForDate(date)
            }
        } catch (e: Exception) {
            Timber.e(e, "解析预览日期失败: $date")
            // 出错时仍尝试加载
            viewModel.loadAllCollagesForDate(date)
        }
    }

    val collageState by viewModel.collage.collectAsState()

    when (collageState) {
        is UiState.Loading -> {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "加载中...",
                    color = Color.White
                )
            }
        }
        is UiState.Error -> {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "加载失败: ${(collageState as UiState.Error).message}",
                    color = Color.Red
                )
            }
        }
        is UiState.Empty -> {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "该日期没有拼图",
                    color = Color.White
                )
            }
        }
        is UiState.Success -> {
            val collage = (collageState as UiState.Success<CollageDetail>).data

            // 使用水平分页器显示多张拼图
            val imagePaths = if (collage.imagePaths.isNotEmpty()) collage.imagePaths else listOf(collage.imagePath)
            // 确保初始索引在有效范围内
            val validInitialIndex = initialIndex.coerceIn(0, imagePaths.size - 1)
            val pagerState = rememberPagerState(
                initialPage = validInitialIndex,
                pageCount = { imagePaths.size }
            )

            // 全屏显示拼图，黑色背景
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black)
            ) {
                // 水平分页器 - 禁止无限循环滑动
                HorizontalPager(
                    state = pagerState,
                    modifier = Modifier.fillMaxSize(),
                    // 禁止预加载边界外的页面，防止无限循环
                    beyondBoundsPageCount = 0
                ) { page ->
                    val imagePath = imagePaths[page]
                    val context = LocalContext.current

                    // 拼图图片 - 使用可缩放的图片组件
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .pointerInput(Unit) {
                                detectTapGestures(
                                    onTap = { onNavigateBack() }
                                )
                            }
                    ) {
                        // 使用可缩放的图片组件
                        ZoomableImage(
                            imagePath = imagePath,
                            contentDescription = "拼图预览 ${page + 1}/${imagePaths.size}",
                            modifier = Modifier.fillMaxSize()
                        )
                    }

                    // 顶部工具栏 - 返回按钮和页面指示器
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp)
                            .align(Alignment.TopCenter),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        // 返回按钮
                        IconButton(
                            onClick = onNavigateBack,
                            modifier = Modifier
                                .size(40.dp)
                                .background(Color.Black.copy(alpha = 0.5f), shape = CircleShape)
                        ) {
                            Icon(
                                imageVector = Icons.Default.ArrowBack,
                                contentDescription = "返回",
                                tint = Color.White
                            )
                        }

                        // 页面指示器
                        Text(
                            text = "${page + 1} / ${imagePaths.size}",
                            color = Color.White,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold,
                            modifier = Modifier
                                .background(
                                    Color.Black.copy(alpha = 0.5f),
                                    shape = RoundedCornerShape(16.dp)
                                )
                                .padding(horizontal = 12.dp, vertical = 6.dp)
                        )

                        // 分享按钮
                        IconButton(
                            onClick = {
                                viewModel.shareCollage(date)
                            },
                            modifier = Modifier
                                .size(40.dp)
                                .background(Color.Black.copy(alpha = 0.5f), shape = CircleShape)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Share,
                                contentDescription = "分享",
                                tint = Color.White
                            )
                        }
                    }

                    // 底部信息和页码指示器
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .align(Alignment.BottomCenter)
                            .padding(bottom = 16.dp)
                    ) {
                        Text(
                            text = "${collage.formattedDate} - ${collage.formattedTime}",
                            color = Color.White,
                            fontSize = 14.sp,
                            modifier = Modifier.align(Alignment.Center)
                        )

                        // 显示页码指示器
                        if (imagePaths.size > 1) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(top = 24.dp)
                                    .align(Alignment.BottomCenter),
                                horizontalArrangement = Arrangement.Center
                            ) {
                                repeat(imagePaths.size) { index ->
                                    val color = if (pagerState.currentPage == index) Color.White else Color.Gray
                                    Box(
                                        modifier = Modifier
                                            .padding(horizontal = 4.dp)
                                            .size(8.dp)
                                            .clip(CircleShape)
                                            .background(color)
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
        else -> {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Text(text = "未知状态", color = Color.Gray)
            }
        }
    }
}