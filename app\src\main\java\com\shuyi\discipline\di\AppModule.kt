package com.shuyi.discipline.di

import android.content.Context
import com.shuyi.discipline.data.repository.CollageReportRepository
import com.shuyi.discipline.data.repository.NotificationSettingsRepository
import com.shuyi.discipline.data.repository.QuietPeriodRepository
import com.shuyi.discipline.data.repository.RuntimeRecordRepository
import com.shuyi.discipline.data.repository.ScheduleRepository
import com.shuyi.discipline.data.repository.ScreenshotDeduplicationRepository
import com.shuyi.discipline.data.repository.ScreenshotRepository
import com.shuyi.discipline.data.repository.SystemMonitorRepository
import com.shuyi.discipline.data.repository.ThemeSettingsRepository
import com.shuyi.discipline.data.repository.impl.CollageReportRepositoryImpl
import com.shuyi.discipline.data.repository.impl.NotificationSettingsRepositoryImpl
import com.shuyi.discipline.data.repository.impl.QuietPeriodRepositoryImpl
import com.shuyi.discipline.data.repository.impl.RuntimeRecordRepositoryImpl
import com.shuyi.discipline.data.repository.impl.ScheduleRepositoryImpl
import com.shuyi.discipline.data.repository.impl.ScreenshotDeduplicationRepositoryImpl
import com.shuyi.discipline.data.repository.impl.ScreenshotRepositoryImpl
import com.shuyi.discipline.data.repository.impl.SystemMonitorRepositoryImpl
import com.shuyi.discipline.data.repository.impl.ThemeSettingsRepositoryImpl
import com.shuyi.discipline.data.source.database.AppDatabase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 应用依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
object AppModule {

    /**
     * 提供数据库实例
     */
    @Provides
    @Singleton
    fun provideAppDatabase(@ApplicationContext context: Context): AppDatabase {
        return AppDatabase.getInstance(context)
    }

    /**
     * 提供截图仓库
     */
    @Provides
    @Singleton
    fun provideScreenshotRepository(
        database: AppDatabase,
        @ApplicationContext context: Context
    ): ScreenshotRepository {
        return ScreenshotRepositoryImpl(context, database.screenshotDao())
    }

    /**
     * 提供调度仓库
     */
    @Provides
    @Singleton
    fun provideScheduleRepository(database: AppDatabase): ScheduleRepository {
        return ScheduleRepositoryImpl(database.scheduleRuleDao())
    }

    /**
     * 提供免打扰时段仓库
     */
    @Provides
    @Singleton
    fun provideQuietPeriodRepository(database: AppDatabase): QuietPeriodRepository {
        return QuietPeriodRepositoryImpl(database.quietPeriodDao())
    }

    /**
     * 提供拼图报告仓库
     */
    @Provides
    @Singleton
    fun provideCollageReportRepository(
        database: AppDatabase,
        @ApplicationContext context: Context,
        screenshotRepository: ScreenshotRepository
    ): CollageReportRepository {
        return CollageReportRepositoryImpl(context, database.collageReportDao(), screenshotRepository)
    }

    /**
     * 提供通知设置仓库
     */
    @Provides
    @Singleton
    fun provideNotificationSettingsRepository(database: AppDatabase): NotificationSettingsRepository {
        return NotificationSettingsRepositoryImpl(database.notificationSettingsDao())
    }

    /**
     * 提供主题设置仓库
     */
    @Provides
    @Singleton
    fun provideThemeSettingsRepository(database: AppDatabase): ThemeSettingsRepository {
        return ThemeSettingsRepositoryImpl(database.themeSettingsDao())
    }

    /**
     * 提供系统监控仓库
     */
    @Provides
    @Singleton
    fun provideSystemMonitorRepository(database: AppDatabase): SystemMonitorRepository {
        return SystemMonitorRepositoryImpl(database.systemMonitorDao())
    }

    /**
     * 提供运行时记录仓库
     */
    @Provides
    @Singleton
    fun provideRuntimeRecordRepository(database: AppDatabase): RuntimeRecordRepository {
        return RuntimeRecordRepositoryImpl(
            recordDao = database.runtimeRecordDao(),
            sessionDao = database.runtimeSessionDao(),
            metadataDao = database.runtimeMetadataDao()
        )
    }

    /**
     * 提供截图去重仓库
     */
    @Provides
    @Singleton
    fun provideScreenshotDeduplicationRepository(database: AppDatabase): ScreenshotDeduplicationRepository {
        return ScreenshotDeduplicationRepositoryImpl(database.screenshotDeduplicationDao())
    }
}