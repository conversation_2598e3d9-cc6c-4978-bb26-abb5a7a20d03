package com.shuyi.discipline.data.source.database

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.shuyi.discipline.data.model.QuietPeriod
import kotlinx.coroutines.flow.Flow

/**
 * 免打扰时段数据访问对象
 */
@Dao
interface QuietPeriodDao {
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertQuietPeriod(quietPeriod: QuietPeriod)
    
    @Update
    suspend fun updateQuietPeriod(quietPeriod: QuietPeriod)
    
    @Delete
    suspend fun deleteQuietPeriod(quietPeriod: QuietPeriod)
    
    @Query("SELECT * FROM quiet_periods ORDER BY startHour, startMinute")
    fun getAllQuietPeriods(): Flow<List<QuietPeriod>>
    
    @Query("SELECT * FROM quiet_periods WHERE id = :id")
    suspend fun getQuietPeriodById(id: String): QuietPeriod?
    
    @Query("SELECT * FROM quiet_periods WHERE repeatPattern = :repeatPattern OR repeatPattern = 0")
    suspend fun getQuietPeriodsForDay(repeatPattern: Int): List<QuietPeriod>
} 