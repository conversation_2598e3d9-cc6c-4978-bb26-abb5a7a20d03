package com.shuyi.discipline.ui.screen.quietperiod

/**
 * 免打扰时段重复模式常量
 */
object QuietPeriodPatterns {
    const val PATTERN_DAILY = 0     // 每天
    const val PATTERN_WEEKDAYS = 1  // 工作日(周一到周五)
    const val PATTERN_WEEKENDS = 2  // 周末(周六和周日)
    const val PATTERN_CUSTOM = 3    // 自定义
    
    /**
     * 获取所有重复模式选项
     */
    fun getAllPatternOptions(): List<PatternOption> {
        return listOf(
            PatternOption(PATTERN_DAILY, "每天"),
            PatternOption(PATTERN_WEEKDAYS, "工作日 (周一至周五)"),
            PatternOption(PATTERN_WEEKENDS, "周末 (周六和周日)"),
            PatternOption(PATTERN_CUSTOM, "自定义")
        )
    }
    
    /**
     * 获取模式名称
     */
    fun getPatternName(patternId: Int): String {
        return when (patternId) {
            PATTERN_DAILY -> "每天"
            PATTERN_WEEKDAYS -> "工作日"
            PATTERN_WEEKENDS -> "周末"
            PATTERN_CUSTOM -> "自定义"
            else -> "未知"
        }
    }
    
    /**
     * 获取模式显示文本
     */
    fun getPatternDisplayText(patternId: Int): String {
        return when (patternId) {
            PATTERN_DAILY -> "每天重复"
            PATTERN_WEEKDAYS -> "工作日重复 (周一至周五)"
            PATTERN_WEEKENDS -> "周末重复 (周六和周日)"
            PATTERN_CUSTOM -> "自定义重复"
            else -> "未知重复模式"
        }
    }
    
    /**
     * 格式化时间字符串
     */
    fun formatTimeString(hour: Int, minute: Int): String {
        return "${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}"
    }
    
    /**
     * 检查特定日期是否匹配重复模式
     * @param dayOfWeek 星期几 (1-7，1代表周一)
     * @param patternId 重复模式ID
     * @param customDays 自定义重复日 (仅当patternId为PATTERN_CUSTOM时使用)
     */
    fun isDayMatchingPattern(dayOfWeek: Int, patternId: Int, customDays: List<Int> = emptyList()): Boolean {
        return when (patternId) {
            PATTERN_DAILY -> true
            PATTERN_WEEKDAYS -> dayOfWeek in 1..5
            PATTERN_WEEKENDS -> dayOfWeek in 6..7
            PATTERN_CUSTOM -> dayOfWeek in customDays
            else -> false
        }
    }
}

/**
 * 重复模式选项
 */
data class PatternOption(
    val id: Int,
    val label: String
) 