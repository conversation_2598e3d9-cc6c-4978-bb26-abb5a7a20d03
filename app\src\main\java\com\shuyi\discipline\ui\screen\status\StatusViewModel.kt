package com.shuyi.discipline.ui.screen.status

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.shuyi.discipline.data.model.MonitorStatistics
import com.shuyi.discipline.data.model.MonitorType
import com.shuyi.discipline.data.model.SystemMonitorRecord
import com.shuyi.discipline.data.repository.RuntimeRecordRepository
import com.shuyi.discipline.data.repository.SystemMonitorRepository
import com.shuyi.discipline.domain.usecase.ScheduleSystemMonitorUseCase
import com.shuyi.discipline.ui.model.UiState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * 系统状态数据
 */
data class SystemStatusData(
    val totalDowntime: Long, // 未运行总时长
    val appDowntime: Long, // App未运行时长（旧监控系统）
    val newAppDowntime: Long, // App未运行时长（新监控系统）
    val screenshotServiceDowntime: Long, // 截图服务未运行时长
    val appDowntimeRecords: List<SystemMonitorRecord>, // App未运行记录（旧系统）
    val screenshotServiceDowntimeRecords: List<SystemMonitorRecord> // 截图服务未运行记录
)

/**
 * 系统状态页面ViewModel
 */
@HiltViewModel
class StatusViewModel @Inject constructor(
    private val systemMonitorRepository: SystemMonitorRepository,
    private val scheduleSystemMonitorUseCase: ScheduleSystemMonitorUseCase,
    private val runtimeRecordRepository: RuntimeRecordRepository
) : ViewModel() {

    // 24小时运行状态统计
    private val _status24Hours = MutableStateFlow<UiState<SystemStatusData>>(UiState.Loading)
    val status24Hours: StateFlow<UiState<SystemStatusData>> = _status24Hours.asStateFlow()

    // 48小时运行状态统计
    private val _status48Hours = MutableStateFlow<UiState<SystemStatusData>>(UiState.Loading)
    val status48Hours: StateFlow<UiState<SystemStatusData>> = _status48Hours.asStateFlow()

    // 监控任务状态
    private val _monitoringTaskStatus = MutableStateFlow<Boolean>(false)
    val monitoringTaskStatus: StateFlow<Boolean> = _monitoringTaskStatus.asStateFlow()

    // 刷新状态
    private val _isRefreshing = MutableStateFlow<Boolean>(false)
    val isRefreshing: StateFlow<Boolean> = _isRefreshing.asStateFlow()

    // 消息提示状态
    private val _message = MutableStateFlow<String?>(null)
    val message: StateFlow<String?> = _message.asStateFlow()

    // 自动刷新定时器
    private var autoRefreshJob: Job? = null

    init {
        loadStatistics()
        checkMonitoringTaskStatus()
        startAutoRefresh()
    }

    /**
     * 加载统计数据
     */
    fun loadStatistics() {
        viewModelScope.launch {
            try {
                _isRefreshing.value = true

                // 加载24小时状态数据
                load24HourStatus()

                // 加载48小时状态数据
                load48HourStatus()

                // 检查监控任务状态
                checkMonitoringTaskStatus()

            } catch (e: Exception) {
                Timber.e(e, "加载统计数据失败")
            } finally {
                _isRefreshing.value = false
            }
        }
    }

    /**
     * 刷新数据
     */
    fun refresh() {
        viewModelScope.launch {
            try {
                // 显示开始更新的提示
                _message.value = "数据正在更新中..."
                Timber.d("开始刷新数据")

                // 立即执行一次监控检查
                scheduleSystemMonitorUseCase.executeImmediateCheck()

                // 等待一段时间后重新加载数据
                kotlinx.coroutines.delay(2000)

                loadStatistics()

                // 显示更新完成的提示
                _message.value = "数据更新完成"
                Timber.d("数据刷新完成")

                // 注意：消息的自动消失逻辑现在由UI组件处理
                // 更新完成后停留1秒后自动消失

            } catch (e: Exception) {
                Timber.e(e, "刷新数据失败")
                _message.value = "数据更新失败: ${e.message}"

                // 5秒后清除错误消息
                kotlinx.coroutines.delay(5000)
                _message.value = null
            }
        }
    }

    /**
     * 启动系统监控
     */
    fun startSystemMonitoring() {
        viewModelScope.launch {
            try {
                scheduleSystemMonitorUseCase.startMonitoring()
                checkMonitoringTaskStatus()
                Timber.d("系统监控已启动")
            } catch (e: Exception) {
                Timber.e(e, "启动系统监控失败")
            }
        }
    }

    /**
     * 停止系统监控
     */
    fun stopSystemMonitoring() {
        viewModelScope.launch {
            try {
                scheduleSystemMonitorUseCase.stopMonitoring()
                checkMonitoringTaskStatus()
                Timber.d("系统监控已停止")
            } catch (e: Exception) {
                Timber.e(e, "停止系统监控失败")
            }
        }
    }

    /**
     * 加载24小时状态数据
     */
    private suspend fun load24HourStatus() {
        try {
            _status24Hours.value = UiState.Loading
            val statusData = getSystemStatusData(24)
            _status24Hours.value = UiState.Success(statusData)
            Timber.d("24小时状态数据加载成功")
        } catch (e: Exception) {
            Timber.e(e, "加载24小时状态数据失败")
            _status24Hours.value = UiState.Error(e.message ?: "加载失败")
        }
    }

    /**
     * 加载48小时状态数据
     */
    private suspend fun load48HourStatus() {
        try {
            _status48Hours.value = UiState.Loading
            val statusData = getSystemStatusData(48)
            _status48Hours.value = UiState.Success(statusData)
            Timber.d("48小时状态数据加载成功")
        } catch (e: Exception) {
            Timber.e(e, "加载48小时状态数据失败")
            _status48Hours.value = UiState.Error(e.message ?: "加载失败")
        }
    }

    /**
     * 获取系统状态数据
     */
    private suspend fun getSystemStatusData(hours: Int): SystemStatusData {
        val endTime = System.currentTimeMillis()
        val startTime = endTime - hours * 60 * 60 * 1000L

        // 获取App未运行记录（不包括设备关机时间）
        val rawAppDowntimeRecords = systemMonitorRepository.getRecordsByTypeAndTimeRange(
            MonitorType.APP_BACKGROUND_RUNNING, startTime, endTime
        ).filter { !it.isDeviceShutdown }

        // 获取截图服务未运行记录
        val rawScreenshotServiceDowntimeRecords = systemMonitorRepository.getRecordsByTypeAndTimeRange(
            MonitorType.SCREENSHOT_FUNCTION, startTime, endTime
        )

        // 处理记录：确保只有一条未完成的记录，其他未完成的记录视为已在查询时间结束
        val appDowntimeRecords = processRecords(rawAppDowntimeRecords, endTime)
        val screenshotServiceDowntimeRecords = processRecords(rawScreenshotServiceDowntimeRecords, endTime)

        // 计算App未运行时长（排除设备关机时间）
        val appDowntime = appDowntimeRecords.sumOf { record ->
            calculateRecordDurationInTimeRange(record, startTime, endTime)
        }

        // 计算截图服务未运行时长
        val screenshotServiceDowntime = screenshotServiceDowntimeRecords.sumOf { record ->
            calculateRecordDurationInTimeRange(record, startTime, endTime)
        }

        // 获取新监控系统的App未运行时长
        val newAppDowntime = try {
            runtimeRecordRepository.getTotalDowntime(hours)
        } catch (e: Exception) {
            Timber.e(e, "获取新监控系统未运行时长失败")
            0L
        }

        // 总未运行时长 = 旧App未运行时长 + 新App未运行时长 + 截图服务未运行时长
        val totalDowntime = appDowntime + newAppDowntime + screenshotServiceDowntime

        Timber.d("未运行时长统计:")
        Timber.d("  旧App未运行时长: ${appDowntime}ms")
        Timber.d("  新App未运行时长: ${newAppDowntime}ms")
        Timber.d("  截图服务未运行时长: ${screenshotServiceDowntime}ms")
        Timber.d("  总未运行时长: ${totalDowntime}ms")

        return SystemStatusData(
            totalDowntime = totalDowntime,
            appDowntime = appDowntime,
            newAppDowntime = newAppDowntime,
            screenshotServiceDowntime = screenshotServiceDowntime,
            appDowntimeRecords = appDowntimeRecords,
            screenshotServiceDowntimeRecords = screenshotServiceDowntimeRecords
        )
    }

    /**
     * 处理记录列表，确保逻辑正确
     * 对于多条未完成的记录，只保留最新的一条，其他的视为已在查询时间结束
     */
    private fun processRecords(records: List<SystemMonitorRecord>, queryTime: Long): List<SystemMonitorRecord> {
        val processedRecords = mutableListOf<SystemMonitorRecord>()

        // 分离已完成和未完成的记录
        val completedRecords = records.filter { it.endTime != null }
        val unfinishedRecords = records.filter { it.endTime == null }

        // 添加所有已完成的记录
        processedRecords.addAll(completedRecords)

        // 对于未完成的记录，只保留最新的一条，其他的设置为在查询时间结束
        if (unfinishedRecords.isNotEmpty()) {
            // 按时间戳排序，最新的在前
            val sortedUnfinishedRecords = unfinishedRecords.sortedByDescending { it.timestamp }

            // 保留最新的未完成记录
            processedRecords.add(sortedUnfinishedRecords.first())

            // 其他未完成记录视为已在查询时间结束
            sortedUnfinishedRecords.drop(1).forEach { record ->
                val processedRecord = record.copy(
                    endTime = queryTime,
                    duration = queryTime - record.startTime,
                    status = com.shuyi.discipline.data.model.MonitorStatus.RECOVERED
                )
                processedRecords.add(processedRecord)
            }
        }

        return processedRecords.sortedByDescending { it.timestamp }
    }

    /**
     * 计算记录在指定时间范围内的持续时长
     */
    private fun calculateRecordDurationInTimeRange(
        record: SystemMonitorRecord,
        rangeStartTime: Long,
        rangeEndTime: Long
    ): Long {
        // 确保记录的开始时间在范围内
        val effectiveStartTime = maxOf(record.startTime, rangeStartTime)

        return when {
            // 记录已结束
            record.endTime != null -> {
                val effectiveEndTime = minOf(record.endTime, rangeEndTime)
                maxOf(0L, effectiveEndTime - effectiveStartTime)
            }
            // 记录仍在进行中且状态为异常
            record.status == com.shuyi.discipline.data.model.MonitorStatus.ABNORMAL -> {
                maxOf(0L, rangeEndTime - effectiveStartTime)
            }
            // 其他情况
            else -> 0L
        }
    }

    /**
     * 检查监控任务状态
     */
    private fun checkMonitoringTaskStatus() {
        viewModelScope.launch {
            try {
                val isRunning = scheduleSystemMonitorUseCase.getMonitoringStatus()
                _monitoringTaskStatus.value = isRunning
                Timber.d("监控任务状态: $isRunning")
            } catch (e: Exception) {
                Timber.e(e, "检查监控任务状态失败")
                _monitoringTaskStatus.value = false
            }
        }
    }

    /**
     * 插入测试数据（仅用于演示）
     */
    fun insertTestData() {
        viewModelScope.launch {
            try {
                systemMonitorRepository.insertTestData()
                Timber.d("测试数据插入成功")
                // 重新加载统计数据
                loadStatistics()
            } catch (e: Exception) {
                Timber.e(e, "插入测试数据失败")
            }
        }
    }

    /**
     * 清空所有监控数据（用于测试）
     */
    fun clearAllMonitorData() {
        viewModelScope.launch {
            try {
                Timber.d("开始清空所有监控数据")

                // 清空旧监控系统数据
                systemMonitorRepository.deleteAllRecords()

                // 清空新监控系统数据
                runtimeRecordRepository.deleteAllData()

                // 🎯 关键修复：重置监控状态，避免重新处理历史退出信息
                resetMonitoringState()

                Timber.d("清空所有监控数据成功")
                // 重新加载统计数据
                loadStatistics()
            } catch (e: Exception) {
                Timber.e(e, "清空监控数据失败")
            }
        }
    }

    /**
     * 重置监控状态，避免重新处理历史数据
     */
    private suspend fun resetMonitoringState() {
        try {
            // 使用专门的重置方法
            runtimeRecordRepository.resetMonitoringState()

            Timber.d("已重置监控状态")

        } catch (e: Exception) {
            Timber.e(e, "重置监控状态失败")
        }
    }

    /**
     * 立即执行一次系统监控检查（用于测试）
     */
    fun executeImmediateMonitorCheck() {
        viewModelScope.launch {
            try {
                scheduleSystemMonitorUseCase.executeImmediateCheck()
                Timber.d("立即执行系统监控检查")

                // 等待一段时间后重新加载统计数据
                kotlinx.coroutines.delay(3000) // 等待3秒
                loadStatistics()
            } catch (e: Exception) {
                Timber.e(e, "立即执行系统监控检查失败")
            }
        }
    }

    /**
     * 清除消息提示
     */
    fun clearMessage() {
        _message.value = null
    }

    /**
     * 启动自动刷新
     * 每30秒刷新一次统计数据，确保实时显示未运行时长
     */
    private fun startAutoRefresh() {
        autoRefreshJob?.cancel()
        autoRefreshJob = viewModelScope.launch {
            while (true) {
                try {
                    // 等待30秒
                    kotlinx.coroutines.delay(30_000L)

                    // 静默刷新统计数据（不显示刷新状态）
                    silentRefreshStatistics()

                } catch (e: CancellationException) {
                    Timber.d("自动刷新协程被取消")
                    break
                } catch (e: Exception) {
                    Timber.e(e, "自动刷新失败")
                    // 继续循环，不中断自动刷新
                }
            }
        }
        Timber.d("自动刷新已启动，每30秒刷新一次")
    }

    /**
     * 静默刷新统计数据
     * 不显示刷新状态，用于自动刷新
     */
    private suspend fun silentRefreshStatistics() {
        try {
            // 加载24小时状态数据
            load24HourStatus()

            // 加载48小时状态数据
            load48HourStatus()

            Timber.d("自动刷新统计数据完成")
        } catch (e: Exception) {
            Timber.e(e, "静默刷新统计数据失败")
        }
    }

    /**
     * 停止自动刷新
     */
    fun stopAutoRefresh() {
        autoRefreshJob?.cancel()
        autoRefreshJob = null
        Timber.d("自动刷新已停止")
    }

    override fun onCleared() {
        super.onCleared()
        stopAutoRefresh()
    }

    /**
     * 获取新监控系统的运行时记录
     */
    suspend fun getNewAppRuntimeRecords(): List<com.shuyi.discipline.data.repository.RuntimeRecord> {
        return try {
            val endTime = System.currentTimeMillis()
            val startTime = endTime - 24 * 60 * 60 * 1000L // 24小时

            // 获取所有记录并按时间戳降序排序，只返回DOWNTIME类型的记录
            val allRecords = runtimeRecordRepository.getRecordsInRange(startTime, endTime)
            val downtimeRecords = allRecords
                .filter { it.type == com.shuyi.discipline.data.repository.RecordType.DOWNTIME }
                .sortedByDescending { it.timestamp } // 按时间戳降序排序，最新的在前

            Timber.d("获取到 ${downtimeRecords.size} 条未运行记录")
            downtimeRecords.forEach { record ->
                val startTime = java.util.Date(record.timestamp)
                val endTime = java.util.Date(record.timestamp + record.duration)
                Timber.d("未运行记录: ${startTime} -> ${endTime}, 时长: ${record.duration}ms")
            }

            downtimeRecords
        } catch (e: Exception) {
            Timber.e(e, "获取新监控系统运行时记录失败")
            emptyList()
        }
    }
}
