package com.shuyi.discipline.data.repository

import android.graphics.Bitmap
import com.shuyi.discipline.data.model.Screenshot
import kotlinx.coroutines.flow.Flow
import java.io.File
import java.util.Date

/**
 * 截图存储仓库接口
 */
interface ScreenshotRepository {
    /**
     * 保存截图
     */
    suspend fun saveScreenshot(screenshot: Screenshot)
    
    /**
     * 获取所有截图
     */
    fun getAllScreenshots(): Flow<List<Screenshot>>
    
    /**
     * 获取未处理的截图
     */
    fun getUnprocessedScreenshots(): Flow<List<Screenshot>>
    
    /**
     * 获取指定日期的截图
     * @param date 日期字符串，格式为"yyyy-MM-dd"
     */
    suspend fun getScreenshotsForDay(date: String): List<Screenshot>

    /**
     * 监听指定日期的截图变化
     * @param date 日期字符串，格式为"yyyy-MM-dd"
     */
    fun observeScreenshotsForDay(date: String): Flow<List<Screenshot>>
    
    /**
     * 获取已审阅的截图
     */
    suspend fun getReviewedScreenshots(): Flow<List<Screenshot>>
    
    /**
     * 获取未审阅的截图
     */
    suspend fun getUnreviewedScreenshots(): Flow<List<Screenshot>>
    
    /**
     * 获取指定应用包名的截图
     */
    suspend fun getScreenshotsByPackageName(packageName: String): Flow<List<Screenshot>>
    
    /**
     * 获取指定分类的截图
     */
    suspend fun getScreenshotsByCategory(category: String): Flow<List<Screenshot>>
    
    /**
     * 按日期获取截图
     */
    suspend fun getScreenshotsByDate(startDate: Date, endDate: Date): Flow<List<Screenshot>>
    
    /**
     * 标记截图为已处理
     */
    suspend fun markScreenshotsAsProcessed(screenshots: List<Screenshot>)
    
    /**
     * 删除旧截图
     */
    suspend fun deleteOldScreenshots(daysToKeep: Int)
    
    /**
     * 根据ID删除截图
     */
    suspend fun deleteScreenshotById(id: String): Int

    /**
     * 根据文件路径删除截图
     */
    suspend fun deleteScreenshotByPath(filePath: String): Int
    
    /**
     * 将Bitmap保存为文件
     * @param bitmap 原始bitmap
     * @param quality 压缩质量（1-100）
     * @param resolution 分辨率百分比（1-100）
     * @param blur 高斯模糊百分比（0-100）
     */
    suspend fun saveBitmapToFile(bitmap: Bitmap, quality: Int = 80, resolution: Int = 100, blur: Int = 0): File
} 