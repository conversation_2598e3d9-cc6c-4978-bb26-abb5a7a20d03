package com.shuyi.discipline.ui.theme

import android.content.Context
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import com.shuyi.discipline.data.model.FontSize
import com.shuyi.discipline.data.model.ThemeMode
import com.shuyi.discipline.data.model.ThemeSettings
import com.shuyi.discipline.data.repository.ThemeSettingsRepository
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.Flow
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 主题管理器
 */
@Singleton
class ThemeManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val themeSettingsRepository: ThemeSettingsRepository
) {

    /**
     * 获取主题设置流
     */
    fun getThemeSettings(): Flow<ThemeSettings> {
        return themeSettingsRepository.getThemeSettings()
    }

    /**
     * 获取主题设置的 Composable 状态
     * 使用 remember 来避免重复创建 collectAsState
     */
    @Composable
    fun getThemeSettingsState(): ThemeSettings {
        val themeSettingsFlow = remember { getThemeSettings() }
        val themeSettings by themeSettingsFlow.collectAsState(initial = ThemeSettings())
        return themeSettings
    }

    /**
     * 判断是否应该使用深色主题
     */
    @Composable
    fun shouldUseDarkTheme(): Boolean {
        val themeSettings = getThemeSettingsState()
        val isSystemInDarkTheme = isSystemInDarkTheme()

        return when (themeSettings.themeMode) {
            ThemeMode.LIGHT -> false
            ThemeMode.DARK -> true
            ThemeMode.FOLLOW_SYSTEM -> isSystemInDarkTheme
        }
    }

    /**
     * 判断是否启用动态颜色
     */
    @Composable
    fun shouldUseDynamicColor(): Boolean {
        val themeSettings = getThemeSettingsState()
        return themeSettings.isDynamicColorEnabled
    }

    /**
     * 获取当前字体大小
     */
    @Composable
    fun getCurrentFontSize(): FontSize {
        val themeSettings = getThemeSettingsState()
        return themeSettings.fontSize
    }
}
