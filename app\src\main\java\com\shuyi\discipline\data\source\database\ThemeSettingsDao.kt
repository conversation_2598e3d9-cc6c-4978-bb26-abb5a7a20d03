package com.shuyi.discipline.data.source.database

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.shuyi.discipline.data.model.ThemeSettings
import kotlinx.coroutines.flow.Flow

/**
 * 主题设置数据访问对象
 */
@Dao
interface ThemeSettingsDao {
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertThemeSettings(themeSettings: ThemeSettings)
    
    @Update
    suspend fun updateThemeSettings(themeSettings: ThemeSettings)
    
    @Query("SELECT * FROM theme_settings WHERE id = 1")
    fun getThemeSettings(): Flow<ThemeSettings?>
    
    @Query("SELECT * FROM theme_settings WHERE id = 1")
    suspend fun getThemeSettingsSync(): ThemeSettings?
}
