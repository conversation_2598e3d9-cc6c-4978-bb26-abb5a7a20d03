package com.shuyi.discipline.ui.screen.collage

import android.app.Application
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CalendarToday
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.GridView
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.shuyi.discipline.ui.components.BottomNavBar
import com.shuyi.discipline.ui.components.DatePickerDialog
import com.shuyi.discipline.ui.navigation.Screen
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import com.shuyi.discipline.utils.FileUtils

/**
 * 拼图列表屏幕
 */
@Composable
fun CollageListScreen(
    onNavigateBack: () -> Unit = {},
    onNavigateToHome: () -> Unit = {},
    onNavigateToScreenshots: () -> Unit = {},
    onNavigateToConfig: () -> Unit = {},
    onNavigateToCollageDetail: (String) -> Unit = {},
    viewModel: CollageListViewModel = viewModel(
        factory = CollageViewModelFactory(LocalContext.current.applicationContext as Application)
    ),
    navController: NavController? = null
) {
    val uiState by viewModel.uiState.collectAsState()
    var selectedTabIndex by remember { mutableStateOf(2) } // 拼图选项卡
    var showPreview by remember { mutableStateOf(false) }
    var previewCollageIndex by remember { mutableStateOf(0) }
    var previewCollageDate by remember { mutableStateOf("") }
    var showDatePicker by remember { mutableStateOf(false) }

    // 导航到预览页面
    fun navigateToPreview(index: Int, date: String) {
        // 直接导航到预览页面，传递日期和索引
        val collage = uiState.allCollages.getOrNull(index)
        if (collage != null) {
            // 使用日期和索引导航到预览页面
            onNavigateToCollageDetail("$date/$index")
        }
    }

    Scaffold(
        bottomBar = {
            BottomNavBar(
                selectedIndex = selectedTabIndex,
                onItemSelected = { index ->
                    selectedTabIndex = index
                    // 根据选中的标签执行相应的导航
                    when(index) {
                        0 -> onNavigateToHome()
                        1 -> onNavigateToScreenshots()
                        // 2是拼图页面，已经在此页面，无需导航
                        3 -> navController?.navigate(Screen.Status.route) {
                            // 避免创建多个实例
                            launchSingleTop = true
                        }
                        4 -> onNavigateToConfig()
                    }
                },
                onNavigateToHome = onNavigateToHome,
                onNavigateToScreenshots = onNavigateToScreenshots,
                onNavigateToCollages = { /* 已在拼图页面，无需导航 */ },
                onNavigateToStatus = {
                    navController?.navigate(Screen.Status.route) {
                        // 避免创建多个实例
                        launchSingleTop = true
                    }
                },
                onNavigateToConfig = onNavigateToConfig
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(horizontal = 16.dp)
        ) {
            // 标题栏
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "拼图列表",
                    style = MaterialTheme.typography.headlineMedium,
                    fontWeight = FontWeight.Bold
                )

                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // 当前选中日期显示
                    Text(
                        text = uiState.formattedDate,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSecondaryContainer
                    )

                    // 日期选择按钮
                    Box(
                        modifier = Modifier
                            .size(40.dp)
                            .clip(CircleShape)
                            .background(MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f))
                            .clickable { showDatePicker = true },
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.CalendarToday,
                            contentDescription = "选择日期",
                            tint = MaterialTheme.colorScheme.onSecondaryContainer
                        )
                    }
                }
            }



            // 按钮行：立即拼图和清除当日拼图
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 立即拼图按钮
                Button(
                    onClick = { viewModel.generateCollage() },
                    modifier = Modifier
                        .weight(1f),
                    colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.primary),
                    enabled = !uiState.isLoading // 加载中时禁用按钮
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.GridView,
                            contentDescription = "拼图图标",
                            tint = MaterialTheme.colorScheme.onPrimary,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = "立即拼图",
                            color = MaterialTheme.colorScheme.onPrimary,
                            fontSize = 14.sp,
                            modifier = Modifier.padding(vertical = 8.dp)
                        )
                    }
                }

                // 清除当日拼图按钮
                Button(
                    onClick = { viewModel.deleteCurrentDateCollages() },
                    modifier = Modifier
                        .weight(1f),
                    colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.error),
                    enabled = !uiState.isLoading && uiState.totalCollages > 0 // 加载中或没有拼图时禁用按钮
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center
                    ) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "删除图标",
                            tint = Color.White,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = "清除拼图",
                            color = Color.White,
                            fontSize = 14.sp,
                            modifier = Modifier.padding(vertical = 8.dp)
                        )
                    }
                }
            }

            // 错误信息显示
            if (uiState.error != null) {
                Text(
                    text = uiState.error!!,
                    color = Color.Red,
                    fontSize = 14.sp,
                    modifier = Modifier.padding(vertical = 4.dp)
                )
            }

            if (uiState.isLoading) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            } else {
                // 内容区域
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(bottom = 16.dp)  // 确保内容不会覆盖底部导航栏
                        .verticalScroll(rememberScrollState())
                ) {
                    // 拼图统计卡片
                    CollageStatsCard(
                        date = uiState.formattedDate,
                        count = uiState.totalCollages
                    )

                    // 所有拼图
                    if (uiState.allCollages.isNotEmpty()) {
                        Text(
                            text = "所有拼图",
                            style = MaterialTheme.typography.bodyMedium,
                            color = Color.Gray,
                            modifier = Modifier.padding(vertical = 8.dp)
                        )

                        CollageGrid(
                            collages = uiState.allCollages,
                            onCollageClick = { index, date ->
                                navigateToPreview(index, date)
                            }
                        )
                    }

                    // 无数据提示
                    if (uiState.totalCollages == 0) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(200.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "当前日期没有拼图",
                                color = Color.Gray
                            )
                        }
                    }
                }
            }
        }
    }

    // 日期选择对话框
    DatePickerDialog(
        isVisible = showDatePicker,
        selectedDate = uiState.dateItems.getOrNull(uiState.selectedDateIndex)?.date ?: Date(),
        onDateSelected = { selectedDate ->
            viewModel.selectDateByDate(selectedDate)
        },
        onDismiss = { showDatePicker = false }
    )
}

/**
 * 日期选择器项
 */
@Composable
fun DateSelectorItem(
    dayOfWeek: String,
    day: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .padding(end = 12.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(if (isSelected) MaterialTheme.colorScheme.primary else Color.Transparent)
            .clickable(onClick = onClick)
            .padding(horizontal = 12.dp, vertical = 8.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = dayOfWeek,
            fontSize = 12.sp,
            color = if (isSelected) MaterialTheme.colorScheme.onPrimary else MaterialTheme.colorScheme.onSecondaryContainer
        )
        Text(
            text = day,
            fontSize = 16.sp,
            fontWeight = FontWeight.Medium,
            color = if (isSelected) MaterialTheme.colorScheme.onPrimary else MaterialTheme.colorScheme.onSurface
        )
    }
}

/**
 * 拼图统计卡片
 */
@Composable
fun CollageStatsCard(date: String, count: Int) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(12.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "今日拼图",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.SemiBold
                )

                Text(
                    text = " · $date",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSecondaryContainer
                )
            }

            Text(
                text = "共计 $count 张拼图",
                style = MaterialTheme.typography.bodyMedium,
                fontWeight = FontWeight.Medium,
                color = Color.Gray
            )
        }
    }
}

/**
 * 拼图网格
 */
@Composable
fun CollageGrid(
    collages: List<CollageInfo>,
    onCollageClick: (index: Int, date: String) -> Unit = { _, _ -> }
) {
    val timeFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())

    // 计算网格高度，确保所有拼图都能显示
    // 每行显示2个拼图，每个拼图高度约为110dp，加上间距
    val rows = (collages.size + 1) / 2 // 向上取整
    val gridHeight = (rows * 120).dp // 每行高度120dp，包含间距

    LazyVerticalGrid(
        columns = GridCells.Fixed(2),
        modifier = Modifier
            .fillMaxWidth()
            .height(gridHeight),
        verticalArrangement = Arrangement.spacedBy(8.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        // 禁用滚动，因为外层已有滚动
        userScrollEnabled = false
    ) {
        itemsIndexed(
            items = collages,
            key = { _, collage -> collage.id }
        ) { index, collage ->
            CollageItem(
                filePath = collage.collagePath,
                time = timeFormat.format(Date(collage.createTime)),
                onClick = { onCollageClick(index, collage.date) }
            )
        }
    }
}

/**
 * 拼图项
 */
@Composable
fun CollageItem(
    filePath: String,
    time: String,
    onClick: () -> Unit = {}
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(16f / 10f)  // 稍微调整比例，使其更紧凑
            .clip(RoundedCornerShape(8.dp))
            .background(Color.LightGray)
            .clickable(onClick = onClick)
    ) {
        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current)
                .data(File(filePath))
                .crossfade(true)
                .build(),
            contentDescription = "拼图",
            contentScale = ContentScale.Crop,
            modifier = Modifier.fillMaxSize()
        )

        Box(
            modifier = Modifier
                .align(Alignment.BottomStart)
                .fillMaxWidth()
                .background(Color.Black.copy(alpha = 0.5f))
                .padding(8.dp)
        ) {
            Column {
                Text(
                    text = time,
                    color = Color.White,
                    style = MaterialTheme.typography.bodySmall
                )
                Text(
                    text = FileUtils.formatFileSize(filePath),
                    color = Color.White.copy(alpha = 0.8f),
                    style = MaterialTheme.typography.bodySmall.copy(fontSize = 10.sp)
                )
            }
        }
    }
}

/**
 * 预览
 */
@Preview(showBackground = true)
@Composable
fun CollageListScreenPreview() {
    MaterialTheme {
        Surface {
            CollageListScreen()
        }
    }
}
