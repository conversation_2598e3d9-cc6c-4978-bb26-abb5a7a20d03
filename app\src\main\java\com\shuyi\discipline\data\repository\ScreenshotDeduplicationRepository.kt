package com.shuyi.discipline.data.repository

import com.shuyi.discipline.data.model.DeduplicationStats
import com.shuyi.discipline.data.model.ScreenshotDeduplication
import com.shuyi.discipline.utils.SensitivityLevel
import kotlinx.coroutines.flow.Flow

/**
 * 截图去重仓库接口
 */
interface ScreenshotDeduplicationRepository {
    
    /**
     * 获取去重设置（Flow）
     */
    fun getDeduplication(): Flow<ScreenshotDeduplication>
    
    /**
     * 同步获取去重设置
     */
    suspend fun getDeduplicationSync(): ScreenshotDeduplication
    
    /**
     * 保存去重设置
     */
    suspend fun saveDeduplication(deduplication: ScreenshotDeduplication)
    
    /**
     * 更新上一张截图的哈希值和路径
     */
    suspend fun updateLastScreenshotHash(hash: Long, path: String)
    
    /**
     * 更新重复统计信息
     */
    suspend fun updateDuplicateStats(spaceSavedMb: Double)
    
    /**
     * 更新处理计数（非重复情况）
     */
    suspend fun updateProcessedCount()
    
    /**
     * 更新去重功能启用状态
     */
    suspend fun updateEnabled(enabled: Boolean)
    
    /**
     * 更新敏感度级别
     */
    suspend fun updateSensitivityLevel(level: SensitivityLevel)
    
    /**
     * 重置统计信息
     */
    suspend fun resetStats()
    
    /**
     * 获取上一张截图的哈希值
     */
    suspend fun getLastScreenshotHash(): Long
    
    /**
     * 获取去重功能启用状态
     */
    suspend fun isDeduplicationEnabled(): Boolean
    
    /**
     * 获取敏感度级别
     */
    suspend fun getSensitivityLevel(): SensitivityLevel
    
    /**
     * 获取去重统计信息
     */
    suspend fun getDeduplicationStats(): DeduplicationStats
}
