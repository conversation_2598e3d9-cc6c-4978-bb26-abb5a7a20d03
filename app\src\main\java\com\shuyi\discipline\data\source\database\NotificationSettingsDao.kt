package com.shuyi.discipline.data.source.database

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.shuyi.discipline.data.model.NotificationSettings
import kotlinx.coroutines.flow.Flow

/**
 * 通知设置数据访问对象
 */
@Dao
interface NotificationSettingsDao {
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertNotificationSettings(notificationSettings: NotificationSettings)
    
    @Query("SELECT * FROM notification_settings WHERE id = 1")
    fun getNotificationSettings(): Flow<NotificationSettings?>
    
    @Query("SELECT * FROM notification_settings WHERE id = 1")
    suspend fun getNotificationSettingsSync(): NotificationSettings?
    
    @Query("UPDATE notification_settings SET isScreenshotNotificationEnabled = :enabled WHERE id = 1")
    suspend fun updateScreenshotNotificationEnabled(enabled: Boolean)
    
    @Query("UPDATE notification_settings SET isCollageNotificationEnabled = :enabled WHERE id = 1")
    suspend fun updateCollageNotificationEnabled(enabled: Boolean)
}
