package com.shuyi.discipline.domain.usecase

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import com.shuyi.discipline.data.repository.ScheduleRepository
import com.shuyi.discipline.domain.receiver.ScreenshotAlarmReceiver
import timber.log.Timber

/**
 * 截图调度用例
 */
class ScheduleScreenshotUseCase(
    private val context: Context,
    private val scheduleRepository: ScheduleRepository
) {

    /**
     * 执行调度
     */
    suspend fun schedule() {
        try {
            // 获取调度规则
            val rule = scheduleRepository.getScheduleRule()
            if (rule == null) {
                Timber.e("无法获取调度规则")
                return
            }

            // 检查服务是否启用
            if (!rule.isEnabled) {
                Timber.d("截图服务未启用，不进行调度")
                return
            }

            val nextCaptureTime = scheduleRepository.calculateNextCaptureTime()
            Timber.d("计算得到下次截图时间: ${java.util.Date(nextCaptureTime)}, 间隔分钟: ${rule.intervalMinutes}")

            // 获取AlarmManager
            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager

            // 创建PendingIntent
            val intent = Intent(context, ScreenshotAlarmReceiver::class.java).apply {
                action = ScreenshotAlarmReceiver.ACTION_TAKE_SCREENSHOT
            }

            val pendingIntent = PendingIntent.getBroadcast(
                context,
                0,
                intent,
                PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
            )

            // 取消之前的闹钟，确保不会重复触发
            alarmManager.cancel(pendingIntent)

            // 设置闹钟
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                // Android 12及以上版本，需要检查权限
                if (alarmManager.canScheduleExactAlarms()) {
                    Timber.d("使用setExactAndAllowWhileIdle设置精确闹钟")
                    alarmManager.setExactAndAllowWhileIdle(
                        AlarmManager.RTC_WAKEUP,
                        nextCaptureTime,
                        pendingIntent
                    )
                } else {
                    // 降级为不精确的闹钟
                    Timber.d("无法设置精确闹钟，降级为普通闹钟")
                    alarmManager.set(
                        AlarmManager.RTC_WAKEUP,
                        nextCaptureTime,
                        pendingIntent
                    )
                }
            } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // Android 6.0到Android 11
                Timber.d("使用setExactAndAllowWhileIdle设置精确闹钟")
                alarmManager.setExactAndAllowWhileIdle(
                    AlarmManager.RTC_WAKEUP,
                    nextCaptureTime,
                    pendingIntent
                )
            } else {
                // 旧版Android
                Timber.d("使用setExact设置精确闹钟")
                alarmManager.setExact(
                    AlarmManager.RTC_WAKEUP,
                    nextCaptureTime,
                    pendingIntent
                )
            }
            Timber.d("截图调度成功，下次截图时间: ${java.util.Date(nextCaptureTime)}")
        } catch (e: Exception) {
            Timber.e(e, "截图调度失败: ${e.message}")
        }
    }

    /**
     * 取消调度
     */
    fun cancel() {
        val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as AlarmManager
        val intent = Intent(context, ScreenshotAlarmReceiver::class.java).apply {
            action = ScreenshotAlarmReceiver.ACTION_TAKE_SCREENSHOT
        }
        val pendingIntent = PendingIntent.getBroadcast(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        alarmManager.cancel(pendingIntent)
        pendingIntent.cancel()

        Timber.d("截图调度已取消")
    }
}