package com.shuyi.discipline.ui.screen.collage

import android.app.Application
import android.content.Intent
import android.net.Uri
import androidx.core.content.FileProvider
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.shuyi.discipline.data.model.CollageReport
import com.shuyi.discipline.data.repository.CollageReportRepository
import com.shuyi.discipline.data.repository.impl.CollageReportRepositoryImpl
import com.shuyi.discipline.data.repository.impl.ScreenshotRepositoryImpl
import com.shuyi.discipline.data.source.database.AppDatabase
import com.shuyi.discipline.ui.model.UiState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 拼图详情数据类
 */
data class CollageDetail(
    val id: String,
    val date: Date,
    val imagePath: String,
    val imagePaths: List<String> = emptyList(), // 多张拼图的路径
    val screenshotCount: Int,
    val formattedDate: String,
    val formattedTime: String
)

/**
 * 拼图详情ViewModel
 */
class CollageDetailViewModel(
    private val application: Application,
    private val collageReportRepository: CollageReportRepository
) : ViewModel() {

    private val _collage = MutableStateFlow<UiState<CollageDetail>>(UiState.Loading)
    val collage: StateFlow<UiState<CollageDetail>> = _collage

    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    private val timeFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
    private val displayDateFormat = SimpleDateFormat("yyyy年MM月dd日", Locale.getDefault())

    /**
     * 加载指定日期的拼图
     */
    fun loadCollage(dateString: String) {
        viewModelScope.launch {
            try {
                _collage.value = UiState.Loading
                Timber.d("开始加载拼图详情，日期: $dateString")

                // 获取日期的拼图报告
                val collageReport = collageReportRepository.getCollageReportForDate(dateString)

                if (collageReport == null) {
                    Timber.d("该日期没有拼图: $dateString")
                    _collage.value = UiState.Empty
                    return@launch
                }

                // 检查拼图文件是否存在
                val collageFile = File(collageReport.collagePath)
                if (!collageFile.exists()) {
                    Timber.e("拼图文件不存在: ${collageReport.collagePath}")
                    _collage.value = UiState.Error("拼图文件不存在")
                    return@launch
                }

                // 创建拼图详情
                val collageDetail = CollageDetail(
                    id = collageReport.id,
                    date = collageReport.date,
                    imagePath = collageReport.collagePath,
                    imagePaths = collageReport.collagePaths.ifEmpty { listOf(collageReport.collagePath) },
                    screenshotCount = collageReport.screenshotIds.size,
                    formattedDate = displayDateFormat.format(collageReport.date),
                    formattedTime = timeFormat.format(Date(collageReport.createTime))
                )

                Timber.d("拼图详情加载成功: ${collageDetail.id}, 路径: ${collageDetail.imagePath}")
                _collage.value = UiState.Success(collageDetail)
            } catch (e: Exception) {
                Timber.e(e, "加载拼图详情失败: $dateString")
                _collage.value = UiState.Error(e.message ?: "未知错误")
            }
        }
    }

    /**
     * 加载指定日期的所有拼图
     * 这个方法会查找该日期的所有拼图报告，并将它们的路径合并到一个列表中
     */
    fun loadAllCollagesForDate(dateString: String) {
        viewModelScope.launch {
            try {
                _collage.value = UiState.Loading
                Timber.d("开始加载日期的所有拼图，日期: $dateString")

                // 获取所有拼图报告
                val allCollageReports = mutableListOf<CollageReport>()

                // 从数据库获取所有拼图报告（使用first()避免持续监听）
                val reports = collageReportRepository.getAllCollageReports().first()
                // 过滤出指定日期的拼图报告
                val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
                val filteredReports = reports.filter { report ->
                    dateFormat.format(report.date) == dateString
                }

                if (filteredReports.isEmpty()) {
                    Timber.d("该日期没有拼图: $dateString")
                    _collage.value = UiState.Empty
                    return@launch
                }

                allCollageReports.addAll(filteredReports)

                // 按创建时间排序，最早的拼图显示在前面
                val sortedReports = filteredReports.sortedBy { it.createTime }

                // 收集所有拼图路径
                val allImagePaths = mutableListOf<String>()
                sortedReports.forEach { report ->
                    // 如果collagePaths不为空，添加所有路径；否则添加主路径
                    if (report.collagePaths.isNotEmpty()) {
                        // 按照路径名称排序，确保顺序一致
                        val sortedPaths = report.collagePaths.sorted()
                        allImagePaths.addAll(sortedPaths)
                    } else {
                        allImagePaths.add(report.collagePath)
                    }
                }

                // 检查文件是否存在，过滤掉不存在的文件
                val validImagePaths = allImagePaths.filter { path ->
                    val file = File(path)
                    file.exists()
                }

                if (validImagePaths.isEmpty()) {
                    Timber.e("所有拼图文件都不存在")
                    _collage.value = UiState.Error("拼图文件不存在")
                    return@launch
                }

                // 使用第一个报告作为基础，但使用所有有效的拼图路径
                val firstReport = filteredReports.first()
                val collageDetail = CollageDetail(
                    id = firstReport.id,
                    date = firstReport.date,
                    imagePath = validImagePaths.first(), // 使用第一个有效路径作为主路径
                    imagePaths = validImagePaths, // 使用所有有效路径
                    screenshotCount = firstReport.screenshotIds.size,
                    formattedDate = displayDateFormat.format(firstReport.date),
                    formattedTime = timeFormat.format(Date(firstReport.createTime))
                )

                Timber.d("拼图详情加载成功: ${collageDetail.id}, 共 ${validImagePaths.size} 张拼图")
                _collage.value = UiState.Success(collageDetail)
            } catch (e: Exception) {
                Timber.e(e, "加载日期的所有拼图失败: $dateString")
                _collage.value = UiState.Error(e.message ?: "未知错误")
            }
        }
    }

    /**
     * 分享拼图
     */
    fun shareCollage(dateString: String) {
        viewModelScope.launch {
            try {
                // 获取拼图报告
                val collageReport = collageReportRepository.getCollageReportForDate(dateString)
                if (collageReport == null) {
                    Timber.e("分享失败，拼图不存在: $dateString")
                    return@launch
                }

                // 检查文件是否存在
                val collageFile = File(collageReport.collagePath)
                if (!collageFile.exists()) {
                    Timber.e("分享失败，拼图文件不存在: ${collageReport.collagePath}")
                    return@launch
                }

                // 创建分享Intent
                val uri = FileProvider.getUriForFile(
                    application,
                    "${application.packageName}.fileprovider",
                    collageFile
                )

                val shareIntent = Intent().apply {
                    action = Intent.ACTION_SEND
                    putExtra(Intent.EXTRA_STREAM, uri)
                    type = "image/jpeg"
                    addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
                }

                // 启动分享
                val chooserIntent = Intent.createChooser(shareIntent, "分享拼图")
                chooserIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                application.startActivity(chooserIntent)

                Timber.d("拼图分享成功: ${collageReport.id}")
            } catch (e: Exception) {
                Timber.e(e, "分享拼图失败: $dateString")
            }
        }
    }
}

/**
 * 拼图详情ViewModel工厂
 */
class CollageDetailViewModelFactory(private val application: Application) : ViewModelProvider.Factory {
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(CollageDetailViewModel::class.java)) {
            val database = AppDatabase.getInstance(application)
            val screenshotRepository = ScreenshotRepositoryImpl(application, database.screenshotDao())
            val collageReportRepository = CollageReportRepositoryImpl(
                application,
                database.collageReportDao(),
                screenshotRepository
            )
            return CollageDetailViewModel(application, collageReportRepository) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}
