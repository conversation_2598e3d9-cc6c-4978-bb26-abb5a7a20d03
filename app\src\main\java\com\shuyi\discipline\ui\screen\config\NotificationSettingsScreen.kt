package com.shuyi.discipline.ui.screen.config

import android.app.Application
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.shuyi.discipline.data.model.NotificationSettings
import com.shuyi.discipline.ui.components.PageTitleBar
import com.shuyi.discipline.ui.components.SettingsCard
import com.shuyi.discipline.ui.components.SettingsSwitchItem
import com.shuyi.discipline.ui.model.UiState

/**
 * 通知设置页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun NotificationSettingsScreen(
    onNavigateBack: () -> Unit
) {
    val scrollState = rememberScrollState()
    
    // 获取通知设置ViewModel
    val notificationSettingsViewModel: NotificationSettingsViewModel = viewModel(
        factory = NotificationSettingsViewModelFactory(LocalContext.current.applicationContext as Application)
    )
    val notificationSettingsState by notificationSettingsViewModel.notificationSettings.collectAsStateWithLifecycle()

    Scaffold(
        containerColor = MaterialTheme.colorScheme.background,
        topBar = {
            PageTitleBar(
                title = "通知设置",
                showBackButton = true,
                onBackClick = onNavigateBack
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(MaterialTheme.colorScheme.background)
                .padding(horizontal = 16.dp)
                .verticalScroll(scrollState),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            when (notificationSettingsState) {
                is UiState.Loading -> {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(200.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
                is UiState.Error -> {
                    Text(
                        text = "加载通知设置失败: ${(notificationSettingsState as UiState.Error).message}",
                        color = MaterialTheme.colorScheme.error
                    )
                }
                is UiState.Success -> {
                    val settings = (notificationSettingsState as UiState.Success<NotificationSettings>).data

                    SettingsCard(title = "应用通知") {
                        Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                            // 截屏通知
                            SettingsSwitchItem(
                                title = "截屏通知",
                                description = "每次截屏时显示通知",
                                checked = settings.isScreenshotNotificationEnabled,
                                onCheckedChange = { notificationSettingsViewModel.updateScreenshotNotificationEnabled(it) }
                            )

                            // 拼图通知
                            SettingsSwitchItem(
                                title = "拼图通知",
                                description = "拼图完成时显示通知",
                                checked = settings.isCollageNotificationEnabled,
                                onCheckedChange = { notificationSettingsViewModel.updateCollageNotificationEnabled(it) }
                            )
                        }
                    }

                    SettingsCard(title = "通知说明") {
                        Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                            Text(
                                text = "• 截屏通知：每次自动截屏时会显示一个简短的通知",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSecondaryContainer
                            )
                            Text(
                                text = "• 拼图通知：当每日拼图生成完成时会显示通知",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSecondaryContainer
                            )
                            Text(
                                text = "• 您可以在系统设置中进一步管理应用通知权限",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSecondaryContainer
                            )
                        }
                    }
                }
                else -> {
                    // 处理其他状态
                    Text("准备加载通知设置...")
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun NotificationSettingsScreenPreview() {
    MaterialTheme {
        NotificationSettingsScreen(
            onNavigateBack = {}
        )
    }
}
