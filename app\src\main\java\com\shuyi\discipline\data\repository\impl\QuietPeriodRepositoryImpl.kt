package com.shuyi.discipline.data.repository.impl

import com.shuyi.discipline.data.model.QuietPeriod
import com.shuyi.discipline.data.repository.QuietPeriodRepository
import com.shuyi.discipline.data.source.database.QuietPeriodDao
import kotlinx.coroutines.flow.Flow
import java.util.Calendar

/**
 * 免打扰时段仓库实现类
 */
class QuietPeriodRepositoryImpl(
    private val quietPeriodDao: QuietPeriodDao
) : QuietPeriodRepository {
    
    override fun getAllQuietPeriods(): Flow<List<QuietPeriod>> {
        return quietPeriodDao.getAllQuietPeriods()
    }
    
    override suspend fun saveQuietPeriod(quietPeriod: QuietPeriod): Long {
        quietPeriodDao.insertQuietPeriod(quietPeriod)
        return 1L // 返回行ID，如果方法不返回插入ID，可以返回成功指示值
    }
    
    override suspend fun deleteQuietPeriod(id: Int): Int {
        // 需要先获取QuietPeriod对象，然后删除
        val period = quietPeriodDao.getQuietPeriodById(id.toString())
        return if (period != null) {
            quietPeriodDao.deleteQuietPeriod(period)
            1 // 返回删除的行数
        } else {
            0
        }
    }
    
    override suspend fun updateQuietPeriod(quietPeriod: QuietPeriod): Int {
        quietPeriodDao.updateQuietPeriod(quietPeriod)
        return 1 // 返回更新的行数
    }
    
    // 这个方法不在接口中定义，但被实现类使用，保留为内部方法
    private suspend fun getQuietPeriodById(id: String): QuietPeriod? {
        return quietPeriodDao.getQuietPeriodById(id)
    }
    
    override suspend fun isInQuietPeriod(calendar: Calendar): Boolean {
        // 获取当前日期和时间
        val currentHour = calendar.get(Calendar.HOUR_OF_DAY)
        val currentMinute = calendar.get(Calendar.MINUTE)
        
        // 计算当前是工作日还是周末
        val dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK)
        val isWeekend = dayOfWeek == Calendar.SATURDAY || dayOfWeek == Calendar.SUNDAY
        val repeatPattern = if (isWeekend) 2 else 1 // 0=每日, 1=工作日, 2=周末
        
        // 获取今天适用的所有安静时段
        val todayQuietPeriods = quietPeriodDao.getQuietPeriodsForDay(repeatPattern)
        
        // 检查当前时间是否在任何安静时段内
        return todayQuietPeriods.any { period ->
            isTimeInPeriod(currentHour, currentMinute, period)
        }
    }
    
    /**
     * 检查时间是否在时段内
     */
    private fun isTimeInPeriod(hour: Int, minute: Int, period: QuietPeriod): Boolean {
        // 转换为分钟数，便于比较
        val currentTimeInMinutes = hour * 60 + minute
        val startTimeInMinutes = period.startHour * 60 + period.startMinute
        val endTimeInMinutes = period.endHour * 60 + period.endMinute
        
        // 处理跨天的情况，例如 23:00 - 06:00
        return if (endTimeInMinutes > startTimeInMinutes) {
            // 非跨天情况
            currentTimeInMinutes in startTimeInMinutes..endTimeInMinutes
        } else {
            // 跨天情况
            currentTimeInMinutes >= startTimeInMinutes || currentTimeInMinutes <= endTimeInMinutes
        }
    }
} 