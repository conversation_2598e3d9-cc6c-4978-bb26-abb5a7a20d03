package com.shuyi.discipline.data.source.database

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.shuyi.discipline.data.model.ScheduleRule
import kotlinx.coroutines.flow.Flow

/**
 * 调度规则数据访问对象
 */
@Dao
interface ScheduleRuleDao {
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertScheduleRule(scheduleRule: ScheduleRule)
    
    @Update
    suspend fun updateScheduleRule(scheduleRule: ScheduleRule)
    
    @Query("SELECT * FROM schedule_rules WHERE id = 1")
    suspend fun getScheduleRule(): ScheduleRule?
    
    @Query("SELECT * FROM schedule_rules WHERE id = 1")
    fun getScheduleRuleFlow(): Flow<ScheduleRule?>
    
    @Query("UPDATE schedule_rules SET lastCaptureTime = :timestamp WHERE id = 1")
    suspend fun updateLastCaptureTime(timestamp: Long)
    
    @Query("UPDATE schedule_rules SET isEnabled = :isEnabled WHERE id = 1")
    suspend fun updateServiceStatus(isEnabled: Boolean)
} 