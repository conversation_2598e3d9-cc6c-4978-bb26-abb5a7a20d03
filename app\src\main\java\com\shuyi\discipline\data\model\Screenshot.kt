package com.shuyi.discipline.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import java.util.UUID

/**
 * 截图数据模型
 */
@Entity(tableName = "screenshots")
data class Screenshot(
    @PrimaryKey val id: String = UUID.randomUUID().toString(),
    val timestamp: Long, // 截图时间戳
    val filePath: String, // 文件路径
    val quality: Int, // 截图质量设置
    val appPackage: String? = null, // 截图时的前台应用包名
    val isProcessed: Boolean = false // 是否已处理（用于拼图生成）
) 