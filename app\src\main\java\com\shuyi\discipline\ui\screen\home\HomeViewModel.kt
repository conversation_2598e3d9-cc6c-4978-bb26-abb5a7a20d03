package com.shuyi.discipline.ui.screen.home

import android.app.Application
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.net.Uri
import android.os.PowerManager
import android.provider.Settings
import android.widget.Toast
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.delay
import com.shuyi.discipline.data.model.Screenshot
import com.shuyi.discipline.data.repository.ScheduleRepository
import com.shuyi.discipline.data.repository.ScreenshotRepository
import com.shuyi.discipline.domain.service.ScreenshotService
import com.shuyi.discipline.domain.usecase.ScheduleSystemMonitorUseCase
import com.shuyi.discipline.ui.model.ScreenshotInfo
import com.shuyi.discipline.ui.model.ServiceStatus
import com.shuyi.discipline.ui.model.UiState
import com.shuyi.discipline.utils.MediaProjectionUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.UUID
import javax.inject.Inject

/**
 * 首页ViewModel
 */
@HiltViewModel
class HomeViewModel @Inject constructor(
    application: Application,
    private val screenshotRepository: ScreenshotRepository,
    private val scheduleRepository: ScheduleRepository,
    private val scheduleSystemMonitorUseCase: ScheduleSystemMonitorUseCase
) : AndroidViewModel(application) {

    private val context = getApplication<Application>()

    // 今日截图列表
    private val _todayScreenshots = MutableStateFlow<UiState<List<ScreenshotInfo>>>(UiState.Loading)
    val todayScreenshots: StateFlow<UiState<List<ScreenshotInfo>>> = _todayScreenshots

    // 服务状态
    private val _serviceStatus = MutableStateFlow<ServiceStatus>(ServiceStatus.Stopped)
    val serviceStatus: StateFlow<ServiceStatus> = _serviceStatus

    // 电池优化状态
    private val _batteryOptimizationIgnored = MutableStateFlow<Boolean>(false)
    val batteryOptimizationIgnored: StateFlow<Boolean> = _batteryOptimizationIgnored

    // 日期格式化器
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())

    // 服务状态广播接收器
    private val serviceStatusReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                ScreenshotService.ACTION_SERVICE_STATUS -> {
                    val isRunning = intent.getBooleanExtra("is_running", false)
                    val hasPermission = intent.getBooleanExtra("has_permission", false)
                    val errorMessage = intent.getStringExtra("errorMessage")
                    val permissionRevokedByUser = intent.getBooleanExtra("permission_revoked_by_user", false)

                    Timber.d("收到服务状态广播: 运行=$isRunning, 有权限=$hasPermission, 错误=$errorMessage, 用户撤销权限=$permissionRevokedByUser")

                    _serviceStatus.value = when {
                        isRunning && hasPermission -> ServiceStatus.Running
                        !hasPermission && permissionRevokedByUser -> {
                            Timber.w("用户主动撤销了屏幕录制权限，将状态设置为Stopped")
                            ServiceStatus.Stopped
                        }
                        !hasPermission -> ServiceStatus.Unauthorized
                        errorMessage != null -> ServiceStatus.Error(errorMessage)
                        else -> ServiceStatus.Stopped
                    }
                }
                "com.shuyi.discipline.PROJECTION_PERMISSION_DENIED" -> {
                    Timber.d("收到媒体投影权限被拒绝的广播")
                    // 用户拒绝了权限，将状态设置回Stopped
                    viewModelScope.launch {
                        try {
                            // 更新服务状态为禁用
                            scheduleRepository.updateServiceStatus(false)
                            _serviceStatus.value = ServiceStatus.Stopped
                            Timber.d("媒体投影权限被拒绝，服务状态已更新为Stopped")
                        } catch (e: Exception) {
                            Timber.e(e, "更新服务状态失败")
                            _serviceStatus.value = ServiceStatus.Error(e.message ?: "未知错误")
                        }
                    }
                }
                "com.shuyi.discipline.PROJECTION_PERMISSION_GRANTED" -> {
                    Timber.d("收到媒体投影权限已授予的广播")
                    // 用户同意了权限，确保状态为Running
                    viewModelScope.launch {
                        try {
                            // 更新服务状态为启用
                            scheduleRepository.updateServiceStatus(true)
                            _serviceStatus.value = ServiceStatus.Running
                            Timber.d("媒体投影权限已授予，服务状态已更新为Running")

                            // 刷新今日截图列表
                            loadTodayScreenshots()
                        } catch (e: Exception) {
                            Timber.e(e, "更新服务状态失败")
                            _serviceStatus.value = ServiceStatus.Error(e.message ?: "未知错误")
                        }
                    }
                }
                "com.shuyi.discipline.CHECK_BATTERY_OPTIMIZATION" -> {
                    Timber.d("收到检查电池优化状态的广播")
                    checkBatteryOptimizationStatus()
                }
                "com.shuyi.discipline.GET_BATTERY_OPTIMIZATION_INTENT" -> {
                    Timber.d("收到获取电池优化设置意图的广播")
                    // 发送电池优化设置意图
                    val batteryOptIntent = Intent("com.shuyi.discipline.BATTERY_OPTIMIZATION_INTENT")
                    batteryOptIntent.putExtra("intent", getRequestIgnoreBatteryOptimizationIntent())
                    context.sendBroadcast(batteryOptIntent)
                }
            }
        }
    }

    init {
        Timber.d("HomeViewModel初始化")

        // 注册服务状态广播接收器
        val filter = IntentFilter().apply {
            addAction(ScreenshotService.ACTION_SERVICE_STATUS)
            addAction("com.shuyi.discipline.PROJECTION_PERMISSION_DENIED")
            addAction("com.shuyi.discipline.PROJECTION_PERMISSION_GRANTED")
            addAction("com.shuyi.discipline.CHECK_BATTERY_OPTIMIZATION")
            addAction("com.shuyi.discipline.GET_BATTERY_OPTIMIZATION_INTENT")
        }

        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            context.registerReceiver(
                serviceStatusReceiver,
                filter,
                Context.RECEIVER_NOT_EXPORTED
            )
        } else {
            context.registerReceiver(serviceStatusReceiver, filter)
        }

        // 初始化时加载服务状态
        loadServiceStatus()

        // 检查电池优化状态
        checkBatteryOptimizationStatus()

        // 加载今日截图
        loadTodayScreenshots()
    }

    /**
     * 加载今日截图
     */
    fun loadTodayScreenshots() {
        viewModelScope.launch {
            try {
                _todayScreenshots.value = UiState.Loading

                // 获取今天的日期字符串
                val today = dateFormat.format(Date())
                Timber.d("正在加载今日截图，日期: $today")

                // 获取今日截图
                val screenshots = screenshotRepository.getScreenshotsForDay(today)
                Timber.d("获取到截图数量: ${screenshots.size}")

                // 检查截图文件是否存在
                val validScreenshots = screenshots.filter { screenshot ->
                    val file = File(screenshot.filePath)
                    val exists = file.exists()
                    if (!exists) {
                        Timber.w("截图文件不存在: ${screenshot.filePath}")
                    }
                    exists
                }

                Timber.d("有效截图数量: ${validScreenshots.size}")

                val screenshotInfos = validScreenshots.map { screenshot ->
                    Timber.d("截图详情 - ID: ${screenshot.id}, 路径: ${screenshot.filePath}, 时间戳: ${screenshot.timestamp}")

                    // 获取文件大小
                    val file = File(screenshot.filePath)
                    val fileSize = file.length()

                    ScreenshotInfo(
                        id = screenshot.id,
                        timestamp = screenshot.timestamp,
                        date = Date(screenshot.timestamp),
                        filePath = screenshot.filePath,
                        appPackageName = screenshot.appPackage,
                        appName = null,
                        size = fileSize
                    )
                }.sortedByDescending { it.timestamp } // 按时间降序排序，最新的在前面

                if (screenshotInfos.isEmpty()) {
                    Timber.d("今日没有截图")
                    _todayScreenshots.value = UiState.Empty
                } else {
                    Timber.d("今日截图加载成功，共 ${screenshotInfos.size} 张")
                    _todayScreenshots.value = UiState.Success(screenshotInfos)
                }
            } catch (e: Exception) {
                Timber.e(e, "加载今日截图失败: ${e.message}")
                _todayScreenshots.value = UiState.Error(e.message ?: "未知错误")
            }
        }
    }

    /**
     * 刷新今日截图
     */
    fun refreshTodayScreenshots() {
        Timber.d("手动刷新今日截图")
        loadTodayScreenshots()
    }

    /**
     * 执行测试截图（临时方法，用于调试）
     */
    fun takeTestScreenshot() {
        viewModelScope.launch {
            try {
                Timber.d("执行测试截图")
                // 当前时间戳
                val timestamp = System.currentTimeMillis()

                // 创建测试截图目录 - 直接使用内部存储
                val baseDir = context.filesDir
                val screenshotDir = File(baseDir, "screenshots")
                if (!screenshotDir.exists()) {
                    val created = screenshotDir.mkdirs()
                    Timber.d("创建截图目录结果: $created, 路径: ${screenshotDir.absolutePath}")
                } else {
                    Timber.d("截图目录已存在: ${screenshotDir.absolutePath}")
                }

                // 检查目录是否可写
                if (!screenshotDir.canWrite()) {
                    Timber.e("截图目录不可写: ${screenshotDir.absolutePath}")
                    return@launch
                }

                // 创建测试截图文件名
                val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
                val fileName = "test_screenshot_${timeStamp}_${UUID.randomUUID().toString().substring(0, 8)}.jpg"
                val file = File(screenshotDir, fileName)

                Timber.d("准备创建测试截图文件: ${file.absolutePath}")

                // 创建一个更大的测试图片，并添加时间标记
                val bitmap = Bitmap.createBitmap(400, 400, Bitmap.Config.ARGB_8888)
                val canvas = Canvas(bitmap)

                // 绘制渐变背景
                val paint = android.graphics.Paint()
                val gradient = android.graphics.LinearGradient(
                    0f, 0f, 400f, 400f,
                    intArrayOf(Color.BLUE, Color.CYAN, Color.GREEN),
                    null, android.graphics.Shader.TileMode.CLAMP
                )
                paint.shader = gradient
                canvas.drawRect(0f, 0f, 400f, 400f, paint)

                // 添加时间文本
                paint.shader = null
                paint.color = Color.WHITE
                paint.textSize = 40f
                paint.isFakeBoldText = true
                paint.isAntiAlias = true
                val text = SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault()).format(Date())
                canvas.drawText(text, 20f, 200f, paint)

                // 添加测试标记
                paint.textSize = 30f
                canvas.drawText("测试截图", 20f, 250f, paint)

                // 保存到文件
                try {
                    FileOutputStream(file).use { out ->
                        bitmap.compress(Bitmap.CompressFormat.JPEG, 90, out)
                        out.flush()
                    }
                    Timber.d("测试截图保存成功: ${file.absolutePath}")

                    // 创建截图记录
                    val screenshot = Screenshot(
                        timestamp = timestamp,
                        filePath = file.absolutePath,
                        quality = 90,
                        appPackage = "com.shuyi.discipline.test"
                    )

                    // 保存截图记录
                    screenshotRepository.saveScreenshot(screenshot)
                    Timber.d("测试截图记录保存成功")

                    // 刷新截图列表
                    loadTodayScreenshots()
                } catch (e: Exception) {
                    Timber.e(e, "测试截图保存失败: ${e.message}")
                } finally {
                    bitmap.recycle()
                }
            } catch (e: Exception) {
                Timber.e(e, "测试截图失败: ${e.message}")
            }
        }
    }

    /**
     * 加载服务状态
     */
    fun loadServiceStatus() {
        viewModelScope.launch {
            try {
                val rule = scheduleRepository.getScheduleRule()
                val hasMediaProjectionPermission = MediaProjectionUtils.hasPermission(context)
                val isServiceRunning = isServiceRunning()

                Timber.d("加载服务状态，规则: ${rule?.toString()}, 启用状态: ${rule?.isEnabled}, MediaProjection权限: $hasMediaProjectionPermission, 服务运行: $isServiceRunning")

                _serviceStatus.value = when {
                    // 如果MediaProjection权限被撤销，无论其他状态如何，都应该显示为停止状态
                    !hasMediaProjectionPermission && rule?.isEnabled == true -> {
                        Timber.w("MediaProjection权限已被撤销，但规则仍为启用状态，将服务状态设置为Stopped")
                        // 同步更新规则状态，确保数据一致性
                        scheduleRepository.updateServiceStatus(false)
                        ServiceStatus.Stopped
                    }
                    // 规则启用、有权限且服务运行时，显示为运行状态
                    rule?.isEnabled == true && hasMediaProjectionPermission && isServiceRunning -> {
                        ServiceStatus.Running
                    }
                    // 规则启用、有权限但服务未运行时，显示为未授权状态（需要重新启动服务）
                    rule?.isEnabled == true && hasMediaProjectionPermission && !isServiceRunning -> {
                        ServiceStatus.Unauthorized
                    }
                    // 规则启用但没有MediaProjection权限时，显示为停止状态（权限被撤销的情况）
                    rule?.isEnabled == true && !hasMediaProjectionPermission -> {
                        Timber.w("规则已启用但MediaProjection权限缺失，将服务状态设置为Stopped")
                        // 同步更新规则状态，确保数据一致性
                        scheduleRepository.updateServiceStatus(false)
                        ServiceStatus.Stopped
                    }
                    // 其他情况（规则未启用等）显示为停止状态
                    else -> {
                        ServiceStatus.Stopped
                    }
                }

                Timber.d("服务状态已更新为: ${_serviceStatus.value}")
            } catch (e: Exception) {
                Timber.e(e, "加载服务状态失败: ${e.message}")
                _serviceStatus.value = ServiceStatus.Error(e.message ?: "未知错误")
            }
        }
    }

    /**
     * 检查服务是否正在运行
     */
    private fun isServiceRunning(): Boolean {
        // 这里我们使用一个简单的方法来检查服务是否在运行
        // 在实际应用中，可能需要更复杂的检查方法
        try {
            // 尝试发送一个检查状态的意图到服务
            val serviceIntent = Intent(context, ScreenshotService::class.java).apply {
                action = ScreenshotService.ACTION_CHECK_STATUS
            }
            context.startService(serviceIntent)

            // 首次打开应用时，我们假设服务未运行
            // 如果服务实际运行，它会通过广播通知我们
            return false
        } catch (e: Exception) {
            Timber.e(e, "检查服务运行状态失败")
            return false
        }
    }

    /**
     * 切换服务状态
     */
    fun toggleServiceStatus() {
        viewModelScope.launch {
            try {
                when (val status = _serviceStatus.value) {
                    ServiceStatus.Running -> {
                        Timber.d("当前服务正在运行，准备停止")
                        // 更新服务状态
                        scheduleRepository.updateServiceStatus(false)

                        // 停止服务
                        val serviceIntent = Intent(context, ScreenshotService::class.java).apply {
                            action = ScreenshotService.ACTION_STOP_SERVICE
                        }
                        context.startService(serviceIntent)

                        _serviceStatus.value = ServiceStatus.Stopped
                        Timber.d("服务已停止")
                    }
                    ServiceStatus.Stopped, is ServiceStatus.Error -> {
                        Timber.d("当前服务已停止，准备启动")
                        // 更新服务状态
                        scheduleRepository.updateServiceStatus(true)

                        // 先更新UI状态为Running，表示功能已开启
                        _serviceStatus.value = ServiceStatus.Running
                        Timber.d("服务状态已更新为Running")

                        // 🎯 立即执行监控检查，确保异常记录及时结束
                        scheduleSystemMonitorUseCase.executeImmediateCheck()

                        // 请求媒体投影权限
                        requestMediaProjectionPermission()
                    }
                    ServiceStatus.Unauthorized -> {
                        Timber.d("当前服务未授权，重新请求权限")
                        // 更新服务状态
                        scheduleRepository.updateServiceStatus(true)

                        // 先更新UI状态为Running，表示功能已开启
                        _serviceStatus.value = ServiceStatus.Running
                        Timber.d("服务状态已更新为Running")

                        // 🎯 立即执行监控检查，确保异常记录及时结束
                        scheduleSystemMonitorUseCase.executeImmediateCheck()

                        // 重新请求权限
                        requestMediaProjectionPermission()
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "切换服务状态失败")
                _serviceStatus.value = ServiceStatus.Error(e.message ?: "未知错误")
            }
        }
    }

    /**
     * 请求媒体投影权限
     */
    private fun requestMediaProjectionPermission() {
        // 不再在这里修改服务状态，保持当前状态
        // 发送广播通知MainActivity请求权限
        val intent = Intent("com.shuyi.discipline.REQUEST_PROJECTION")
        context.sendBroadcast(intent)
        Timber.d("发送请求媒体投影权限的广播")
    }

    /**
     * 检查电池优化状态
     */
    fun checkBatteryOptimizationStatus() {
        viewModelScope.launch {
            try {
                val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
                val packageName = context.packageName

                val isIgnoringBatteryOptimizations = if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
                    powerManager.isIgnoringBatteryOptimizations(packageName)
                } else {
                    true // 在Android 6.0以下版本，不需要请求忽略电池优化
                }

                _batteryOptimizationIgnored.value = isIgnoringBatteryOptimizations
                Timber.d("电池优化状态: ${if (isIgnoringBatteryOptimizations) "已忽略" else "未忽略"}")
            } catch (e: Exception) {
                Timber.e(e, "检查电池优化状态失败: ${e.message}")
            }
        }
    }

    /**
     * 请求忽略电池优化
     */
    fun requestIgnoreBatteryOptimization() {
        // 发送广播通知MainActivity请求忽略电池优化
        val intent = Intent("com.shuyi.discipline.REQUEST_IGNORE_BATTERY_OPTIMIZATION")
        context.sendBroadcast(intent)
        Timber.d("发送请求忽略电池优化的广播")
    }

    /**
     * 获取请求忽略电池优化的Intent
     */
    private fun getRequestIgnoreBatteryOptimizationIntent(): Intent? {
        return if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.M) {
            val packageName = context.packageName
            Intent(android.provider.Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                data = android.net.Uri.parse("package:$packageName")
            }
        } else {
            null
        }
    }

    /**
     * 触发实际截图
     */
    fun triggerActualScreenshot() {
        viewModelScope.launch {
            try {
                Timber.d("手动触发实际截图")

                // 检查服务状态
                if (_serviceStatus.value != ServiceStatus.Running) {
                    Timber.d("服务未运行，需要先启动服务")
                    Toast.makeText(context, "请先启动截图服务", Toast.LENGTH_SHORT).show()
                    return@launch
                }

                // 发送截图命令到服务
                val serviceIntent = Intent(context, ScreenshotService::class.java).apply {
                    action = ScreenshotService.ACTION_TAKE_SCREENSHOT
                }
                context.startService(serviceIntent)

                Toast.makeText(context, "已触发截图，请稍后刷新", Toast.LENGTH_SHORT).show()

                // 等待一秒后刷新截图列表
                delay(1000)
                loadTodayScreenshots()
            } catch (e: Exception) {
                Timber.e(e, "触发实际截图失败: ${e.message}")
                Toast.makeText(context, "截图失败: ${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * ViewModel被清除时调用
     */
    override fun onCleared() {
        super.onCleared()
        Timber.d("HomeViewModel被清除")

        // 注销广播接收器
        try {
            context.unregisterReceiver(serviceStatusReceiver)
            Timber.d("服务状态广播接收器已注销")
        } catch (e: Exception) {
            Timber.e(e, "注销服务状态广播接收器失败")
        }
    }
}
