<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限加固 - 自动截屏</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="mobile-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-bar-time">9:41</div>
            <div class="status-bar-icons">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 10a6 6 0 0 0-12 0v11h12V10z"/><path d="M9 2v1"/><path d="M15 2v1"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z"/><path d="M5 10h14"/><path d="M9 16h6"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 4h14a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1z"/><path d="M4 8h16"/><path d="M8 4v16"/></svg>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content-area">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold">权限管理</h1>
            </div>

            <!-- 权限状态概览 -->
            <div class="card mb-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold">权限状态</h2>
                    <span class="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium">需要授权</span>
                </div>

                <div class="p-3 bg-blue-50 rounded-lg">
                    <div class="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-500 mt-1 mr-3"><circle cx="12" cy="12" r="10"/><path d="M12 16v-4"/><path d="M12 8h.01"/></svg>
                        <div>
                            <p class="text-blue-700 font-medium">为什么需要这些权限？</p>
                            <p class="text-blue-600 text-sm mt-1">自动截屏应用需要特定权限才能在后台稳定运行并执行截屏操作。请确保授予所有必要权限以获得最佳体验。</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 必要权限 -->
            <div class="card mb-6">
                <h2 class="text-lg font-semibold mb-4">必要权限</h2>

                <!-- 后台运行权限 -->
                <div class="permission-item">
                    <div class="permission-info">
                        <div class="permission-title">后台运行</div>
                        <div class="text-sm text-gray-500">允许应用在后台持续运行</div>
                    </div>
                    <div class="flex items-center">
                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs mr-2">已授权</span>
                        <button class="p-2 bg-gray-100 rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 18l6-6-6-6"/></svg>
                        </button>
                    </div>
                </div>

                <!-- 截屏权限 -->
                <div class="permission-item">
                    <div class="permission-info">
                        <div class="permission-title">屏幕截图</div>
                        <div class="text-sm text-gray-500">允许应用捕获屏幕内容</div>
                    </div>
                    <div class="flex items-center">
                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs mr-2">已授权</span>
                        <button class="p-2 bg-gray-100 rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 18l6-6-6-6"/></svg>
                        </button>
                    </div>
                </div>

                <!-- 存储权限 -->
                <div class="permission-item">
                    <div class="permission-info">
                        <div class="permission-title">存储空间</div>
                        <div class="text-sm text-gray-500">允许应用保存截图到设备</div>
                    </div>
                    <div class="flex items-center">
                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs mr-2">已授权</span>
                        <button class="p-2 bg-gray-100 rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 18l6-6-6-6"/></svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 高级权限 -->
            <div class="card mb-6">
                <h2 class="text-lg font-semibold mb-4">高级权限</h2>

                <!-- 无障碍服务 -->
                <div class="permission-item">
                    <div class="permission-info">
                        <div class="permission-title">无障碍服务</div>
                        <div class="text-sm text-gray-500">允许应用在特定场景下自动操作</div>
                    </div>
                    <div class="flex items-center">
                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs mr-2">未授权</span>
                        <button class="p-2 bg-gray-100 rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 18l6-6-6-6"/></svg>
                        </button>
                    </div>
                </div>

                <!-- 悬浮窗权限 -->
                <div class="permission-item">
                    <div class="permission-info">
                        <div class="permission-title">悬浮窗</div>
                        <div class="text-sm text-gray-500">允许应用在其他应用上层显示内容</div>
                    </div>
                    <div class="flex items-center">
                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs mr-2">已授权</span>
                        <button class="p-2 bg-gray-100 rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 18l6-6-6-6"/></svg>
                        </button>
                    </div>
                </div>

                <!-- 自启动权限 -->
                <div class="permission-item">
                    <div class="permission-info">
                        <div class="permission-title">自动启动</div>
                        <div class="text-sm text-gray-500">允许应用在设备启动时自动运行</div>
                    </div>
                    <div class="flex items-center">
                        <span class="px-2 py-1 bg-yellow-100 text-yellow-800 rounded-full text-xs mr-2">未授权</span>
                        <button class="p-2 bg-gray-100 rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 18l6-6-6-6"/></svg>
                        </button>
                    </div>
                </div>

                <!-- 电池优化排除 -->
                <div class="permission-item">
                    <div class="permission-info">
                        <div class="permission-title">电池优化排除</div>
                        <div class="text-sm text-gray-500">允许应用不受电池优化限制</div>
                    </div>
                    <div class="flex items-center">
                        <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs mr-2">已授权</span>
                        <button class="p-2 bg-gray-100 rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 18l6-6-6-6"/></svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 权限问题排查 -->
            <div class="card mb-6">
                <h2 class="text-lg font-semibold mb-4">权限问题排查</h2>

                <div class="p-3 bg-yellow-50 rounded-lg mb-4">
                    <div class="flex items-start">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-yellow-500 mt-1 mr-3"><path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"/><path d="M12 9v4"/><path d="M12 17h.01"/></svg>
                        <div>
                            <p class="text-yellow-700 font-medium">检测到权限问题</p>
                            <p class="text-yellow-600 text-sm mt-1">部分权限未授予，可能导致应用无法正常工作。请点击下方按钮一键修复。</p>
                        </div>
                    </div>
                </div>

                <button class="w-full py-3 bg-blue-500 text-white rounded-lg font-medium">
                    一键授予所有权限
                </button>
            </div>

            <!-- 设备信息 -->
            <div class="card">
                <h2 class="text-lg font-semibold mb-4">设备信息</h2>

                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-500">设备型号</span>
                        <span>iPhone 15 Pro</span>
                    </div>

                    <div class="flex justify-between">
                        <span class="text-gray-500">系统版本</span>
                        <span>iOS 17.2</span>
                    </div>

                    <div class="flex justify-between">
                        <span class="text-gray-500">应用版本</span>
                        <span>1.0.0</span>
                    </div>

                    <div class="flex justify-between">
                        <span class="text-gray-500">设备ID</span>
                        <span class="text-gray-800">XXXX-XXXX-XXXX-XXXX</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航栏 -->
        <div class="tab-bar">
            <div class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9 22 9 12 15 12 15 22"/></svg>
                <span>首页</span>
            </div>
            <div class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><circle cx="9" cy="9" r="2"/><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/></svg>
                <span>截图</span>
            </div>
            <div class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><path d="M3 15h18"/><path d="M9 9h.01"/><path d="M15 9h.01"/></svg>
                <span>拼图</span>
            </div>
            <div class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 20h9"/><path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"/></svg>
                <span>状态</span>
            </div>
            <div class="tab-item active">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/></svg>
                <span>设置</span>
            </div>
        </div>
    </div>
</body>
</html>