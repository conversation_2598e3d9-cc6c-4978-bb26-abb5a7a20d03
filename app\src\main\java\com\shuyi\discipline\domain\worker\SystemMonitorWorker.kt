package com.shuyi.discipline.domain.worker

import android.app.ActivityManager
import android.content.Context
import android.content.Intent
import androidx.hilt.work.HiltWorker
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.shuyi.discipline.data.model.MonitorStatus
import com.shuyi.discipline.data.model.MonitorType
import com.shuyi.discipline.data.repository.SystemMonitorRepository
import com.shuyi.discipline.domain.service.DisciplineAccessibilityService
import com.shuyi.discipline.domain.service.ScreenshotService
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import timber.log.Timber

/**
 * 系统监控工作器
 * 定期检查应用后台运行状态和截图服务状态
 */
@HiltWorker
class SystemMonitorWorker @AssistedInject constructor(
    @Assisted private val context: Context,
    @Assisted workerParams: WorkerParameters,
    private val systemMonitorRepository: SystemMonitorRepository
) : CoroutineWorker(context, workerParams) {

    override suspend fun doWork(): Result {
        return try {
            Timber.d("开始执行系统监控检查")

            // 检查应用后台运行状态
            val isAppInForeground = checkAppBackgroundRunningStatus()

            // 只有当应用在前台运行时，才检查截图服务状态
            // 这样可以避免app未运行时错误地将时间计入截图服务未运行时长
            if (isAppInForeground) {
                Timber.d("应用在前台运行，检查截图服务状态")
                checkScreenshotServiceStatus()
            } else {
                Timber.d("应用不在前台，跳过截图服务状态检查")
                // 当应用不在前台时，如果有截图服务的异常记录，也需要暂停它
                // 因为app都没运行，截图服务的状态检查就没有意义
                pauseScreenshotServiceMonitoring()
            }

            Timber.d("系统监控检查完成")
            Result.success()
        } catch (e: Exception) {
            Timber.e(e, "系统监控检查失败")
            Result.failure()
        }
    }

    /**
     * 检查应用后台运行状态
     * @return 返回应用是否在前台运行
     */
    private suspend fun checkAppBackgroundRunningStatus(): Boolean {
        try {
            // 检查应用是否在前台运行
            val isAppInForeground = isAppInForeground()
            Timber.d("应用前台状态检查结果: $isAppInForeground")

            // 获取最新的未完成异常记录
            val latestRecord = systemMonitorRepository.getLatestUnfinishedRecord(
                MonitorType.APP_BACKGROUND_RUNNING,
                MonitorStatus.ABNORMAL
            )
            Timber.d("最新未完成的App后台运行异常记录: ${latestRecord?.id}")

            // 检查应用进程是否正在运行（包括前台和后台）
            val isAppProcessRunning = isAppProcessRunning()
            Timber.d("应用进程运行状态: $isAppProcessRunning")

            // 关键逻辑修改：当应用进程不存在时，记录为未运行异常
            // 这样可以捕获到通过后台任务列表强制退出应用的情况
            if (!isAppProcessRunning) {
                // 应用进程真正未运行，检查是否已有异常记录
                if (latestRecord == null) {
                    // 没有异常记录，创建新的异常记录
                    val recordId = systemMonitorRepository.recordAbnormalStart(
                        MonitorType.APP_BACKGROUND_RUNNING,
                        "应用未运行，进程已停止或被系统强制关闭",
                        false
                    )
                    Timber.d("应用未运行异常开始记录，记录ID: $recordId")
                } else {
                    Timber.d("应用未运行，但已有异常记录: ${latestRecord.id}，无需重复创建")
                }
            } else {
                // 应用进程正在运行（前台或后台），如果有异常记录则结束它
                latestRecord?.let { record ->
                    val currentTime = System.currentTimeMillis()
                    val duration = currentTime - record.startTime
                    systemMonitorRepository.finishRecord(
                        record.id,
                        currentTime,
                        duration,
                        MonitorStatus.RECOVERED
                    )
                    val statusDesc = if (isAppInForeground) "前台" else "后台"
                    Timber.d("应用未运行异常已恢复（应用在${statusDesc}运行），记录ID: ${record.id}, 持续时长: ${duration}ms")
                } ?: run {
                    val statusDesc = if (isAppInForeground) "前台" else "后台"
                    Timber.d("应用在${statusDesc}运行，且没有未完成的异常记录，状态正常")
                }
            }
            return isAppInForeground
        } catch (e: Exception) {
            Timber.e(e, "检查应用后台运行状态失败")
            return false
        }
    }

    /**
     * 检查截图服务状态
     */
    private suspend fun checkScreenshotServiceStatus() {
        try {
            // 🎯 检查应用是否刚启动
            val appLifecyclePrefs = context.getSharedPreferences("app_lifecycle", Context.MODE_PRIVATE)
            val appFirstLaunchTime = appLifecyclePrefs.getLong("app_first_launch_time", 0)
            val currentTime = System.currentTimeMillis()
            
            if (appFirstLaunchTime > 0) {
                val timeSinceFirstLaunch = currentTime - appFirstLaunchTime
                // 如果应用启动不到10秒，跳过截图服务检查，给服务足够的初始化时间
                if (timeSinceFirstLaunch < 10 * 1000L) {
                    Timber.d("应用刚启动${timeSinceFirstLaunch}ms，跳过截图服务状态检查，避免误报")
                    return
                }
            }

            // 检查截图服务是否正在运行
            val isServiceRunning = isScreenshotServiceRunning()
            Timber.d("截图服务运行状态检查结果: $isServiceRunning")

            // 获取最新的未完成异常记录
            val latestRecord = systemMonitorRepository.getLatestUnfinishedRecord(
                MonitorType.SCREENSHOT_FUNCTION,
                MonitorStatus.ABNORMAL
            )
            Timber.d("截图服务最新异常记录: ${latestRecord?.id}")

            if (!isServiceRunning) {
                // 服务未运行，检查是否已有异常记录
                if (latestRecord == null) {
                    // 没有异常记录，创建新的异常记录
                    val recordId = systemMonitorRepository.recordAbnormalStart(
                        MonitorType.SCREENSHOT_FUNCTION,
                        "截图功能异常，可能缺少权限或服务停止",
                        false
                    )
                    Timber.d("截图功能异常开始记录，记录ID: $recordId（这可能是首次启动时的正常状态）")
                } else {
                    Timber.d("截图服务未运行，但已有异常记录: ${latestRecord.id}")
                }
            } else {
                // 服务正在运行，如果有异常记录则结束它
                latestRecord?.let { record ->
                    val duration = currentTime - record.startTime
                    systemMonitorRepository.finishRecord(
                        record.id,
                        currentTime,
                        duration,
                        MonitorStatus.RECOVERED
                    )
                    Timber.d("截图功能异常已恢复，记录ID: ${record.id}, 持续时长: ${duration}ms")
                }
            }
        } catch (e: Exception) {
            Timber.e(e, "检查截图服务状态失败")
        }
    }

    /**
     * 暂停截图服务监控
     * 当应用不在前台时，暂停截图服务的异常记录，避免错误计入截图服务未运行时长
     */
    private suspend fun pauseScreenshotServiceMonitoring() {
        try {
            // 获取最新的未完成截图服务异常记录
            val latestRecord = systemMonitorRepository.getLatestUnfinishedRecord(
                MonitorType.SCREENSHOT_FUNCTION,
                MonitorStatus.ABNORMAL
            )

            latestRecord?.let { record ->
                val currentTime = System.currentTimeMillis()
                val duration = currentTime - record.startTime
                systemMonitorRepository.finishRecord(
                    record.id,
                    currentTime,
                    duration,
                    MonitorStatus.RECOVERED // 使用恢复状态，表示因为app不在前台而结束监控
                )
                Timber.d("应用不在前台，结束截图服务异常记录（避免错误计入截图服务未运行时长），记录ID: ${record.id}, 持续时长: ${duration}ms")
            } ?: run {
                Timber.d("应用不在前台，但没有未完成的截图服务异常记录")
            }
        } catch (e: Exception) {
            Timber.e(e, "暂停截图服务监控失败")
        }
    }

    /**
     * 检查应用是否在前台运行
     */
    private fun isAppInForeground(): Boolean {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val runningAppProcesses = activityManager.runningAppProcesses

            runningAppProcesses?.any { processInfo ->
                processInfo.processName == context.packageName &&
                processInfo.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND
            } ?: false
        } catch (e: Exception) {
            Timber.e(e, "检查应用前台状态失败")
            false
        }
    }

    /**
     * 检查应用进程是否正在运行（包括前台和后台）
     */
    private fun isAppProcessRunning(): Boolean {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            val runningAppProcesses = activityManager.runningAppProcesses

            val isRunning = runningAppProcesses?.any { processInfo ->
                processInfo.processName == context.packageName
            } ?: false

            Timber.d("应用进程运行状态检查: $isRunning")
            isRunning
        } catch (e: Exception) {
            Timber.e(e, "检查应用进程状态失败")
            false
        }
    }

    /**
     * 检查截图服务是否正在运行
     *
     * 使用多种方法来检测服务状态：
     * 1. SharedPreferences 状态标记（主要方法）
     * 2. ActivityManager.getRunningServices()（辅助方法）
     */
    private fun isScreenshotServiceRunning(): Boolean {
        return try {
            // 方法1：检查 SharedPreferences 中的服务状态标记
            val sharedPrefs = context.getSharedPreferences("service_status", Context.MODE_PRIVATE)
            val isRunningByPrefs = sharedPrefs.getBoolean("screenshot_service_running", false)
            val lastUpdateTime = sharedPrefs.getLong("last_update_time", 0)
            val currentTime = System.currentTimeMillis()

            // 🎯 移除首次启动的特殊处理，直接使用状态值
            // 因为现在在应用启动时已经正确初始化了状态

            // 检查状态更新时间，如果超过5分钟没有更新，认为可能不准确
            val isStatusFresh = (currentTime - lastUpdateTime) < 5 * 60 * 1000L // 5分钟

            if (!isStatusFresh && lastUpdateTime > 0) {
                Timber.w("服务状态可能过期，上次更新时间: ${java.util.Date(lastUpdateTime)}")
            }

            // 方法2：使用 ActivityManager 检查服务状态（作为辅助验证）
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
            @Suppress("DEPRECATION")
            val runningServices = activityManager.getRunningServices(Integer.MAX_VALUE)

            val isRunningByActivityManager = runningServices?.any { serviceInfo ->
                serviceInfo.service.className == ScreenshotService::class.java.name
            } ?: false

            Timber.d("服务状态检测结果:")
            Timber.d("  SharedPreferences: $isRunningByPrefs (更新时间: ${if (lastUpdateTime > 0) "${(currentTime - lastUpdateTime) / 1000}秒前" else "从未更新"})")
            Timber.d("  ActivityManager: $isRunningByActivityManager")
            Timber.d("  状态是否新鲜: $isStatusFresh")

            // 综合判断：
            // 1. 如果 SharedPreferences 状态是新鲜的，优先使用它
            // 2. 如果状态不新鲜，使用 ActivityManager 的结果
            // 3. 如果两者都显示服务未运行，则认为服务确实未运行
            val finalResult = if (isStatusFresh) {
                Timber.d("使用SharedPreferences的新鲜状态: $isRunningByPrefs")
                isRunningByPrefs
            } else {
                Timber.d("SharedPreferences状态不新鲜，使用ActivityManager结果: $isRunningByActivityManager")
                // 状态不新鲜时，使用 ActivityManager 结果，并更新 SharedPreferences
                if (isRunningByActivityManager != isRunningByPrefs) {
                    Timber.d("更新SharedPreferences状态: $isRunningByPrefs -> $isRunningByActivityManager")
                    sharedPrefs.edit()
                        .putBoolean("screenshot_service_running", isRunningByActivityManager)
                        .putLong("last_update_time", currentTime)
                        .apply()
                }
                isRunningByActivityManager
            }

            Timber.d("最终服务状态检测结果: $finalResult")
            finalResult

        } catch (e: Exception) {
            Timber.e(e, "检查截图服务状态失败")
            false
        }
    }
}