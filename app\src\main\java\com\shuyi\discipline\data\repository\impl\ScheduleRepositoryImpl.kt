package com.shuyi.discipline.data.repository.impl

import com.shuyi.discipline.data.model.ScheduleRule
import com.shuyi.discipline.data.repository.ScheduleRepository
import com.shuyi.discipline.data.source.database.ScheduleRuleDao
import kotlinx.coroutines.flow.Flow
import timber.log.Timber
import kotlin.math.ln
import kotlin.random.Random

/**
 * 调度规则仓库实现类
 */
class ScheduleRepositoryImpl(
    private val scheduleRuleDao: ScheduleRuleDao
) : ScheduleRepository {

    override suspend fun getScheduleRule(): ScheduleRule? {
        return scheduleRuleDao.getScheduleRule() ?: createDefaultScheduleRule()
    }

    override fun getScheduleRuleFlow(): Flow<ScheduleRule?> {
        return scheduleRuleDao.getScheduleRuleFlow()
    }

    override suspend fun saveScheduleRule(scheduleRule: ScheduleRule) {
        scheduleRuleDao.insertScheduleRule(scheduleRule)
    }

    override suspend fun updateLastCaptureTime(timestamp: Long) {
        scheduleRuleDao.updateLastCaptureTime(timestamp)
    }

    override suspend fun updateServiceStatus(isEnabled: Boolean) {
        scheduleRuleDao.updateServiceStatus(isEnabled)
    }

    override suspend fun calculateNextCaptureTime(): Long {
        val rule = getScheduleRule() ?: createDefaultScheduleRule().also {
            saveScheduleRule(it)
        }

        val now = System.currentTimeMillis()
        val lastCaptureTime = rule.lastCaptureTime.takeIf { it > 0 } ?: now

        return if (rule.isRandom) {
            // 基础间隔时间（毫秒）= 分钟 * 60 * 1000 + 秒 * 1000
            val baseInterval = rule.intervalMinutes * 60 * 1000L + rule.intervalSeconds * 1000L

            // 随机范围（毫秒）= 分钟 * 60 * 1000 + 秒 * 1000
            val randomRangeMs = rule.randomRangeMinutes * 60 * 1000L + rule.randomRangeSeconds * 1000L

            // 在基础间隔时间上添加随机偏移
            val randomOffset = if (randomRangeMs > 0) {
                Random.nextLong(-randomRangeMs, randomRangeMs)
            } else 0L

            // 计算下一次截图时间
            val nextTime = lastCaptureTime + baseInterval + randomOffset

            // 确保下一个时间点不早于当前时间
            if (nextTime < now) now + 10_000 else nextTime
        } else {
            // 固定间隔（毫秒）= 分钟 * 60 * 1000 + 秒 * 1000
            val interval = rule.intervalMinutes * 60 * 1000L + rule.intervalSeconds * 1000L
            val nextTime = lastCaptureTime + interval

            // 确保下一个时间点不早于当前时间
            if (nextTime < now) now + 10_000 else nextTime
        }.also {
            Timber.d("计算下次截图时间: ${java.util.Date(it)}, 间隔: ${(it - lastCaptureTime) / 1000}秒")
        }
    }

    private suspend fun createDefaultScheduleRule(): ScheduleRule {
        return ScheduleRule(
            intervalMinutes = 15, // 默认15分钟间隔
            intervalSeconds = 0, // 默认0秒
            isRandom = false,
            randomRange = 0, // 保留兼容性
            randomRangeMinutes = 0, // 默认随机范围0分钟
            randomRangeSeconds = 0, // 默认随机范围0秒
            qualitySetting = 70, // 默认70%质量
            resolutionSetting = 100, // 默认100%分辨率
            blurSetting = 0, // 默认无模糊
            lastCaptureTime = System.currentTimeMillis()
        ).also { saveScheduleRule(it) }
    }
}