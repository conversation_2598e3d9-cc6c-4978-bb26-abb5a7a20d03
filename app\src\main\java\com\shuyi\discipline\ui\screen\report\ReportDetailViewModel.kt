package com.shuyi.discipline.ui.screen.report

import android.app.Application
import android.content.Intent
import android.net.Uri
import androidx.core.content.FileProvider
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.shuyi.discipline.data.model.Screenshot
import com.shuyi.discipline.data.repository.CollageReportRepository
import com.shuyi.discipline.data.repository.ScreenshotRepository
import com.shuyi.discipline.domain.model.ScreenshotDetail
import com.shuyi.discipline.ui.model.UiState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

/**
 * 报告详情ViewModel
 */
/**
 * 排序方式枚举
 */
enum class SortOrder {
    ASCENDING, // 升序（从早到晚）
    DESCENDING // 降序（从晚到早）
}

class ReportDetailViewModel constructor(
    private val application: Application,
    private val screenshotRepository: ScreenshotRepository,
    private val collageReportRepository: CollageReportRepository
) : AndroidViewModel(application) {

    private val _report = MutableStateFlow<UiState<ReportDetail>>(UiState.Loading)
    val report: StateFlow<UiState<ReportDetail>> = _report

    private val _screenshots = MutableStateFlow<UiState<List<ScreenshotDetail>>>(UiState.Loading)
    val screenshots: StateFlow<UiState<List<ScreenshotDetail>>> = _screenshots

    // 排序方式，默认降序（最新的在前面）
    private val _sortOrder = MutableStateFlow(SortOrder.DESCENDING)
    val sortOrder: StateFlow<SortOrder> = _sortOrder

    // 原始截图列表（未排序）
    private var originalScreenshots = listOf<ScreenshotDetail>()

    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    private val timeFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())

    /**
     * 加载指定日期的截图列表
     */
    fun loadScreenshotsForDate(dateString: String) {
        viewModelScope.launch {
            try {
                _screenshots.value = UiState.Loading

                // 获取指定日期的截图
                val screenshots = screenshotRepository.getScreenshotsForDay(dateString)

                if (screenshots.isEmpty()) {
                    _screenshots.value = UiState.Empty
                    return@launch
                }

                // 将 Screenshot 转换为 ScreenshotDetail
                val screenshotDetails = screenshots.map { screenshot ->
                    ScreenshotDetail(
                        id = screenshot.id,
                        path = screenshot.filePath,
                        timestamp = screenshot.timestamp,
                        date = Date(screenshot.timestamp),
                        timeFormatted = timeFormat.format(Date(screenshot.timestamp)),
                        appName = screenshot.appPackage
                    )
                }

                // 保存原始列表
                originalScreenshots = screenshotDetails

                // 应用当前排序
                applyCurrentSorting()
            } catch (e: Exception) {
                Timber.e(e, "加载截图失败: $dateString")
                _screenshots.value = UiState.Error(e.message ?: "未知错误")
            }
        }
    }

    /**
     * 加载指定日期的报告
     */
    fun loadReport(dateString: String) {
        viewModelScope.launch {
            try {
                _report.value = UiState.Loading

                // 获取日期的拼图报告
                val collageReport = collageReportRepository.getCollageReportForDate(dateString)

                if (collageReport == null) {
                    _report.value = UiState.Empty
                    return@launch
                }

                // 创建报告详情
                val reportDetail = ReportDetail(
                    id = collageReport.id,
                    date = collageReport.date,
                    imagePath = collageReport.collagePath,
                    formattedDate = dateFormat.format(collageReport.date)
                )

                _report.value = UiState.Success(reportDetail)
            } catch (e: Exception) {
                Timber.e(e, "加载报告失败: $dateString")
                _report.value = UiState.Error(e.message ?: "未知错误")
            }
        }
    }

    /**
     * 分享报告
     */
    fun shareReport(dateString: String) {
        val currentState = _report.value

        if (currentState is UiState.Success) {
            val report = currentState.data
            shareImage(report.imagePath, dateString)
        }
    }

    /**
     * 分享图片
     */
    private fun shareImage(imagePath: String, dateString: String) {
        try {
            val file = File(imagePath)
            val authority = "${application.packageName}.fileprovider"
            val uri = FileProvider.getUriForFile(application, authority, file)

            val shareIntent = Intent().apply {
                action = Intent.ACTION_SEND
                putExtra(Intent.EXTRA_STREAM, uri)
                type = "image/jpeg"
                flags = Intent.FLAG_GRANT_READ_URI_PERMISSION
            }

            val chooserIntent = Intent.createChooser(shareIntent, "分享截图").apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }

            application.startActivity(chooserIntent)
        } catch (e: Exception) {
            Timber.e(e, "分享图片失败")
        }
    }

    /**
     * 切换排序方式
     */
    fun toggleSortOrder() {
        val newOrder = if (_sortOrder.value == SortOrder.ASCENDING) {
            SortOrder.DESCENDING
        } else {
            SortOrder.ASCENDING
        }

        _sortOrder.value = newOrder
        applyCurrentSorting()
    }

    /**
     * 设置排序方式
     */
    fun setSortOrder(order: SortOrder) {
        if (_sortOrder.value != order) {
            _sortOrder.value = order
            applyCurrentSorting()
        }
    }

    /**
     * 应用当前排序方式
     */
    private fun applyCurrentSorting() {
        if (originalScreenshots.isEmpty()) return

        val sortedList = when (_sortOrder.value) {
            SortOrder.ASCENDING -> originalScreenshots.sortedBy { it.timestamp }
            SortOrder.DESCENDING -> originalScreenshots.sortedByDescending { it.timestamp }
        }

        _screenshots.value = UiState.Success(sortedList)
    }
}

/**
 * 报告详情数据类
 */
data class ReportDetail(
    val id: String,
    val date: Date,
    val imagePath: String,
    val formattedDate: String
)