package com.shuyi.discipline.ui.screen.config

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.hilt.navigation.compose.hiltViewModel
import com.shuyi.discipline.data.model.ScreenshotDeduplication
import com.shuyi.discipline.data.repository.ScreenshotDeduplicationRepository
import com.shuyi.discipline.ui.components.PageTitleBar
import com.shuyi.discipline.ui.components.SettingsCard
import com.shuyi.discipline.ui.components.SettingsSwitchItem
import com.shuyi.discipline.ui.components.IntervalEditDialog
import com.shuyi.discipline.ui.components.RandomRangeEditDialog
import com.shuyi.discipline.ui.model.UiState
import com.shuyi.discipline.utils.SensitivityLevel
import kotlin.math.roundToInt

/**
 * 截图设置页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ScreenshotSettingsScreen(
    onNavigateBack: () -> Unit,
    viewModel: ConfigViewModel = viewModel(),
    deduplicationViewModel: ScreenshotDeduplicationViewModel = hiltViewModel()
) {
    val uiState by viewModel.scheduleRule.collectAsStateWithLifecycle()
    val deduplicationState by deduplicationViewModel.deduplicationSettings.collectAsStateWithLifecycle()
    val deduplicationStatsState by deduplicationViewModel.deduplicationStats.collectAsStateWithLifecycle()
    val scrollState = rememberScrollState()

    LaunchedEffect(key1 = Unit) {
        viewModel.loadScheduleRule()
    }

    Scaffold(
        containerColor = MaterialTheme.colorScheme.background,
        topBar = {
            PageTitleBar(
                title = "截图设置",
                showBackButton = true,
                onBackClick = onNavigateBack
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(MaterialTheme.colorScheme.background)
                .padding(horizontal = 16.dp)
                .verticalScroll(scrollState),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            when (uiState) {
                is UiState.Loading -> {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(200.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
                is UiState.Error -> {
                    Text(
                        text = "加载错误: ${(uiState as UiState.Error).message}",
                        color = MaterialTheme.colorScheme.error
                    )
                }
                is UiState.Success<*> -> {
                    val rule = (uiState as UiState.Success<ScheduleRuleUi>).data

                    SettingsCard(title = "截图间隔") {
                        Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                            // 截图间隔时间标题和完整时间显示
                            var minutesSliderValue by remember { mutableFloatStateOf(rule.intervalMinutes.toFloat()) }
                            var secondsSliderValue by remember { mutableFloatStateOf(rule.intervalSeconds.toFloat()) }
                            var showIntervalEditDialog by remember { mutableStateOf(false) }

                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = "截屏间隔时间",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSecondaryContainer
                                )

                                // 完整时间显示
                                Text(
                                    text = "${minutesSliderValue.toInt()}分${secondsSliderValue.toInt()}秒",
                                    style = MaterialTheme.typography.bodyLarge.copy(
                                        fontWeight = FontWeight.Bold,
                                        fontSize = 18.sp
                                    ),
                                    color = MaterialTheme.colorScheme.onSurface,
                                    modifier = Modifier.clickable {
                                        showIntervalEditDialog = true
                                    }
                                )
                            }

                            Spacer(modifier = Modifier.height(8.dp))

                            // 分钟选择器
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                // 当秒数为0时，分钟最小为1；当秒数不为0时，分钟可以为0
                                val minMinutes = if (secondsSliderValue.toInt() == 0) 1f else 0f
                                val adjustedMinutesValue = if (minutesSliderValue < minMinutes) minMinutes else minutesSliderValue

                                Slider(
                                    value = adjustedMinutesValue,
                                    onValueChange = { newValue ->
                                        val validValue = if (newValue < minMinutes) minMinutes else newValue
                                        minutesSliderValue = validValue
                                    },
                                    onValueChangeFinished = {
                                        val validValue = if (minutesSliderValue < minMinutes) minMinutes else minutesSliderValue
                                        minutesSliderValue = validValue
                                        viewModel.updateIntervalMinutes(minutesSliderValue.toInt())
                                    },
                                    valueRange = minMinutes..60f,
                                    steps = (60 - minMinutes).toInt(),
                                    modifier = Modifier.weight(1f)
                                )

                                Text(
                                    text = "${minutesSliderValue.toInt()}分钟",
                                    style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
                                    modifier = Modifier.padding(start = 8.dp)
                                )
                            }

                            // 秒数选择器
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Slider(
                                    value = secondsSliderValue,
                                    onValueChange = { newValue ->
                                        // 如果分钟为0且秒数也要设为0，则不允许
                                        if (minutesSliderValue.toInt() == 0 && newValue.toInt() == 0) {
                                            // 保持秒数至少为1
                                            secondsSliderValue = 1f
                                        } else {
                                            secondsSliderValue = newValue
                                        }
                                    },
                                    onValueChangeFinished = {
                                        // 确保分钟和秒数不能同时为0
                                        if (minutesSliderValue.toInt() == 0 && secondsSliderValue.toInt() == 0) {
                                            secondsSliderValue = 1f
                                        }
                                        viewModel.updateIntervalSeconds(secondsSliderValue.toInt())
                                    },
                                    valueRange = 0f..59f,
                                    steps = 59,
                                    modifier = Modifier.weight(1f)
                                )

                                Text(
                                    text = "${secondsSliderValue.toInt()}秒",
                                    style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
                                    modifier = Modifier.padding(start = 8.dp)
                                )
                            }

                            // 随机间隔
                            SettingsSwitchItem(
                                title = "随机间隔时间",
                                description = "在设定时间基础上随机增减",
                                checked = rule.isRandom,
                                onCheckedChange = { viewModel.updateIsRandom(it) }
                            )

                            // 时间编辑对话框
                            if (showIntervalEditDialog) {
                                IntervalEditDialog(
                                    currentMinutes = minutesSliderValue.toInt(),
                                    currentSeconds = secondsSliderValue.toInt(),
                                    onDismiss = { showIntervalEditDialog = false },
                                    onConfirm = { minutes: Int, seconds: Int ->
                                        minutesSliderValue = minutes.toFloat()
                                        secondsSliderValue = seconds.toFloat()
                                        viewModel.updateIntervalMinutes(minutes)
                                        viewModel.updateIntervalSeconds(seconds)
                                        showIntervalEditDialog = false
                                    }
                                )
                            }
                        }
                    }

                    // 随机范围设置
                    if (rule.isRandom) {
                        SettingsCard(title = "随机范围") {
                            Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                                // 随机范围时间标题和完整时间显示
                                var randomRangeMinutesSliderValue by remember { mutableFloatStateOf(rule.randomRangeMinutes.toFloat()) }
                                var randomRangeSecondsSliderValue by remember { mutableFloatStateOf(rule.randomRangeSeconds.toFloat()) }
                                var showRandomRangeEditDialog by remember { mutableStateOf(false) }

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text(
                                        text = "随机范围时间",
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = MaterialTheme.colorScheme.onSecondaryContainer
                                    )

                                    // 完整时间显示
                                    Text(
                                        text = "±${randomRangeMinutesSliderValue.toInt()}分${randomRangeSecondsSliderValue.toInt()}秒",
                                        style = MaterialTheme.typography.bodyLarge.copy(
                                            fontWeight = FontWeight.Bold,
                                            fontSize = 18.sp
                                        ),
                                        color = MaterialTheme.colorScheme.onSurface,
                                        modifier = Modifier.clickable {
                                            showRandomRangeEditDialog = true
                                        }
                                    )
                                }

                                Spacer(modifier = Modifier.height(8.dp))

                                // 分钟选择器
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Slider(
                                        value = randomRangeMinutesSliderValue,
                                        onValueChange = { newValue ->
                                            randomRangeMinutesSliderValue = newValue
                                        },
                                        onValueChangeFinished = {
                                            viewModel.updateRandomRangeMinutes(randomRangeMinutesSliderValue.toInt())
                                        },
                                        valueRange = 0f..60f,
                                        steps = 60,
                                        modifier = Modifier.weight(1f)
                                    )

                                    Text(
                                        text = "${randomRangeMinutesSliderValue.toInt()}分钟",
                                        style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
                                        modifier = Modifier.padding(start = 8.dp)
                                    )
                                }

                                // 秒数选择器
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Slider(
                                        value = randomRangeSecondsSliderValue,
                                        onValueChange = { newValue ->
                                            randomRangeSecondsSliderValue = newValue
                                        },
                                        onValueChangeFinished = {
                                            viewModel.updateRandomRangeSeconds(randomRangeSecondsSliderValue.toInt())
                                        },
                                        valueRange = 0f..59f,
                                        steps = 59,
                                        modifier = Modifier.weight(1f)
                                    )

                                    Text(
                                        text = "${randomRangeSecondsSliderValue.toInt()}秒",
                                        style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
                                        modifier = Modifier.padding(start = 8.dp)
                                    )
                                }

                                // 随机范围编辑对话框
                                if (showRandomRangeEditDialog) {
                                    RandomRangeEditDialog(
                                        currentMinutes = randomRangeMinutesSliderValue.toInt(),
                                        currentSeconds = randomRangeSecondsSliderValue.toInt(),
                                        onDismiss = { showRandomRangeEditDialog = false },
                                        onConfirm = { minutes: Int, seconds: Int ->
                                            randomRangeMinutesSliderValue = minutes.toFloat()
                                            randomRangeSecondsSliderValue = seconds.toFloat()
                                            viewModel.updateRandomRangeMinutes(minutes)
                                            viewModel.updateRandomRangeSeconds(seconds)
                                            showRandomRangeEditDialog = false
                                        }
                                    )
                                }
                            }
                        }
                    }

                    // 图像质量设置
                    SettingsCard(title = "图像质量") {
                        Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                            // 截屏质量
                            Text(
                                text = "截屏质量",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSecondaryContainer,
                                modifier = Modifier.padding(bottom = 4.dp, top = 8.dp)
                            )

                            var qualityValue by remember { mutableFloatStateOf(rule.qualitySetting.toFloat()) }
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Slider(
                                    value = qualityValue,
                                    onValueChange = { newValue ->
                                        qualityValue = newValue.roundToInt().toFloat()
                                    },
                                    onValueChangeFinished = { viewModel.updateQualitySetting(qualityValue.toInt()) },
                                    valueRange = 1f..100f,
                                    steps = 98, // 100档：1%-100%
                                    modifier = Modifier.weight(1f)
                                )

                                Text(
                                    text = "${qualityValue.toInt()}%",
                                    style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
                                    modifier = Modifier.padding(start = 8.dp)
                                )
                            }

                            // 分辨率
                            Text(
                                text = "分辨率",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSecondaryContainer,
                                modifier = Modifier.padding(bottom = 4.dp, top = 8.dp)
                            )

                            var resolutionValue by remember { mutableFloatStateOf(rule.resolutionSetting.toFloat()) }
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Slider(
                                    value = resolutionValue,
                                    onValueChange = { newValue ->
                                        resolutionValue = newValue.roundToInt().toFloat()
                                    },
                                    onValueChangeFinished = { viewModel.updateResolutionSetting(resolutionValue.toInt()) },
                                    valueRange = 1f..100f,
                                    steps = 98, // 100档：1%-100%
                                    modifier = Modifier.weight(1f)
                                )

                                Text(
                                    text = "${resolutionValue.toInt()}%",
                                    style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
                                    modifier = Modifier.padding(start = 8.dp)
                                )
                            }

                            // 高斯模糊（数值越大越模糊）
                            Text(
                                text = "高斯模糊（数值越大越模糊）",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSecondaryContainer,
                                modifier = Modifier.padding(bottom = 4.dp, top = 8.dp)
                            )

                            var blurValue by remember { mutableFloatStateOf(rule.blurSetting.toFloat()) }
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Slider(
                                    value = blurValue,
                                    onValueChange = { newValue ->
                                        blurValue = newValue.roundToInt().toFloat()
                                    },
                                    onValueChangeFinished = { viewModel.updateBlurSetting(blurValue.toInt()) },
                                    valueRange = 0f..100f,
                                    steps = 99, // 101档：0%-100%
                                    modifier = Modifier.weight(1f)
                                )

                                Text(
                                    text = "${blurValue.toInt()}%",
                                    style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
                                    modifier = Modifier.padding(start = 8.dp)
                                )
                            }
                        }
                    }

                    // 智能去重设置
                    SettingsCard(title = "智能去重") {
                        Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                            // 去重功能开关
                            SettingsSwitchItem(
                                title = "智能去重",
                                description = "自动检测并删除重复的截图",
                                checked = when (val state = deduplicationState) {
                                    is UiState.Success -> state.data.isEnabled
                                    else -> true
                                },
                                onCheckedChange = { deduplicationViewModel.updateDeduplicationEnabled(it) }
                            )

                            // 敏感度设置
                            Text(
                                text = "相似度阈值",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSecondaryContainer,
                                modifier = Modifier.padding(bottom = 4.dp, top = 8.dp)
                            )

                            var showSensitivityOptions by remember { mutableStateOf(false) }
                            val currentSensitivity = when (val state = deduplicationState) {
                                is UiState.Success -> SensitivityLevel.fromThreshold(state.data.sensitivityLevel)
                                else -> SensitivityLevel.MEDIUM
                            }

                            Box {
                                Card(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .clickable { showSensitivityOptions = true },
                                    shape = RoundedCornerShape(8.dp),
                                    colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surfaceVariant)
                                ) {
                                    Column(
                                        modifier = Modifier.padding(16.dp)
                                    ) {
                                        Text(
                                            text = currentSensitivity.displayName,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant,
                                            style = MaterialTheme.typography.bodyLarge
                                        )
                                        Text(
                                            text = currentSensitivity.description,
                                            color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                                            style = MaterialTheme.typography.bodySmall
                                        )
                                    }
                                }

                                DropdownMenu(
                                    expanded = showSensitivityOptions,
                                    onDismissRequest = { showSensitivityOptions = false }
                                ) {
                                    SensitivityLevel.values().forEach { level ->
                                        DropdownMenuItem(
                                            text = {
                                                Column {
                                                    Text(level.displayName)
                                                    Text(
                                                        text = level.description,
                                                        style = MaterialTheme.typography.bodySmall,
                                                        color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.6f)
                                                    )
                                                }
                                            },
                                            onClick = {
                                                deduplicationViewModel.updateSensitivityLevel(level.displayName)
                                                showSensitivityOptions = false
                                            }
                                        )
                                    }
                                }
                            }

                            // 统计信息
                            Text(
                                text = "统计信息",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSecondaryContainer,
                                modifier = Modifier.padding(bottom = 4.dp, top = 8.dp)
                            )

                            // 简化的统计信息显示
                            Card(
                                modifier = Modifier.fillMaxWidth(),
                                colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f))
                            ) {
                                Column(
                                    modifier = Modifier.padding(16.dp),
                                    verticalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween
                                    ) {
                                        Text(
                                            text = "已处理截图",
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                        Text(
                                            text = when (val state = deduplicationStatsState) {
                                                is UiState.Success -> "${state.data.totalProcessed} 张"
                                                else -> "0 张"
                                            },
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }

                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween
                                    ) {
                                        Text(
                                            text = "检测到重复",
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                        Text(
                                            text = when (val state = deduplicationStatsState) {
                                                is UiState.Success -> "${state.data.duplicatesFound} 张"
                                                else -> "0 张"
                                            },
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }

                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween
                                    ) {
                                        Text(
                                            text = "节省空间",
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                        Text(
                                            text = when (val state = deduplicationStatsState) {
                                                is UiState.Success -> deduplicationViewModel.formatFileSize(state.data.spaceSavedMb)
                                                else -> "0.0 MB"
                                            },
                                            color = MaterialTheme.colorScheme.onSurfaceVariant
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
                else -> {
                    Text("未知状态")
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ScreenshotSettingsScreenPreview() {
    MaterialTheme {
        ScreenshotSettingsScreen(
            onNavigateBack = {}
        )
    }
}
