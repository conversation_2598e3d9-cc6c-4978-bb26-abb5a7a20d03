package com.shuyi.discipline.data.source.database

import androidx.room.*
import com.shuyi.discipline.data.model.MonitorStatus
import com.shuyi.discipline.data.model.MonitorType
import com.shuyi.discipline.data.model.SystemMonitorRecord
import kotlinx.coroutines.flow.Flow

/**
 * 系统监控记录数据访问对象
 */
@Dao
interface SystemMonitorDao {
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRecord(record: SystemMonitorRecord)
    
    @Update
    suspend fun updateRecord(record: SystemMonitorRecord)
    
    @Query("SELECT * FROM system_monitor_records WHERE id = :id")
    suspend fun getRecordById(id: String): SystemMonitorRecord?
    
    @Query("SELECT * FROM system_monitor_records WHERE monitorType = :type ORDER BY timestamp DESC")
    suspend fun getRecordsByType(type: MonitorType): List<SystemMonitorRecord>
    
    @Query("SELECT * FROM system_monitor_records WHERE monitorType = :type ORDER BY timestamp DESC")
    fun getRecordsByTypeFlow(type: MonitorType): Flow<List<SystemMonitorRecord>>
    
    @Query("""
        SELECT * FROM system_monitor_records 
        WHERE monitorType = :type 
        AND timestamp >= :startTime 
        AND timestamp <= :endTime 
        ORDER BY timestamp DESC
    """)
    suspend fun getRecordsByTypeAndTimeRange(
        type: MonitorType,
        startTime: Long,
        endTime: Long
    ): List<SystemMonitorRecord>
    
    @Query("""
        SELECT * FROM system_monitor_records 
        WHERE monitorType = :type 
        AND status = :status 
        AND endTime IS NULL
        ORDER BY timestamp DESC
        LIMIT 1
    """)
    suspend fun getLatestUnfinishedRecord(
        type: MonitorType,
        status: MonitorStatus
    ): SystemMonitorRecord?
    
    @Query("""
        SELECT * FROM system_monitor_records 
        WHERE timestamp >= :startTime 
        AND timestamp <= :endTime 
        ORDER BY timestamp DESC
    """)
    suspend fun getRecordsByTimeRange(
        startTime: Long,
        endTime: Long
    ): List<SystemMonitorRecord>
    
    @Query("""
        UPDATE system_monitor_records 
        SET endTime = :endTime, duration = :duration, status = :status 
        WHERE id = :id
    """)
    suspend fun finishRecord(
        id: String,
        endTime: Long,
        duration: Long,
        status: MonitorStatus
    )
    
    @Query("DELETE FROM system_monitor_records WHERE timestamp < :cutoffTime")
    suspend fun deleteOldRecords(cutoffTime: Long)
    
    @Query("DELETE FROM system_monitor_records")
    suspend fun deleteAllRecords()
}
