package com.shuyi.discipline.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.shuyi.discipline.data.source.database.Converters
import java.util.Date

/**
 * 拼图报告数据模型
 */
@Entity(tableName = "collage_reports")
@TypeConverters(Converters::class)
data class CollageReport(
    @PrimaryKey
    val id: String,
    @TypeConverters(Converters::class)
    val date: Date,
    val collagePath: String,
    @TypeConverters(Converters::class)
    val collagePaths: List<String> = emptyList(), // 存储多张拼图的路径
    @TypeConverters(Converters::class)
    val screenshotIds: List<String>,
    val thumbnailPath: String? = null,
    val createTime: Long = System.currentTimeMillis()
) {
    companion object {
        /**
         * 格式化日期为显示文本
         */
        fun formatDateForDisplay(date: Date): String {
            val formatter = java.text.SimpleDateFormat("yyyy年MM月dd日", java.util.Locale.CHINA)
            return formatter.format(date)
        }
    }
}