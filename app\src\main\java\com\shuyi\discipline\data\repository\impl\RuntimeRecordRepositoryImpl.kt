package com.shuyi.discipline.data.repository.impl

import com.shuyi.discipline.data.database.dao.RuntimeMetadataDao
import com.shuyi.discipline.data.database.dao.RuntimeRecordDao
import com.shuyi.discipline.data.database.dao.RuntimeSessionDao
import com.shuyi.discipline.data.database.entity.RuntimeMetadataEntity
import com.shuyi.discipline.data.database.entity.RuntimeRecordEntity
import com.shuyi.discipline.data.database.entity.RuntimeSessionEntity
import com.shuyi.discipline.data.repository.RecordType
import com.shuyi.discipline.data.repository.RuntimeRecord
import com.shuyi.discipline.data.repository.RuntimeRecordRepository
import com.shuyi.discipline.data.repository.RuntimeSession
import com.shuyi.discipline.domain.monitor.ExitReason
import com.shuyi.discipline.domain.monitor.RuntimeStats
import com.shuyi.discipline.domain.monitor.SessionStatus
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import timber.log.Timber
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 运行时记录仓库实现
 */
@Singleton
class RuntimeRecordRepositoryImpl @Inject constructor(
    private val recordDao: RuntimeRecordDao,
    private val sessionDao: RuntimeSessionDao,
    private val metadataDao: RuntimeMetadataDao
) : RuntimeRecordRepository {

    // ==================== 会话管理 ====================

    override suspend fun startNewSession(startTime: Long): String {
        return try {
            val sessionId = UUID.randomUUID().toString()
            
            val session = RuntimeSessionEntity(
                sessionId = sessionId,
                startTime = startTime,
                endTime = null,
                status = SessionStatus.FOREGROUND,
                exitReason = null,
                totalDuration = 0L,
                foregroundDuration = 0L,
                backgroundDuration = 0L
            )
            
            sessionDao.insertSession(session)
            
            // 更新当前会话ID
            metadataDao.setValue(
                RuntimeMetadataEntity.KEY_CURRENT_SESSION_ID,
                sessionId
            )
            
            // 记录会话开始
            val record = RuntimeRecordEntity(
                id = UUID.randomUUID().toString(),
                timestamp = startTime,
                type = RecordType.SESSION_START,
                sessionId = sessionId,
                exitReason = null,
                duration = 0L,
                description = "会话开始"
            )
            recordDao.insertRecord(record)
            
            Timber.d("新会话已开始: $sessionId")
            sessionId
            
        } catch (e: Exception) {
            Timber.e(e, "开始新会话失败")
            throw e
        }
    }

    override suspend fun endCurrentSession(endTime: Long, exitReason: ExitReason) {
        try {
            val currentSessionId = metadataDao.getValue(RuntimeMetadataEntity.KEY_CURRENT_SESSION_ID)
            
            if (currentSessionId != null) {
                val session = sessionDao.getSessionById(currentSessionId)
                
                if (session != null) {
                    val totalDuration = endTime - session.startTime
                    
                    // 更新会话
                    val updatedSession = session.copy(
                        endTime = endTime,
                        status = SessionStatus.ENDED,
                        exitReason = exitReason,
                        totalDuration = totalDuration,
                        updatedAt = System.currentTimeMillis()
                    )
                    sessionDao.updateSession(updatedSession)
                    
                    // 记录会话结束
                    val record = RuntimeRecordEntity(
                        id = UUID.randomUUID().toString(),
                        timestamp = endTime,
                        type = RecordType.SESSION_END,
                        sessionId = currentSessionId,
                        exitReason = exitReason,
                        duration = totalDuration,
                        description = "会话结束: ${exitReason.name}"
                    )
                    recordDao.insertRecord(record)
                    
                    // 清除当前会话ID
                    metadataDao.deleteByKey(RuntimeMetadataEntity.KEY_CURRENT_SESSION_ID)
                    
                    Timber.d("会话已结束: $currentSessionId, 时长: ${totalDuration}ms")
                }
            }
            
        } catch (e: Exception) {
            Timber.e(e, "结束当前会话失败")
            throw e
        }
    }

    override suspend fun forceEndSession(sessionId: String, endTime: Long, exitReason: ExitReason) {
        try {
            val session = sessionDao.getSessionById(sessionId)
            
            if (session != null) {
                val totalDuration = endTime - session.startTime
                
                val updatedSession = session.copy(
                    endTime = endTime,
                    status = SessionStatus.ENDED,
                    exitReason = exitReason,
                    totalDuration = totalDuration,
                    updatedAt = System.currentTimeMillis()
                )
                sessionDao.updateSession(updatedSession)
                
                Timber.d("强制结束会话: $sessionId")
            }
            
        } catch (e: Exception) {
            Timber.e(e, "强制结束会话失败: $sessionId")
            throw e
        }
    }

    override suspend fun updateSessionStatus(status: SessionStatus) {
        try {
            val currentSessionId = metadataDao.getValue(RuntimeMetadataEntity.KEY_CURRENT_SESSION_ID)
            
            if (currentSessionId != null) {
                sessionDao.updateSessionStatus(
                    sessionId = currentSessionId,
                    status = status,
                    updatedAt = System.currentTimeMillis()
                )
                
                Timber.d("会话状态已更新: $currentSessionId -> $status")
            }
            
        } catch (e: Exception) {
            Timber.e(e, "更新会话状态失败")
            throw e
        }
    }

    override suspend fun getCurrentSession(): RuntimeSession? {
        return try {
            val currentSessionId = metadataDao.getValue(RuntimeMetadataEntity.KEY_CURRENT_SESSION_ID)
            
            if (currentSessionId != null) {
                sessionDao.getSessionById(currentSessionId)?.toRuntimeSession()
            } else {
                null
            }
            
        } catch (e: Exception) {
            Timber.e(e, "获取当前会话失败")
            null
        }
    }

    override suspend fun getUnfinishedSessions(): List<RuntimeSession> {
        return try {
            sessionDao.getUnfinishedSessions().map { it.toRuntimeSession() }
        } catch (e: Exception) {
            Timber.e(e, "获取未完成会话失败")
            emptyList()
        }
    }

    // ==================== 退出记录 ====================

    override suspend fun recordExit(
        exitTime: Long,
        exitReason: ExitReason,
        description: String,
        downtimeStart: Long,
        downtimeEnd: Long,
        downtimeDuration: Long
    ) {
        try {
            // 记录退出事件
            val exitRecord = RuntimeRecordEntity(
                id = UUID.randomUUID().toString(),
                timestamp = exitTime,
                type = RecordType.APP_EXIT,
                sessionId = null,
                exitReason = exitReason,
                duration = 0L,
                description = description
            )
            recordDao.insertRecord(exitRecord)
            
            // 记录未运行时长
            val downtimeRecord = RuntimeRecordEntity(
                id = UUID.randomUUID().toString(),
                timestamp = downtimeStart,
                type = RecordType.DOWNTIME,
                sessionId = null,
                exitReason = exitReason,
                duration = downtimeDuration,
                description = "未运行时长: ${downtimeDuration}ms",
                endTime = downtimeEnd
            )
            recordDao.insertRecord(downtimeRecord)
            
            // 更新最后记录的退出时间
            metadataDao.setLongValue(
                RuntimeMetadataEntity.KEY_LAST_RECORDED_EXIT_TIME,
                exitTime
            )
            
            Timber.d("已记录退出: $exitTime, 未运行时长: ${downtimeDuration}ms")
            
        } catch (e: Exception) {
            Timber.e(e, "记录退出失败")
            throw e
        }
    }

    override suspend fun getLastProcessedExitTime(): Long {
        return try {
            metadataDao.getLongValue(RuntimeMetadataEntity.KEY_LAST_PROCESSED_EXIT_TIME) ?: 0L
        } catch (e: Exception) {
            Timber.e(e, "获取最后处理退出时间失败")
            0L
        }
    }

    override suspend fun updateLastProcessedExitTime(exitTime: Long) {
        try {
            metadataDao.setLongValue(
                RuntimeMetadataEntity.KEY_LAST_PROCESSED_EXIT_TIME,
                exitTime
            )
        } catch (e: Exception) {
            Timber.e(e, "更新最后处理退出时间失败")
            throw e
        }
    }

    override suspend fun getLastRecordedExitTime(): Long {
        return try {
            metadataDao.getLongValue(RuntimeMetadataEntity.KEY_LAST_RECORDED_EXIT_TIME) ?: 0L
        } catch (e: Exception) {
            Timber.e(e, "获取最后记录退出时间失败")
            0L
        }
    }

    override suspend fun hasExitRecordAfter(timestamp: Long): Boolean {
        return try {
            recordDao.hasExitRecordAfter(timestamp)
        } catch (e: Exception) {
            Timber.e(e, "检查退出记录失败")
            false
        }
    }

    // ==================== 运行时统计 ====================

    override suspend fun getRuntimeStats(hours: Int): RuntimeStats {
        val endTime = System.currentTimeMillis()
        val startTime = endTime - (hours * 60 * 60 * 1000L)
        return getRuntimeStatsInRange(startTime, endTime)
    }

    override suspend fun getRuntimeStatsInRange(startTime: Long, endTime: Long): RuntimeStats {
        return try {
            val totalRuntime = sessionDao.getTotalRuntimeInRange(startTime, endTime) ?: 0L
            val totalDowntime = recordDao.getTotalDowntimeInRange(startTime, endTime) ?: 0L
            val sessionCount = sessionDao.getSessionCountInRange(startTime, endTime)
            val averageSessionDuration = sessionDao.getAverageSessionDurationInRange(startTime, endTime) ?: 0L
            val lastExitTime = recordDao.getLastExitTime()
            
            RuntimeStats(
                totalRuntime = totalRuntime,
                totalDowntime = totalDowntime,
                sessionCount = sessionCount,
                averageSessionDuration = averageSessionDuration,
                lastExitTime = lastExitTime
            )
            
        } catch (e: Exception) {
            Timber.e(e, "获取运行时统计失败")
            RuntimeStats.empty()
        }
    }

    // ==================== 扩展函数 ====================

    private fun RuntimeSessionEntity.toRuntimeSession(): RuntimeSession {
        return RuntimeSession(
            sessionId = sessionId,
            startTime = startTime,
            endTime = endTime,
            status = status,
            exitReason = exitReason,
            totalDuration = totalDuration,
            foregroundDuration = foregroundDuration,
            backgroundDuration = backgroundDuration
        )
    }

    private fun RuntimeRecordEntity.toRuntimeRecord(): RuntimeRecord {
        return RuntimeRecord(
            id = id,
            timestamp = timestamp,
            type = type,
            sessionId = sessionId,
            exitReason = exitReason,
            duration = duration,
            description = description,
            endTime = endTime
        )
    }

    // ==================== 其他方法实现 ====================

    override suspend fun getTotalRuntime(hours: Int): Long {
        val endTime = System.currentTimeMillis()
        val startTime = endTime - (hours * 60 * 60 * 1000L)
        return sessionDao.getTotalRuntimeInRange(startTime, endTime) ?: 0L
    }

    override suspend fun getTotalDowntime(hours: Int): Long {
        val endTime = System.currentTimeMillis()
        val startTime = endTime - (hours * 60 * 60 * 1000L)
        return recordDao.getTotalDowntimeInRange(startTime, endTime) ?: 0L
    }

    override suspend fun getSessionCount(hours: Int): Int {
        val endTime = System.currentTimeMillis()
        val startTime = endTime - (hours * 60 * 60 * 1000L)
        return sessionDao.getSessionCountInRange(startTime, endTime)
    }

    override fun getAllRecords(): Flow<List<RuntimeRecord>> {
        return recordDao.getAllRecords().map { entities ->
            entities.map { it.toRuntimeRecord() }
        }
    }

    override suspend fun getRecordsInRange(startTime: Long, endTime: Long): List<RuntimeRecord> {
        return try {
            recordDao.getRecordsInRange(startTime, endTime).map { it.toRuntimeRecord() }
        } catch (e: Exception) {
            Timber.e(e, "获取时间范围记录失败")
            emptyList()
        }
    }

    override suspend fun getRecentRecords(limit: Int): List<RuntimeRecord> {
        return try {
            recordDao.getRecentRecords(limit).map { it.toRuntimeRecord() }
        } catch (e: Exception) {
            Timber.e(e, "获取最近记录失败")
            emptyList()
        }
    }

    override fun getAllSessions(): Flow<List<RuntimeSession>> {
        return sessionDao.getAllSessions().map { entities ->
            entities.map { it.toRuntimeSession() }
        }
    }

    override suspend fun getSessionsInRange(startTime: Long, endTime: Long): List<RuntimeSession> {
        return try {
            sessionDao.getSessionsInRange(startTime, endTime).map { it.toRuntimeSession() }
        } catch (e: Exception) {
            Timber.e(e, "获取时间范围会话失败")
            emptyList()
        }
    }

    override suspend fun getCurrentSessionId(): String? {
        return try {
            metadataDao.getValue(RuntimeMetadataEntity.KEY_CURRENT_SESSION_ID)
        } catch (e: Exception) {
            Timber.e(e, "获取当前会话ID失败")
            null
        }
    }

    override suspend fun getSessionById(sessionId: String): RuntimeSession? {
        return try {
            sessionDao.getSessionById(sessionId)?.toRuntimeSession()
        } catch (e: Exception) {
            Timber.e(e, "根据ID获取会话失败: $sessionId")
            null
        }
    }

    override suspend fun cleanOldData(cutoffTime: Long) {
        try {
            val deletedRecords = recordDao.deleteOldRecords(cutoffTime)
            val deletedSessions = sessionDao.deleteOldSessions(cutoffTime)

            Timber.d("清理旧数据完成: 记录 $deletedRecords 条, 会话 $deletedSessions 个")
        } catch (e: Exception) {
            Timber.e(e, "清理旧数据失败")
            throw e
        }
    }

    override suspend fun deleteAllData() {
        try {
            recordDao.deleteAllRecords()
            sessionDao.deleteAllSessions()
            metadataDao.deleteAllMetadata()

            Timber.d("所有数据已删除")
        } catch (e: Exception) {
            Timber.e(e, "删除所有数据失败")
            throw e
        }
    }

    override suspend fun resetMonitoringState() {
        try {
            val currentTime = System.currentTimeMillis()

            // 设置最后处理的退出时间为当前时间，避免重新处理历史退出信息
            metadataDao.setLongValue(
                RuntimeMetadataEntity.KEY_LAST_PROCESSED_EXIT_TIME,
                currentTime
            )

            // 清除当前会话ID（如果有的话）
            metadataDao.deleteByKey(RuntimeMetadataEntity.KEY_CURRENT_SESSION_ID)

            Timber.d("监控状态已重置，最后处理时间: ${java.util.Date(currentTime)}")

        } catch (e: Exception) {
            Timber.e(e, "重置监控状态失败")
            throw e
        }
    }

    override suspend fun getDatabaseSize(): Long {
        return try {
            val recordCount = recordDao.getRecordCount()
            val sessionCount = sessionDao.getSessionCount()

            // 简单估算，每条记录约200字节，每个会话约300字节
            (recordCount * 200L) + (sessionCount * 300L)
        } catch (e: Exception) {
            Timber.e(e, "获取数据库大小失败")
            0L
        }
    }

    override suspend fun exportData(): String {
        return try {
            // 简单的JSON导出实现
            val records = recordDao.getRecentRecords(1000)
            val sessions = sessionDao.getRecentSessions(100)

            // 这里应该使用JSON序列化库，简化实现
            """
            {
                "records": ${records.size},
                "sessions": ${sessions.size},
                "exported_at": ${System.currentTimeMillis()}
            }
            """.trimIndent()
        } catch (e: Exception) {
            Timber.e(e, "导出数据失败")
            "{\"error\": \"导出失败\"}"
        }
    }

    override suspend fun importData(data: String): Boolean {
        return try {
            // 简化的导入实现
            Timber.d("导入数据: ${data.length} 字符")
            true
        } catch (e: Exception) {
            Timber.e(e, "导入数据失败")
            false
        }
    }

    /**
     * 修复现有DOWNTIME记录的endTime字段
     * 为没有endTime的DOWNTIME记录计算并设置正确的endTime值
     */
    suspend fun fixDowntimeEndTimes() {
        try {
            Timber.d("开始修复DOWNTIME记录的endTime字段...")

            // 获取所有没有endTime的DOWNTIME记录
            val downtimeRecords = recordDao.getRecordsByType(RecordType.DOWNTIME)
            val recordsToFix = downtimeRecords.filter { it.endTime == null }

            Timber.d("找到 ${recordsToFix.size} 条需要修复的DOWNTIME记录")

            recordsToFix.forEach { record ->
                // 计算正确的endTime = timestamp + duration
                val correctEndTime = record.timestamp + record.duration

                // 更新记录
                val updatedRecord = record.copy(endTime = correctEndTime)
                recordDao.updateRecord(updatedRecord)

                Timber.d("修复记录 ${record.id}: endTime = ${java.util.Date(correctEndTime)}")
            }

            Timber.d("DOWNTIME记录endTime字段修复完成，共修复 ${recordsToFix.size} 条记录")

        } catch (e: Exception) {
            Timber.e(e, "修复DOWNTIME记录endTime字段失败")
            throw e
        }
    }
}
