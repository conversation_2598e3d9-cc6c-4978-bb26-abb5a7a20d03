package com.shuyi.discipline.ui.components

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material.icons.filled.Error
import androidx.compose.material.icons.filled.HideImage
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp

/**
 * 加载指示器组件
 */
@Composable
fun LoadingIndicator(
    modifier: Modifier = Modifier,
    message: String = "加载中..."
) {
    Column(
        modifier = modifier.fillMaxSize(),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        CircularProgressIndicator(
            modifier = Modifier.size(48.dp),
            color = MaterialTheme.colorScheme.primary
        )
        Text(
            text = message,
            style = MaterialTheme.typography.bodyMedium,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = 16.dp)
        )
    }
}

/**
 * 错误提示组件
 */
@Composable
fun ErrorIndicator(
    modifier: Modifier = Modifier,
    message: String = "发生错误，请重试"
) {
    Column(
        modifier = modifier.fillMaxSize(),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Filled.Error,
            contentDescription = "错误",
            modifier = Modifier.size(64.dp),
            tint = Color.Red
        )
        Text(
            text = message,
            style = MaterialTheme.typography.bodyMedium,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = 16.dp, start = 32.dp, end = 32.dp)
        )
    }
}

/**
 * 空报告提示组件
 */
@Composable
fun EmptyReportIndicator(
    modifier: Modifier = Modifier,
    message: String = "没有可显示的报告"
) {
    Column(
        modifier = modifier.fillMaxSize(),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Icon(
            imageVector = Icons.Filled.HideImage,
            contentDescription = "空数据",
            modifier = Modifier.size(64.dp),
            tint = MaterialTheme.colorScheme.outline
        )
        Text(
            text = message,
            style = MaterialTheme.typography.bodyMedium,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(top = 16.dp)
        )
    }
}

/**
 * 可缩放图片组件
 */
@Composable
fun ZoomableImage(
    modifier: Modifier = Modifier,
    imagePath: String
) {
    // 简化版实现，实际应使用Coil加载图片并实现缩放功能
    Box(modifier = modifier.fillMaxSize()) {
        if (imagePath.isNotEmpty()) {
            // 这里实际应使用Coil或其他图片加载库
            // 简化实现，仅显示错误图标
            Icon(
                imageVector = Icons.Filled.Warning,
                contentDescription = "图片加载失败",
                modifier = Modifier
                    .size(120.dp)
                    .align(Alignment.Center),
                tint = MaterialTheme.colorScheme.outline
            )
        } else {
            EmptyReportIndicator()
        }
    }
} 