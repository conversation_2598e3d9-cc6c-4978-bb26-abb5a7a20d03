package com.shuyi.discipline.ui.screen.collage

import android.app.Application
import android.graphics.BitmapFactory
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.shuyi.discipline.ui.components.BottomNavBar
import com.shuyi.discipline.ui.model.UiState
import com.shuyi.discipline.ui.navigation.Screen
import timber.log.Timber
import java.io.File
import java.text.SimpleDateFormat
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.Locale

/**
 * 拼图详情屏幕
 */
@Composable
fun CollageDetailScreen(
    date: String,
    onNavigateBack: () -> Unit = {},
    onNavigateToHome: () -> Unit = {},
    onNavigateToScreenshots: () -> Unit = {},
    onNavigateToCollages: () -> Unit = {},
    onNavigateToConfig: () -> Unit = {},
    onNavigateToPreview: (String) -> Unit = {},
    viewModel: CollageDetailViewModel = viewModel(
        factory = CollageDetailViewModelFactory(LocalContext.current.applicationContext as Application)
    ),
    navController: NavController? = null
) {
    val collageState by viewModel.collage.collectAsState()
    var selectedTabIndex by remember { mutableStateOf(2) } // 拼图选项卡

    // 在Composable外处理日期格式化，避免try-catch直接包裹Composable
    val screenTitle = remember(date) {
        try {
            val localDate = LocalDate.parse(date)
            val formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日")
            "${localDate.format(formatter)}的拼图"
        } catch (e: Exception) {
            "拼图详情"
        }
    }

    // 加载拼图
    LaunchedEffect(date) {
        try {
            // 检查日期格式是否包含索引（如 "2023-05-17/0"）
            if (date.contains("/")) {
                // 如果包含索引，只取日期部分
                val datePart = date.split("/")[0]
                Timber.d("拼图详情：提取日期部分 $datePart 从 $date")
                // 加载指定日期的拼图
                viewModel.loadCollage(datePart)
            } else {
                // 直接使用日期
                Timber.d("拼图详情：使用完整日期 $date")
                viewModel.loadCollage(date)
            }
        } catch (e: Exception) {
            Timber.e(e, "解析详情日期失败: $date")
            // 出错时仍尝试加载
            viewModel.loadCollage(date)
        }
    }

    // 定义颜色
    val lightGray = Color(0xFFF8F8F8)
    val primaryBlue = Color(0xFF007AFF)

    Scaffold(
        containerColor = lightGray,
        bottomBar = {
            BottomNavBar(
                selectedIndex = selectedTabIndex,
                onItemSelected = { index ->
                    selectedTabIndex = index
                    when (index) {
                        0 -> onNavigateToHome()
                        1 -> onNavigateToScreenshots()
                        // 2是拼图选项卡，当前已在拼图详情页面
                        3 -> navController?.navigate(Screen.Status.route) {
                            // 避免创建多个实例
                            launchSingleTop = true
                        }
                        4 -> onNavigateToConfig()
                    }
                },
                onNavigateToHome = onNavigateToHome,
                onNavigateToScreenshots = onNavigateToScreenshots,
                onNavigateToCollages = onNavigateToCollages,
                onNavigateToStatus = {
                    navController?.navigate(Screen.Status.route) {
                        // 避免创建多个实例
                        launchSingleTop = true
                    }
                },
                onNavigateToConfig = onNavigateToConfig
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(lightGray)
        ) {
            // 标题栏
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 12.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 返回按钮
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(Color.White)
                        .padding(8.dp),
                    contentAlignment = Alignment.Center
                ) {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "返回",
                            tint = Color.Black
                        )
                    }
                }

                // 标题
                Text(
                    text = screenTitle,
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )

                // 分享按钮
                Box(
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .background(Color.White)
                        .padding(8.dp),
                    contentAlignment = Alignment.Center
                ) {
                    IconButton(onClick = {
                        // 处理可能包含索引的日期格式
                        val shareDate = if (date.contains("/")) date.split("/")[0] else date
                        viewModel.shareCollage(shareDate)
                    }) {
                        Icon(
                            Icons.Default.Share,
                            contentDescription = "分享",
                            tint = primaryBlue
                        )
                    }
                }
            }

            // 内容区域
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp)
            ) {
                when (collageState) {
                    is UiState.Loading -> {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Text("加载中...")
                        }
                    }
                    is UiState.Empty -> {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "该日期没有拼图",
                                style = MaterialTheme.typography.titleMedium,
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                    is UiState.Error -> {
                        Box(
                            modifier = Modifier.fillMaxSize(),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = "加载错误: ${(collageState as UiState.Error).message}",
                                color = Color.Red,
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                    is UiState.Success -> {
                        val collage = (collageState as UiState.Success<CollageDetail>).data
                        CollageDetailContent(
                            collage = collage,
                            onNavigateToPreview = onNavigateToPreview
                        )
                    }
                    else -> {
                        // 处理其他可能的状态
                    }
                }
            }
        }
    }
}

/**
 * 拼图详情内容
 */
@Composable
fun CollageDetailContent(
    collage: CollageDetail,
    onNavigateToPreview: (String) -> Unit = {}
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(bottom = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 拼图信息
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = "创建时间: ${collage.formattedTime}",
                style = MaterialTheme.typography.bodyMedium,
                color = Color.Gray
            )

            Text(
                text = "包含 ${collage.screenshotCount} 张截图",
                style = MaterialTheme.typography.bodyMedium,
                color = Color.Gray
            )
        }

        Spacer(modifier = Modifier.height(8.dp))

        // 拼图图片
        val file = File(collage.imagePath)
        val bitmap = remember(collage.imagePath) {
            if (file.exists()) {
                try {
                    BitmapFactory.decodeFile(file.absolutePath)
                } catch (e: Exception) {
                    Timber.e(e, "加载拼图图片失败: ${collage.imagePath}")
                    null
                }
            } else {
                Timber.e("拼图文件不存在: ${collage.imagePath}")
                null
            }
        }

        if (bitmap != null) {
            // 显示主拼图
            Image(
                bitmap = bitmap.asImageBitmap(),
                contentDescription = "拼图详情",
                contentScale = ContentScale.FillWidth,
                modifier = Modifier
                    .fillMaxWidth()
                    .clip(RoundedCornerShape(16.dp))
                    .clickable {
                        // 点击拼图时跳转到预览界面
                        val dateString = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(collage.date)
                        onNavigateToPreview("$dateString/0") // 默认显示第一张拼图
                    }
            )

            // 如果有多张拼图，显示提示信息
            if (collage.imagePaths.size > 1) {
                Text(
                    text = "点击查看全部 ${collage.imagePaths.size} 张拼图",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color.Gray,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 8.dp)
                        .align(Alignment.CenterHorizontally)
                )
            }
        } else if (!file.exists()) {
            Text(
                text = "拼图文件不存在: ${collage.imagePath}",
                color = Color.Red,
                modifier = Modifier.padding(16.dp)
            )
        } else {
            Text(
                text = "无法加载拼图图片",
                color = Color.Red,
                modifier = Modifier.padding(16.dp)
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun CollageDetailScreenPreview() {
    MaterialTheme {
        CollageDetailScreen(
            date = "2023-05-15",
            onNavigateBack = {}
        )
    }
}
