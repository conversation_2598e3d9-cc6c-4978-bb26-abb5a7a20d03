package com.shuyi.discipline.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.dynamicDarkColorScheme
import androidx.compose.material3.dynamicLightColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

// 深色主题颜色方案
private val DarkColorScheme = darkColorScheme(
    primary = PrimaryBlue,
    primaryContainer = PrimaryVariantBlue,
    secondary = Green,
    error = Red,
    background = BackgroundDark, // 纯黑色背景
    surface = SurfaceDark, // 深色表面
    surfaceContainer = NavigationBarDark, // 导航栏等容器背景
    surfaceContainerHigh = CardBackgroundDark, // 卡片背景
    onPrimary = OnPrimary,
    onSecondary = OnPrimary,
    onBackground = TextPrimaryDark,
    onSurface = TextPrimaryDark,
    onSecondaryContainer = Color(0xFFE1E1E1), // 确保深色模式下按钮文字清晰可见
    onSurfaceVariant = Color(0xFFE1E1E1), // 确保深色模式下文字清晰可见
    outline = DividerColorDark,
    surfaceVariant = CardBackgroundDark
)

// 浅色主题颜色方案
private val LightColorScheme = lightColorScheme(
    primary = PrimaryBlue,
    primaryContainer = PrimaryVariantBlue,
    secondary = Green,
    error = Red,
    background = BackgroundGray,
    surface = CardBackgroundLight,
    onPrimary = OnPrimary,
    onSecondary = OnPrimary,
    onBackground = TextPrimary,
    onSurface = TextPrimary,
    onSecondaryContainer = TextSecondary,
    outline = DividerColor,
    surfaceVariant = CardBackgroundLight
)

@Composable
fun DisciplineselfTheme(
    themeManager: ThemeManager? = null,
    content: @Composable () -> Unit
) {
    // 如果提供了 themeManager，使用它来确定主题，否则使用系统默认
    val darkTheme = themeManager?.shouldUseDarkTheme() ?: isSystemInDarkTheme()
    val dynamicColor = themeManager?.shouldUseDynamicColor() ?: false
    val fontSize = themeManager?.getCurrentFontSize() ?: com.shuyi.discipline.data.model.FontSize.STANDARD

    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }
        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }

    // 创建动态Typography
    val typography = createTypography(fontSize)

    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            // 设置状态栏颜色为透明，让应用内容延伸到状态栏
            window.statusBarColor = Color.Transparent.toArgb()
            // 根据主题模式设置状态栏图标颜色
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = !darkTheme
            // 让内容延伸到系统栏（状态栏和导航栏）
            WindowCompat.setDecorFitsSystemWindows(window, false)
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = typography,
        content = content
    )
}