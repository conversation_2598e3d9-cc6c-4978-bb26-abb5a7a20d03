package com.shuyi.discipline.ui.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import com.shuyi.discipline.ui.screen.collage.CollageDetailScreen
import com.shuyi.discipline.ui.screen.collage.CollageListScreen
import com.shuyi.discipline.ui.screen.collage.CollagePreviewScreen
import com.shuyi.discipline.ui.screen.config.*
import com.shuyi.discipline.ui.screen.home.HomeScreen
import com.shuyi.discipline.ui.screen.quietperiod.QuietPeriodScreen
import com.shuyi.discipline.ui.screen.screenshots.ScreenshotsScreen
import com.shuyi.discipline.ui.screen.status.StatusScreen
import com.shuyi.discipline.ui.screen.permissions.PermissionsScreen
import timber.log.Timber

/**
 * 应用导航系统
 */
@Composable
fun AppNavigation(
    navController: NavHostController,
    modifier: Modifier = Modifier
) {
    NavHost(
        navController = navController,
        startDestination = Screen.Home.route,
        modifier = modifier
    ) {
        composable(Screen.Home.route) {
            HomeScreen(
                onNavigateToConfig = {
                    navController.navigate(Screen.Config.route) {
                        // 避免创建多个实例
                        launchSingleTop = true
                    }
                },
                onNavigateToQuietPeriod = {
                    navController.navigate(Screen.QuietPeriod.route) {
                        // 避免创建多个实例
                        launchSingleTop = true
                    }
                },
                onNavigateToReportList = {
                    navController.navigate(Screen.Collage.route) {
                        // 避免创建多个实例
                        launchSingleTop = true
                    }
                },
                onNavigateToScreenshots = {
                    navController.navigate(Screen.Screenshots.route) {
                        // 避免创建多个实例
                        launchSingleTop = true
                    }
                },
                onNavigateToPermissions = {
                    navController.navigate(Screen.Permissions.route) {
                        // 避免创建多个实例
                        launchSingleTop = true
                    }
                },
                navController = navController
            )
        }

        composable(Screen.Screenshots.route) {
            ScreenshotsScreen(
                onNavigateBack = { navController.popBackStack() },
                onNavigateToHome = {
                    navController.navigate(Screen.Home.route) {
                        // 清除导航栈，确保Home是栈底
                        popUpTo(navController.graph.startDestinationId) { inclusive = true }
                    }
                },
                onNavigateToReports = {
                    navController.navigate(Screen.Collage.route) {
                        // 避免创建多个实例
                        launchSingleTop = true
                    }
                },
                onNavigateToConfig = {
                    navController.navigate(Screen.Config.route) {
                        // 避免创建多个实例
                        launchSingleTop = true
                    }
                },
                navController = navController
            )
        }

        composable(Screen.Config.route) {
            SettingsMainScreen(
                onNavigateBack = { navController.popBackStack() },
                onNavigateToHome = {
                    navController.navigate(Screen.Home.route) {
                        // 清除导航栈，确保Home是栈底
                        popUpTo(navController.graph.startDestinationId) { inclusive = true }
                    }
                },
                onNavigateToScreenshots = {
                    navController.navigate(Screen.Screenshots.route) {
                        // 避免创建多个实例
                        launchSingleTop = true
                    }
                },
                onNavigateToReports = {
                    navController.navigate(Screen.Collage.route) {
                        // 避免创建多个实例
                        launchSingleTop = true
                    }
                },
                navController = navController
            )
        }

        composable(Screen.QuietPeriod.route) {
            QuietPeriodScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }

        composable(Screen.Collage.route) {
            CollageListScreen(
                onNavigateBack = {
                    navController.navigate(Screen.Home.route) {
                        // 清除导航栈，确保Home是栈底
                        popUpTo(navController.graph.startDestinationId) { inclusive = true }
                    }
                },
                onNavigateToHome = {
                    navController.navigate(Screen.Home.route) {
                        // 清除导航栈，确保Home是栈底
                        popUpTo(navController.graph.startDestinationId) { inclusive = true }
                    }
                },
                onNavigateToCollageDetail = { dateAndIndex ->
                    // 解析日期和索引
                    try {
                        // 格式应该是 "date/index"
                        val parts = dateAndIndex.split("/")
                        if (parts.size == 2) {
                            val date = parts[0]
                            val index = parts[1].toIntOrNull() ?: 0
                            // 使用正确的参数创建路由
                            navController.navigate(Screen.CollagePreview.createRoute(date, index))
                        } else {
                            // 如果格式不正确，使用默认索引0
                            navController.navigate(Screen.CollagePreview.createRoute(dateAndIndex, 0))
                        }
                    } catch (e: Exception) {
                        // 出现异常时使用默认索引0
                        Timber.e(e, "解析拼图预览参数失败: $dateAndIndex")
                        navController.navigate(Screen.CollagePreview.createRoute(dateAndIndex, 0))
                    }
                },
                onNavigateToConfig = {
                    navController.navigate(Screen.Config.route) {
                        // 避免创建多个实例
                        launchSingleTop = true
                    }
                },
                onNavigateToScreenshots = {
                    navController.navigate(Screen.Screenshots.route) {
                        // 避免创建多个实例
                        launchSingleTop = true
                    }
                },
                navController = navController
            )
        }

        composable(
            route = Screen.CollageDetail.route,
            arguments = listOf(
                navArgument("date") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val date = backStackEntry.arguments?.getString("date") ?: ""
            CollageDetailScreen(
                date = date,
                onNavigateBack = { navController.popBackStack() },
                onNavigateToHome = {
                    navController.navigate(Screen.Home.route) {
                        popUpTo(navController.graph.startDestinationId)
                        launchSingleTop = true
                    }
                },
                onNavigateToScreenshots = {
                    navController.navigate(Screen.Screenshots.route) {
                        popUpTo(navController.graph.startDestinationId)
                        launchSingleTop = true
                    }
                },
                onNavigateToCollages = {
                    navController.navigate(Screen.Collage.route) {
                        popUpTo(navController.graph.startDestinationId)
                        launchSingleTop = true
                    }
                },
                onNavigateToConfig = {
                    navController.navigate(Screen.Config.route) {
                        popUpTo(navController.graph.startDestinationId)
                        launchSingleTop = true
                    }
                },
                onNavigateToPreview = { dateAndIndex ->
                    // 解析日期和索引
                    try {
                        // 格式应该是 "date/index"
                        val parts = dateAndIndex.split("/")
                        if (parts.size == 2) {
                            val date = parts[0]
                            val index = parts[1].toIntOrNull() ?: 0
                            // 使用正确的参数创建路由
                            navController.navigate(Screen.CollagePreview.createRoute(date, index))
                        } else {
                            // 如果格式不正确，使用默认索引0
                            navController.navigate(Screen.CollagePreview.createRoute(dateAndIndex, 0))
                        }
                    } catch (e: Exception) {
                        // 出现异常时使用默认索引0
                        Timber.e(e, "解析拼图预览参数失败: $dateAndIndex")
                        navController.navigate(Screen.CollagePreview.createRoute(dateAndIndex, 0))
                    }
                },
                navController = navController
            )
        }

        // 拼图预览页面
        composable(
            route = Screen.CollagePreview.route,
            arguments = listOf(
                navArgument("date") { type = NavType.StringType },
                navArgument("index") { type = NavType.IntType }
            )
        ) { backStackEntry ->
            val date = backStackEntry.arguments?.getString("date") ?: ""
            val index = backStackEntry.arguments?.getInt("index") ?: 0
            CollagePreviewScreen(
                date = date,
                initialIndex = index,
                onNavigateBack = { navController.popBackStack() }
            )
        }

        // 系统状态页面
        composable(Screen.Status.route) {
            StatusScreen(
                onNavigateToHome = {
                    navController.navigate(Screen.Home.route) {
                        // 清除导航栈，确保Home是栈底
                        popUpTo(navController.graph.startDestinationId) { inclusive = true }
                    }
                },
                onNavigateToScreenshots = {
                    navController.navigate(Screen.Screenshots.route) {
                        // 避免创建多个实例
                        launchSingleTop = true
                    }
                },
                onNavigateToCollages = {
                    navController.navigate(Screen.Collage.route) {
                        // 避免创建多个实例
                        launchSingleTop = true
                    }
                },
                onNavigateToConfig = {
                    navController.navigate(Screen.Config.route) {
                        // 避免创建多个实例
                        launchSingleTop = true
                    }
                }
            )
        }

        // 权限加固页面
        composable(Screen.Permissions.route) {
            PermissionsScreen(
                onNavigateToHome = {
                    navController.navigate(Screen.Home.route) {
                        // 清除导航栈，确保Home是栈底
                        popUpTo(navController.graph.startDestinationId) { inclusive = true }
                    }
                },
                onNavigateToScreenshots = {
                    navController.navigate(Screen.Screenshots.route) {
                        // 避免创建多个实例
                        launchSingleTop = true
                    }
                },
                onNavigateToCollages = {
                    navController.navigate(Screen.Collage.route) {
                        // 避免创建多个实例
                        launchSingleTop = true
                    }
                },
                onNavigateToStatus = {
                    navController.navigate(Screen.Status.route) {
                        // 避免创建多个实例
                        launchSingleTop = true
                    }
                },
                onNavigateToConfig = {
                    navController.navigate(Screen.Config.route) {
                        // 避免创建多个实例
                        launchSingleTop = true
                    }
                }
            )
        }

        // 字体大小设置页面
        composable(Screen.FontSize.route) {
            FontSizeScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }

        // 外观设置页面
        composable(Screen.AppearanceSettings.route) {
            AppearanceSettingsScreen(
                onNavigateBack = { navController.popBackStack() },
                navController = navController
            )
        }

        // 截图设置页面
        composable(Screen.ScreenshotSettings.route) {
            ScreenshotSettingsScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }

        // 拼图设置页面
        composable(Screen.CollageSettings.route) {
            CollageSettingsScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }

        // 通知设置页面
        composable(Screen.NotificationSettings.route) {
            NotificationSettingsScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }

        // 关于页面
        composable(Screen.About.route) {
            AboutScreen(
                onNavigateBack = { navController.popBackStack() }
            )
        }
    }
}