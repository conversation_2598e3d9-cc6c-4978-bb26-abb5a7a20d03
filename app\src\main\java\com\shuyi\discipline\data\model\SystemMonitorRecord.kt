package com.shuyi.discipline.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 系统监控记录数据模型
 */
@Entity(tableName = "system_monitor_records")
data class SystemMonitorRecord(
    @PrimaryKey val id: String, // UUID
    val timestamp: Long, // 记录时间戳
    val monitorType: MonitorType, // 监控类型
    val status: MonitorStatus, // 监控状态
    val startTime: Long, // 异常开始时间
    val endTime: Long? = null, // 异常结束时间（null表示仍在异常状态）
    val duration: Long = 0L, // 异常持续时长（毫秒）
    val description: String, // 异常描述
    val isDeviceShutdown: Boolean = false // 是否因设备关机导致
)

/**
 * 监控类型枚举
 */
enum class MonitorType {
    APP_BACKGROUND_RUNNING, // 应用后台运行监控
    SCREENSHOT_FUNCTION // 自动截屏功能监控
}

/**
 * 监控状态枚举
 */
enum class MonitorStatus {
    NORMAL, // 正常状态
    ABNORMAL, // 异常状态
    RECOVERED // 已恢复状态
}

/**
 * 监控统计数据
 */
data class MonitorStatistics(
    val monitorType: MonitorType,
    val totalTime: Long, // 总监控时间（24小时内）
    val normalTime: Long, // 正常运行时间
    val abnormalTime: Long, // 异常时间
    val normalPercentage: Float, // 正常运行百分比
    val abnormalRecords: List<SystemMonitorRecord> // 异常记录列表
)
