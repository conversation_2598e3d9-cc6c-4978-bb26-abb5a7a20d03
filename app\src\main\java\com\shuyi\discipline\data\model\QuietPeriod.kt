package com.shuyi.discipline.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.shuyi.discipline.data.source.database.Converters
import java.util.UUID

/**
 * 免打扰时段数据模型
 */
@Entity(tableName = "quiet_periods")
@TypeConverters(Converters::class)
data class QuietPeriod(
    @PrimaryKey val id: String = UUID.randomUUID().toString(),
    val startHour: Int, // 开始小时
    val startMinute: Int, // 开始分钟
    val endHour: Int, // 结束小时
    val endMinute: Int, // 结束分钟
    val repeatPattern: Int, // 重复模式：0=每日, 1=工作日, 2=周末, 3=自定义
    val customDays: List<Int> = emptyList(), // 自定义天数（仅当repeatPattern=3时有效）
    val isEnabled: Boolean = true, // 是否启用
    val createdAt: Long = System.currentTimeMillis(), // 创建时间
    val updatedAt: Long = System.currentTimeMillis() // 更新时间
) 