package com.shuyi.discipline.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 拼图设置数据模型
 */
@Entity(tableName = "collage_settings")
data class CollageSettings(
    @PrimaryKey val id: Int = 1, // 只有一个拼图设置，所以ID固定为1
    val isAutoCollageEnabled: Boolean = true, // 是否启用自动拼图
    val collageHour: Int = 22, // 拼图生成时间（小时）- 默认晚上10点
    val collageMinute: Int = 0, // 拼图生成时间（分钟）
    val collageLayout: CollageLayout = CollageLayout.GRID_4X4, // 拼图布局 - 默认4x4网格
    val collageQuality: Int = 90, // 拼图质量（1-100）
    val collageResolution: Int = 100, // 拼图分辨率（1-100）
    val collageBlur: Int = 0, // 拼图高斯模糊（0-100）
    val lastCollageTime: Long = 0L // 上次拼图生成时间
)

/**
 * 拼图布局枚举
 */
enum class CollageLayout(val displayName: String) {
    GRID_3X3("网格布局 (3x3)"),
    GRID_4X4("网格布局 (4x4)");

    companion object {
        /**
         * 根据截图数量获取最佳布局
         */
        fun getBestLayoutForCount(count: Int): CollageLayout {
            return when {
                count <= 9 -> GRID_3X3
                else -> GRID_4X4
            }
        }

        /**
         * 根据布局名称获取布局
         */
        fun fromDisplayName(name: String): CollageLayout {
            return values().find { it.displayName == name } ?: GRID_4X4 // 默认4x4网格布局
        }
    }
}

/**
 * 拼图质量枚举
 * 0% = 原始尺寸，10% = 压缩10%，...，90% = 压缩90%
 */
enum class CollageQuality(val displayName: String, val value: Int) {
    ORIGINAL("原始尺寸", 0),
    COMPRESS_10("压缩10%", 10),
    COMPRESS_20("压缩20%", 20),
    COMPRESS_30("压缩30%", 30),
    COMPRESS_40("压缩40%", 40),
    COMPRESS_50("压缩50%", 50),
    COMPRESS_60("压缩60%", 60),
    COMPRESS_70("压缩70%", 70),
    COMPRESS_80("压缩80%", 80),
    COMPRESS_90("压缩90%", 90);

    companion object {
        /**
         * 根据质量名称获取质量
         */
        fun fromDisplayName(name: String): CollageQuality {
            return values().find { it.displayName == name } ?: COMPRESS_50
        }

        /**
         * 根据质量值获取质量
         */
        fun fromValue(value: Int): CollageQuality {
            return when (value) {
                0 -> ORIGINAL
                10 -> COMPRESS_10
                20 -> COMPRESS_20
                30 -> COMPRESS_30
                40 -> COMPRESS_40
                50 -> COMPRESS_50
                60 -> COMPRESS_60
                70 -> COMPRESS_70
                80 -> COMPRESS_80
                90 -> COMPRESS_90
                else -> COMPRESS_50 // 默认值
            }
        }

        /**
         * 获取显示文本（用于Slider）
         */
        fun getDisplayText(value: Int): String {
            return if (value == 0) "原始尺寸" else "压缩${value}%"
        }
    }
}
