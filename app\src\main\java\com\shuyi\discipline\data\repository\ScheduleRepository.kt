package com.shuyi.discipline.data.repository

import com.shuyi.discipline.data.model.ScheduleRule
import kotlinx.coroutines.flow.Flow

/**
 * 调度规则仓库接口
 */
interface ScheduleRepository {
    
    /**
     * 获取调度规则
     */
    suspend fun getScheduleRule(): ScheduleRule?
    
    /**
     * 获取调度规则流
     */
    fun getScheduleRuleFlow(): Flow<ScheduleRule?>
    
    /**
     * 保存调度规则
     */
    suspend fun saveScheduleRule(scheduleRule: ScheduleRule)
    
    /**
     * 更新上次截图时间
     */
    suspend fun updateLastCaptureTime(timestamp: Long)
    
    /**
     * 更新服务状态
     */
    suspend fun updateServiceStatus(isEnabled: Boolean)
    
    /**
     * 计算下次截图时间
     */
    suspend fun calculateNextCaptureTime(): Long
} 