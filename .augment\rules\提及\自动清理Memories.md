---
type: "manual"
description: "Example description"
---
### 主要用途

用于清理过时的和无效的Augment Memories时，你通过本地文件路径找到具体的问题，进行内容清理。

### 本地文件路径

C:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\ad43f96af2bac9608d42ea4bdeccaa96\Augment.vscode-augment\Augment-Memories

如果找不到上述这个文件的路径，那可能是我写错了，你就提醒我更新一下这个路径。

### 清理哪些记忆

#### 应该删除：

- ❌ 已经过时的技术偏好
- ❌ 已解决的具体bug记录
- ❌ 矛盾或错误的信息
- ❌ 过于具体的一次性任务记录
- ❌ 重复的记忆条目

#### 应该保留：

- ✅ 长期的开发偏好和规则
- ✅ 项目架构和设计原则
- ✅ 重要的配置偏好
- ✅ 核心的工作流程偏好

### 何时使用

当用户提出相关的要求时，比如：

- 清理Augment Memories的过时、无效的记忆
- 直接使用@提及该文件，让清理Memories

# 
