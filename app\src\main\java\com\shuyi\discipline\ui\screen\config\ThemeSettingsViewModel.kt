package com.shuyi.discipline.ui.screen.config

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.shuyi.discipline.data.model.FontSize
import com.shuyi.discipline.data.model.ThemeMode
import com.shuyi.discipline.data.model.ThemeSettings
import com.shuyi.discipline.data.repository.ThemeSettingsRepository
import com.shuyi.discipline.ui.model.UiState
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * 主题设置ViewModel
 */
@HiltViewModel
class ThemeSettingsViewModel @Inject constructor(
    application: Application,
    private val themeSettingsRepository: ThemeSettingsRepository
) : AndroidViewModel(application) {

    // 主题设置状态
    private val _themeSettings = MutableStateFlow<UiState<ThemeSettings>>(UiState.Loading)
    val themeSettings: StateFlow<UiState<ThemeSettings>> = _themeSettings

    // 主题模式选项
    val themeModeOptions = ThemeMode.values().map { it.displayName }

    // 初始化
    init {
        loadThemeSettings()
    }

    /**
     * 加载主题设置
     */
    private fun loadThemeSettings() {
        viewModelScope.launch {
            _themeSettings.value = UiState.Loading

            themeSettingsRepository.getThemeSettings()
                .catch { e ->
                    Timber.e(e, "加载主题设置失败")
                    _themeSettings.value = UiState.Error(e.message ?: "未知错误")
                }
                .collect { settings ->
                    _themeSettings.value = UiState.Success(settings)
                }
        }
    }

    /**
     * 更新主题模式
     */
    fun updateThemeMode(themeModeName: String) {
        val themeMode = ThemeMode.fromDisplayName(themeModeName)
        updateSettings { currentSettings ->
            currentSettings.copy(themeMode = themeMode)
        }
    }

    /**
     * 更新动态颜色启用状态
     */
    fun updateDynamicColorEnabled(enabled: Boolean) {
        updateSettings { currentSettings ->
            currentSettings.copy(isDynamicColorEnabled = enabled)
        }
    }

    /**
     * 更新字体大小
     */
    fun updateFontSize(fontSize: FontSize) {
        updateSettings { currentSettings ->
            currentSettings.copy(fontSize = fontSize)
        }
    }

    /**
     * 更新设置
     */
    private fun updateSettings(update: (ThemeSettings) -> ThemeSettings) {
        val currentState = _themeSettings.value
        if (currentState is UiState.Success) {
            val updatedSettings = update(currentState.data)

            viewModelScope.launch {
                try {
                    themeSettingsRepository.saveThemeSettings(updatedSettings)
                    _themeSettings.value = UiState.Success(updatedSettings)
                    Timber.d("主题设置更新成功")
                } catch (e: Exception) {
                    Timber.e(e, "更新主题设置失败")
                    _themeSettings.value = UiState.Error(e.message ?: "更新失败")
                }
            }
        }
    }
}
