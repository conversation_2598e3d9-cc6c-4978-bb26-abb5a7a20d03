package com.shuyi.discipline.data.source.database

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.shuyi.discipline.data.model.CollageSettings
import kotlinx.coroutines.flow.Flow

/**
 * 拼图设置数据访问对象
 */
@Dao
interface CollageSettingsDao {
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCollageSettings(collageSettings: CollageSettings)
    
    @Query("SELECT * FROM collage_settings WHERE id = 1")
    fun getCollageSettings(): Flow<CollageSettings?>
    
    @Query("SELECT * FROM collage_settings WHERE id = 1")
    suspend fun getCollageSettingsSync(): CollageSettings?
    
    @Query("UPDATE collage_settings SET lastCollageTime = :time WHERE id = 1")
    suspend fun updateLastCollageTime(time: Long)
    
    @Query("UPDATE collage_settings SET isAutoCollageEnabled = :enabled WHERE id = 1")
    suspend fun updateAutoCollageEnabled(enabled: Boolean)
    
    @Query("UPDATE collage_settings SET collageHour = :hour, collageMinute = :minute WHERE id = 1")
    suspend fun updateCollageTime(hour: Int, minute: Int)
}
