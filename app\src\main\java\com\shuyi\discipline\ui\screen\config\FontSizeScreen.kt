package com.shuyi.discipline.ui.screen.config

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import com.shuyi.discipline.data.model.FontSize
import com.shuyi.discipline.data.model.ThemeSettings
import com.shuyi.discipline.ui.model.UiState

/**
 * 字体大小设置页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FontSizeScreen(
    onNavigateBack: () -> Unit,
    themeSettingsViewModel: ThemeSettingsViewModel = hiltViewModel()
) {
    val themeSettingsState by themeSettingsViewModel.themeSettings.collectAsState()
    val scrollState = rememberScrollState()

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Text(
                        text = "字体大小",
                        style = MaterialTheme.typography.titleLarge
                    )
                },
                navigationIcon = {
                    IconButton(onClick = onNavigateBack) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回"
                        )
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = MaterialTheme.colorScheme.background
                )
            )
        }
    ) { paddingValues ->
        val currentThemeSettingsState = themeSettingsState
        when (currentThemeSettingsState) {
            is UiState.Success -> {
                val themeSettings = currentThemeSettingsState.data
                FontSizeContent(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(paddingValues)
                        .background(MaterialTheme.colorScheme.background),
                    currentFontSize = themeSettings.fontSize,
                    onFontSizeChanged = { fontSize ->
                        themeSettingsViewModel.updateFontSize(fontSize)
                    },
                    scrollState = scrollState
                )
            }
            is UiState.Loading -> {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(paddingValues),
                    contentAlignment = Alignment.Center
                ) {
                    CircularProgressIndicator()
                }
            }
            is UiState.Error -> {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(paddingValues),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "加载失败: ${currentThemeSettingsState.message}",
                        color = MaterialTheme.colorScheme.error
                    )
                }
            }
            else -> {
                // 其他状态
            }
        }
    }
}

/**
 * 字体大小设置内容
 */
@Composable
fun FontSizeContent(
    modifier: Modifier = Modifier,
    currentFontSize: FontSize,
    onFontSizeChanged: (FontSize) -> Unit,
    scrollState: androidx.compose.foundation.ScrollState
) {
    Column(
        modifier = modifier
            .verticalScroll(scrollState)
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(24.dp)
    ) {
        // 预览文字
        FontSizePreviewCard(currentFontSize = currentFontSize)

        // 字体大小选择器
        FontSizeSelector(
            currentFontSize = currentFontSize,
            onFontSizeChanged = onFontSizeChanged
        )
    }
}

/**
 * 字体大小预览卡片
 */
@Composable
fun FontSizePreviewCard(currentFontSize: FontSize) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 预览标题
            Text(
                text = "预览字体大小",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.primary
            )

            // 说明文字
            Text(
                text = "拖动下面的滑块，可设置字体大小",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            // 设置后说明
            Text(
                text = "设置后，会改变聊天和朋友圈的字体大小。如果在使用过程中存在问题或意见，可反馈给微信团队",
                style = MaterialTheme.typography.bodyMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )

            // 24小时运行状态样板
            StatusPreviewSample(currentFontSize = currentFontSize)
        }
    }
}

/**
 * 状态预览样板
 */
@Composable
fun StatusPreviewSample(currentFontSize: FontSize) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(12.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surfaceVariant.copy(alpha = 0.5f))
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            // 标题
            Text(
                text = "24小时运行状态",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface
            )

            // 状态项
            StatusSampleItem(
                title = "未运行总时长",
                duration = "11小时17分钟",
                color = MaterialTheme.colorScheme.error,
                currentFontSize = currentFontSize
            )

            StatusSampleItem(
                title = "App未运行时长",
                duration = "9小时13分钟",
                color = MaterialTheme.colorScheme.primary,
                currentFontSize = currentFontSize
            )

            StatusSampleItem(
                title = "截图服务未运行时长",
                duration = "16小时4分钟",
                color = MaterialTheme.colorScheme.secondary,
                currentFontSize = currentFontSize
            )
        }
    }
}

/**
 * 状态样板项
 */
@Composable
fun StatusSampleItem(
    title: String,
    duration: String,
    color: Color,
    currentFontSize: FontSize
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 状态指示点
        Box(
            modifier = Modifier
                .size(10.dp)
                .background(color, CircleShape)
        )

        Spacer(modifier = Modifier.width(8.dp))

        // 标题
        Text(
            text = title,
            style = MaterialTheme.typography.bodyMedium,
            modifier = Modifier.weight(1f),
            color = MaterialTheme.colorScheme.onSurface
        )

        // 时长
        Text(
            text = duration,
            style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.SemiBold),
            color = color
        )
    }
}

/**
 * 字体大小选择器
 */
@Composable
fun FontSizeSelector(
    currentFontSize: FontSize,
    onFontSizeChanged: (FontSize) -> Unit
) {
    val fontSizes = FontSize.values()
    var sliderValue by remember(currentFontSize) {
        mutableFloatStateOf(currentFontSize.value.toFloat())
    }

    Column(
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        // 滑块
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 左侧标签
            Text(
                text = "A",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface
            )

            // 滑块
            Slider(
                value = sliderValue,
                onValueChange = { value ->
                    sliderValue = value
                    val newFontSize = fontSizes[value.toInt().coerceIn(0, fontSizes.size - 1)]
                    onFontSizeChanged(newFontSize)
                },
                valueRange = 0f..(fontSizes.size - 1).toFloat(),
                steps = fontSizes.size - 2,
                modifier = Modifier.weight(1f)
            )

            // 右侧标签
            Text(
                text = "A",
                style = MaterialTheme.typography.titleLarge,
                color = MaterialTheme.colorScheme.onSurface
            )
        }

        // 当前字体大小标签
        Text(
            text = "当前: ${currentFontSize.displayName}",
            style = MaterialTheme.typography.bodySmall,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            modifier = Modifier.fillMaxWidth()
        )
    }
}

@Preview(showBackground = true)
@Composable
fun FontSizeScreenPreview() {
    MaterialTheme {
        FontSizeScreen(
            onNavigateBack = {}
        )
    }
}
