package com.shuyi.discipline.data.repository.impl

import com.shuyi.discipline.data.model.NotificationSettings
import com.shuyi.discipline.data.repository.NotificationSettingsRepository
import com.shuyi.discipline.data.source.database.NotificationSettingsDao
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

/**
 * 通知设置仓库实现类
 */
class NotificationSettingsRepositoryImpl @Inject constructor(
    private val notificationSettingsDao: NotificationSettingsDao
) : NotificationSettingsRepository {
    
    override fun getNotificationSettings(): Flow<NotificationSettings> {
        return notificationSettingsDao.getNotificationSettings().map { 
            it ?: NotificationSettings() 
        }
    }
    
    override suspend fun getNotificationSettingsSync(): NotificationSettings {
        return notificationSettingsDao.getNotificationSettingsSync() ?: NotificationSettings()
    }
    
    override suspend fun saveNotificationSettings(notificationSettings: NotificationSettings) {
        notificationSettingsDao.insertNotificationSettings(notificationSettings)
    }
    
    override suspend fun updateScreenshotNotificationEnabled(enabled: Boolean) {
        val settings = getNotificationSettingsSync()
        saveNotificationSettings(settings.copy(isScreenshotNotificationEnabled = enabled))
    }
    
    override suspend fun updateCollageNotificationEnabled(enabled: Boolean) {
        val settings = getNotificationSettingsSync()
        saveNotificationSettings(settings.copy(isCollageNotificationEnabled = enabled))
    }
}
