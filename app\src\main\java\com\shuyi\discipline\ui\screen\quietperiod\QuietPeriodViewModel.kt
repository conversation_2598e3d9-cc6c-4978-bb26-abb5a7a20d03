package com.shuyi.discipline.ui.screen.quietperiod

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.shuyi.discipline.data.model.QuietPeriod
import com.shuyi.discipline.data.repository.QuietPeriodRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.Calendar
import java.util.UUID

/**
 * 免打扰时段界面的ViewModel
 */
class QuietPeriodViewModel(
    private val quietPeriodRepository: QuietPeriodRepository
) : ViewModel() {

    // 免打扰时段列表状态
    private val _quietPeriods = MutableStateFlow<List<QuietPeriod>>(emptyList())
    val quietPeriods: StateFlow<List<QuietPeriod>> = _quietPeriods.asStateFlow()

    // 加载状态
    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    init {
        loadQuietPeriods()
    }

    /**
     * 加载所有免打扰时段
     */
    fun loadQuietPeriods() {
        viewModelScope.launch {
            _isLoading.value = true
            try {
                quietPeriodRepository.getAllQuietPeriods().collect { periods ->
                    _quietPeriods.value = periods
                }
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 添加新的免打扰时段
     */
    fun addQuietPeriod(
        startHour: Int,
        startMinute: Int,
        endHour: Int,
        endMinute: Int,
        patternId: Int,
        selectedDays: List<Int> = emptyList()
    ) {
        viewModelScope.launch {
            val currentTime = Calendar.getInstance().timeInMillis
            val newQuietPeriod = QuietPeriod(
                id = UUID.randomUUID().toString(),
                startHour = startHour,
                startMinute = startMinute,
                endHour = endHour,
                endMinute = endMinute,
                repeatPattern = patternId,
                customDays = if (patternId == QuietPeriodPatterns.PATTERN_CUSTOM) selectedDays else emptyList(),
                isEnabled = true,
                createdAt = currentTime,
                updatedAt = currentTime
            )
            quietPeriodRepository.saveQuietPeriod(newQuietPeriod)
        }
    }

    /**
     * 删除免打扰时段
     */
    fun deleteQuietPeriod(quietPeriod: QuietPeriod) {
        viewModelScope.launch {
            // 通过ID进行删除
            try {
                // 尝试将字符串ID转换为整数
                val id = quietPeriod.id.toIntOrNull()
                if (id != null) {
                    quietPeriodRepository.deleteQuietPeriod(id)
                } else {
                    // 如果字符串ID无法转换为整数，需要通过扩展QuietPeriodRepository接口来支持删除对象
                    // 这里为临时解决方案
                    // 理想情况应该在Repository接口中添加deleteQuietPeriod(quietPeriod: QuietPeriod)方法
                }
            } catch (e: Exception) {
                // 异常处理
            }
        }
    }

    /**
     * 更新免打扰时段启用状态
     */
    fun updateQuietPeriodStatus(quietPeriod: QuietPeriod, isEnabled: Boolean) {
        viewModelScope.launch {
            val updatedPeriod = quietPeriod.copy(
                isEnabled = isEnabled,
                updatedAt = Calendar.getInstance().timeInMillis
            )
            quietPeriodRepository.updateQuietPeriod(updatedPeriod)
        }
    }
} 