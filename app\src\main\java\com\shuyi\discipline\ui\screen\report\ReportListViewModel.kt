package com.shuyi.discipline.ui.screen.report

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.shuyi.discipline.data.repository.ScreenshotRepository
import com.shuyi.discipline.ui.model.UiState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter

/**
 * 报告列表ViewModel
 */
class ReportListViewModel(
    private val screenshotRepository: ScreenshotRepository
) : ViewModel() {
    
    private val _reports = MutableStateFlow<UiState<List<ReportInfo>>>(UiState.Loading)
    val reports: StateFlow<UiState<List<ReportInfo>>> = _reports
    
    /**
     * 加载报告列表
     */
    fun loadReports() {
        viewModelScope.launch {
            try {
                // 获取最近30天的日期，并检查每一天是否有截图
                val today = LocalDate.now()
                val reportList = mutableListOf<ReportInfo>()
                
                // 从数据库查询所有截图，按日期分组
                screenshotRepository.getAllScreenshots().collect { allScreenshots ->
                    // 按日期分组
                    val screenshotsByDate = allScreenshots.groupBy { screenshot ->
                        // 从时间戳提取日期
                        Instant.ofEpochMilli(screenshot.timestamp)
                            .atZone(ZoneId.systemDefault())
                            .toLocalDate()
                    }
                    
                    // 为每个有截图的日期创建报告信息
                    screenshotsByDate.forEach { (date, screenshots) ->
                        val formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日")
                        reportList.add(
                            ReportInfo(
                                date = date.toString(),
                                dateFormatted = date.format(formatter),
                                screenshotCount = screenshots.size
                            )
                        )
                    }
                    
                    // 按日期降序排序
                    reportList.sortByDescending { it.date }
                    
                    if (reportList.isEmpty()) {
                        _reports.value = UiState.Empty
                    } else {
                        _reports.value = UiState.Success(reportList)
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "加载报告列表失败")
                _reports.value = UiState.Error(e.message ?: "未知错误")
            }
        }
    }
}

/**
 * 报告信息数据类
 */
data class ReportInfo(
    val date: String,
    val dateFormatted: String,
    val screenshotCount: Int
) 