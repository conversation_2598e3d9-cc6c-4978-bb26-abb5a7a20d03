package com.shuyi.discipline

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.media.projection.MediaProjectionManager
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.compose.rememberNavController
import com.shuyi.discipline.domain.service.ScreenshotService
import com.shuyi.discipline.ui.navigation.AppNavigation
import com.shuyi.discipline.ui.theme.DisciplineselfTheme
import com.shuyi.discipline.ui.theme.ThemeManager
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class MainActivity : ComponentActivity() {

    @Inject
    lateinit var themeManager: ThemeManager

    private val mediaProjectionManager by lazy {
        getSystemService(MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
    }

    private val mediaProjectionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK && result.data != null) {
            // 将权限结果传递给服务
            val serviceIntent = Intent(this, ScreenshotService::class.java).apply {
                action = ScreenshotService.ACTION_START_SERVICE
                putExtra("media_projection_result_code", result.resultCode)
                putExtra("media_projection_data", result.data)
            }

            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                startForegroundService(serviceIntent)
            } else {
                startService(serviceIntent)
            }

            Timber.d("成功获取媒体投影权限")
        } else {
            Toast.makeText(this, "需要屏幕录制权限才能使用自律助手", Toast.LENGTH_LONG).show()
            Timber.e("未能获取媒体投影权限")

            // 发送广播通知权限被拒绝
            val permissionDeniedIntent = Intent("com.shuyi.discipline.PROJECTION_PERMISSION_DENIED")
            sendBroadcast(permissionDeniedIntent)
            Timber.d("发送媒体投影权限被拒绝的广播")
        }
    }

    // 电池优化设置启动器
    private val batteryOptimizationLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        // 无论结果如何，都检查电池优化状态
        val intent = Intent("com.shuyi.discipline.CHECK_BATTERY_OPTIMIZATION")
        sendBroadcast(intent)
        Timber.d("电池优化设置返回，发送检查电池优化状态的广播")
    }

    // 广播接收器，用于接收权限请求
    private val requestReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                "com.shuyi.discipline.REQUEST_PROJECTION" -> {
                    Timber.d("收到请求媒体投影权限的广播")
                    requestScreenCapturePermission()
                }
                "com.shuyi.discipline.REQUEST_IGNORE_BATTERY_OPTIMIZATION" -> {
                    Timber.d("收到请求忽略电池优化的广播")
                    requestIgnoreBatteryOptimization()
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()

        Timber.d("MainActivity onCreate被调用")

        // 注册广播接收器
        val filter = IntentFilter().apply {
            addAction("com.shuyi.discipline.REQUEST_PROJECTION")
            addAction("com.shuyi.discipline.REQUEST_IGNORE_BATTERY_OPTIMIZATION")
        }
        registerReceiver(requestReceiver, filter)

        setContent {
            DisciplineselfTheme(themeManager = themeManager) {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    DisciplineSelfApp()
                }
            }
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        // 注销广播接收器
        try {
            unregisterReceiver(requestReceiver)
        } catch (e: Exception) {
            Timber.e(e, "注销广播接收器失败")
        }
    }

    /**
     * 请求屏幕截图权限
     */
    fun requestScreenCapturePermission() {
        val captureIntent = mediaProjectionManager.createScreenCaptureIntent()
        mediaProjectionLauncher.launch(captureIntent)
    }

    /**
     * 请求忽略电池优化
     */
    fun requestIgnoreBatteryOptimization() {
        try {
            // 直接创建请求忽略电池优化的Intent
            val packageName = packageName
            val intent = Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                data = Uri.parse("package:$packageName")
            }

            // 启动电池优化设置
            batteryOptimizationLauncher.launch(intent)
        } catch (e: Exception) {
            Timber.e(e, "请求忽略电池优化失败")
            Toast.makeText(this, "请在设置中手动将应用添加到电池优化白名单", Toast.LENGTH_LONG).show()
        }
    }
}

@Composable
fun DisciplineSelfApp() {
    val navController = rememberNavController()

    // 移除Scaffold的内边距，直接使用全屏布局
    AppNavigation(
        navController = navController,
        modifier = Modifier.fillMaxSize()
    )
}