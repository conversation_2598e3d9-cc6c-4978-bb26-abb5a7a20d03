package com.shuyi.discipline.data.source.database

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.shuyi.discipline.data.model.CollageLayout
import com.shuyi.discipline.data.model.MonitorStatus
import com.shuyi.discipline.data.model.MonitorType
import com.shuyi.discipline.data.model.ThemeMode
import java.util.Date

/**
 * Room数据库类型转换器
 */
class Converters {
    private val gson = Gson()

    @TypeConverter
    fun fromStringList(value: List<String>?): String {
        return gson.toJson(value ?: emptyList<String>())
    }

    @TypeConverter
    fun toStringList(value: String): List<String> {
        val listType = object : TypeToken<List<String>>() {}.type
        return gson.fromJson(value, listType) ?: emptyList()
    }

    @TypeConverter
    fun fromIntList(value: List<Int>?): String {
        return gson.toJson(value ?: emptyList<Int>())
    }

    @TypeConverter
    fun toIntList(value: String): List<Int> {
        val listType = object : TypeToken<List<Int>>() {}.type
        return gson.fromJson(value, listType) ?: emptyList()
    }

    @TypeConverter
    fun fromDate(date: Date?): Long? {
        return date?.time
    }

    @TypeConverter
    fun toDate(timestamp: Long?): Date? {
        return timestamp?.let { Date(it) }
    }

    @TypeConverter
    fun fromCollageLayout(value: CollageLayout): String {
        return value.name
    }

    @TypeConverter
    fun toCollageLayout(value: String): CollageLayout {
        return try {
            CollageLayout.valueOf(value)
        } catch (e: Exception) {
            CollageLayout.GRID_4X4 // 默认值 - 4x4网格布局
        }
    }

    @TypeConverter
    fun fromThemeMode(value: ThemeMode): String {
        return value.name
    }

    @TypeConverter
    fun toThemeMode(value: String): ThemeMode {
        return try {
            ThemeMode.valueOf(value)
        } catch (e: Exception) {
            ThemeMode.FOLLOW_SYSTEM // 默认值
        }
    }

    @TypeConverter
    fun fromMonitorType(value: MonitorType): String {
        return value.name
    }

    @TypeConverter
    fun toMonitorType(value: String): MonitorType {
        return try {
            MonitorType.valueOf(value)
        } catch (e: Exception) {
            MonitorType.APP_BACKGROUND_RUNNING // 默认值
        }
    }

    @TypeConverter
    fun fromMonitorStatus(value: MonitorStatus): String {
        return value.name
    }

    @TypeConverter
    fun toMonitorStatus(value: String): MonitorStatus {
        return try {
            MonitorStatus.valueOf(value)
        } catch (e: Exception) {
            MonitorStatus.NORMAL // 默认值
        }
    }
}