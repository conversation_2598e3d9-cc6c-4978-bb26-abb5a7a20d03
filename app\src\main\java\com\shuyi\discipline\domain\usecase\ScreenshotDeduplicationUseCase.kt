package com.shuyi.discipline.domain.usecase

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import com.shuyi.discipline.data.model.DeduplicationResult
import com.shuyi.discipline.data.repository.ScreenshotDeduplicationRepository
import com.shuyi.discipline.data.repository.ScreenshotRepository
import com.shuyi.discipline.utils.ImageHashCalculator
import com.shuyi.discipline.utils.SensitivityLevel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 截图去重业务逻辑用例
 */
@Singleton
class ScreenshotDeduplicationUseCase @Inject constructor(
    private val deduplicationRepository: ScreenshotDeduplicationRepository,
    private val screenshotRepository: ScreenshotRepository
) {
    
    /**
     * 处理新截图的去重检测
     * @param screenshotPath 新截图的文件路径
     * @return 去重结果
     */
    suspend fun processScreenshot(screenshotPath: String): DeduplicationResult = withContext(Dispatchers.IO) {
        try {
            // 检查去重功能是否启用
            if (!deduplicationRepository.isDeduplicationEnabled()) {
                Timber.d("去重功能未启用，跳过处理")
                return@withContext DeduplicationResult.unique(0, 64).copy(
                    reason = "去重功能未启用"
                )
            }
            
            // 检查文件是否存在
            val screenshotFile = File(screenshotPath)
            if (!screenshotFile.exists()) {
                Timber.e("截图文件不存在: $screenshotPath")
                return@withContext DeduplicationResult.error("截图文件不存在")
            }
            
            // 计算新截图的哈希值
            val newHash = calculateImageHash(screenshotPath)
            if (!ImageHashCalculator.isValidHash(newHash)) {
                Timber.e("计算新截图哈希值失败")
                return@withContext DeduplicationResult.error("计算哈希值失败")
            }
            
            // 获取上一张截图的哈希值
            val lastHash = deduplicationRepository.getLastScreenshotHash()
            
            // 如果是第一张截图
            if (lastHash == 0L) {
                Timber.d("首张截图，无需对比")
                updateScreenshotInfo(newHash, screenshotPath, false)
                return@withContext DeduplicationResult.firstScreenshot()
            }
            
            // 计算相似度
            val hammingDistance = ImageHashCalculator.calculateHammingDistance(newHash, lastHash)
            val similarityPercentage = ImageHashCalculator.calculateSimilarityPercentage(newHash, lastHash)
            val sensitivityLevel = deduplicationRepository.getSensitivityLevel()
            
            // 判断是否为重复图片
            val isDuplicate = ImageHashCalculator.isDuplicate(newHash, lastHash, sensitivityLevel.threshold)
            
            if (isDuplicate) {
                // 重复图片，删除文件并更新统计
                val fileSize = screenshotFile.length()
                val spaceSavedMb = fileSize / (1024.0 * 1024.0)
                
                if (deleteScreenshotFile(screenshotPath)) {
                    updateScreenshotInfo(lastHash, "", true, spaceSavedMb)
                    Timber.i("检测到重复截图并删除: $screenshotPath, 相似度: $similarityPercentage%, 节省空间: ${String.format("%.2f", spaceSavedMb)}MB")
                    return@withContext DeduplicationResult.duplicate(similarityPercentage, hammingDistance)
                } else {
                    Timber.e("删除重复截图失败: $screenshotPath")
                    updateScreenshotInfo(newHash, screenshotPath, false)
                    return@withContext DeduplicationResult.error("删除重复截图失败")
                }
            } else {
                // 非重复图片，更新哈希值
                updateScreenshotInfo(newHash, screenshotPath, false)
                Timber.d("截图不重复，保存: $screenshotPath, 相似度: $similarityPercentage%")
                return@withContext DeduplicationResult.unique(similarityPercentage, hammingDistance)
            }
            
        } catch (e: Exception) {
            Timber.e(e, "处理截图去重失败: $screenshotPath")
            return@withContext DeduplicationResult.error("处理失败: ${e.message}")
        }
    }
    
    /**
     * 计算图片的哈希值
     */
    private suspend fun calculateImageHash(imagePath: String): Long = withContext(Dispatchers.IO) {
        try {
            val bitmap = BitmapFactory.decodeFile(imagePath)
            if (bitmap == null) {
                Timber.e("无法解码图片: $imagePath")
                return@withContext 0L
            }
            
            val hash = ImageHashCalculator.calculateDHashOptimized(bitmap)
            bitmap.recycle()
            
            return@withContext hash
        } catch (e: Exception) {
            Timber.e(e, "计算图片哈希值失败: $imagePath")
            return@withContext 0L
        }
    }
    
    /**
     * 删除截图文件和数据库记录
     */
    private suspend fun deleteScreenshotFile(filePath: String): Boolean {
        return try {
            val file = File(filePath)

            // 先删除文件
            val fileDeleted = file.delete()
            if (!fileDeleted) {
                Timber.e("删除重复截图文件失败: $filePath")
                return false
            }

            // 再删除数据库记录
            val recordDeleted = screenshotRepository.deleteScreenshotByPath(filePath)
            if (recordDeleted > 0) {
                Timber.d("成功删除重复截图文件和数据库记录: $filePath")
                true
            } else {
                Timber.w("删除截图文件成功但数据库记录删除失败: $filePath")
                // 即使数据库删除失败，文件已删除，仍然返回true
                true
            }
        } catch (e: Exception) {
            Timber.e(e, "删除截图文件和记录异常: $filePath")
            false
        }
    }
    
    /**
     * 更新截图信息
     */
    private suspend fun updateScreenshotInfo(
        hash: Long, 
        path: String, 
        isDuplicate: Boolean, 
        spaceSavedMb: Double = 0.0
    ) {
        try {
            // 更新哈希值和路径
            if (!isDuplicate) {
                deduplicationRepository.updateLastScreenshotHash(hash, path)
            }
            
            // 更新统计信息
            if (isDuplicate) {
                deduplicationRepository.updateDuplicateStats(spaceSavedMb)
            } else {
                deduplicationRepository.updateProcessedCount()
            }
        } catch (e: Exception) {
            Timber.e(e, "更新截图信息失败")
        }
    }
    
    /**
     * 批量处理现有截图的去重（用于手动扫描）
     */
    suspend fun batchProcessScreenshots(screenshotPaths: List<String>): List<DeduplicationResult> = withContext(Dispatchers.IO) {
        val results = mutableListOf<DeduplicationResult>()
        
        try {
            if (!deduplicationRepository.isDeduplicationEnabled()) {
                Timber.d("去重功能未启用，跳过批量处理")
                return@withContext emptyList()
            }
            
            val sensitivityLevel = deduplicationRepository.getSensitivityLevel()
            var lastHash = 0L
            
            for ((index, path) in screenshotPaths.withIndex()) {
                try {
                    val currentHash = calculateImageHash(path)
                    if (!ImageHashCalculator.isValidHash(currentHash)) {
                        results.add(DeduplicationResult.error("计算哈希值失败: $path"))
                        continue
                    }
                    
                    if (index == 0) {
                        // 第一张图片
                        lastHash = currentHash
                        results.add(DeduplicationResult.firstScreenshot())
                        continue
                    }
                    
                    val hammingDistance = ImageHashCalculator.calculateHammingDistance(currentHash, lastHash)
                    val similarityPercentage = ImageHashCalculator.calculateSimilarityPercentage(currentHash, lastHash)
                    val isDuplicate = ImageHashCalculator.isDuplicate(currentHash, lastHash, sensitivityLevel.threshold)
                    
                    if (isDuplicate) {
                        val file = File(path)
                        val spaceSavedMb = file.length() / (1024.0 * 1024.0)

                        if (deleteScreenshotFile(path)) {
                            results.add(DeduplicationResult.duplicate(similarityPercentage, hammingDistance))
                            deduplicationRepository.updateDuplicateStats(spaceSavedMb)
                        } else {
                            results.add(DeduplicationResult.error("删除失败: $path"))
                        }
                    } else {
                        lastHash = currentHash
                        results.add(DeduplicationResult.unique(similarityPercentage, hammingDistance))
                        deduplicationRepository.updateProcessedCount()
                    }
                    
                } catch (e: Exception) {
                    Timber.e(e, "处理截图失败: $path")
                    results.add(DeduplicationResult.error("处理失败: ${e.message}"))
                }
            }
            
            // 更新最后一张截图的哈希值
            if (screenshotPaths.isNotEmpty() && lastHash != 0L) {
                val lastPath = screenshotPaths.lastOrNull { File(it).exists() } ?: ""
                deduplicationRepository.updateLastScreenshotHash(lastHash, lastPath)
            }
            
        } catch (e: Exception) {
            Timber.e(e, "批量处理截图失败")
        }
        
        return@withContext results
    }
}
