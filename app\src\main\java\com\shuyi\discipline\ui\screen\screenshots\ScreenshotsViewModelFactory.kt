package com.shuyi.discipline.ui.screen.screenshots

import android.app.Application
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.shuyi.discipline.data.repository.impl.ScreenshotRepositoryImpl
import com.shuyi.discipline.data.repository.impl.ScreenshotDeduplicationRepositoryImpl
import com.shuyi.discipline.data.source.database.AppDatabase

/**
 * ScreenshotsViewModel工厂类
 */
class ScreenshotsViewModelFactory(private val application: Application) : ViewModelProvider.Factory {
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(ScreenshotsViewModel::class.java)) {
            val database = AppDatabase.getInstance(application)
            val screenshotDao = database.screenshotDao()
            val screenshotRepository = ScreenshotRepositoryImpl(application, screenshotDao)
            val deduplicationDao = database.screenshotDeduplicationDao()
            val deduplicationRepository = ScreenshotDeduplicationRepositoryImpl(deduplicationDao)
            return ScreenshotsViewModel(application, screenshotRepository, deduplicationRepository) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}
