package com.shuyi.discipline.data.source.database

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.shuyi.discipline.data.model.ScreenshotDeduplication
import kotlinx.coroutines.flow.Flow

/**
 * 截图去重数据访问对象
 */
@Dao
interface ScreenshotDeduplicationDao {
    
    /**
     * 插入或更新去重设置
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertDeduplication(deduplication: ScreenshotDeduplication)
    
    /**
     * 更新去重设置
     */
    @Update
    suspend fun updateDeduplication(deduplication: ScreenshotDeduplication)
    
    /**
     * 获取去重设置（Flow）
     */
    @Query("SELECT * FROM screenshot_deduplication WHERE id = 1")
    fun getDeduplication(): Flow<ScreenshotDeduplication?>
    
    /**
     * 同步获取去重设置
     */
    @Query("SELECT * FROM screenshot_deduplication WHERE id = 1")
    suspend fun getDeduplicationSync(): ScreenshotDeduplication?
    
    /**
     * 更新上一张截图的哈希值和路径
     */
    @Query("""
        UPDATE screenshot_deduplication 
        SET lastScreenshotHash = :hash, 
            lastScreenshotPath = :path, 
            lastUpdatedTime = :updateTime 
        WHERE id = 1
    """)
    suspend fun updateLastScreenshotHash(hash: Long, path: String, updateTime: Long)
    
    /**
     * 更新去重统计信息
     */
    @Query("""
        UPDATE screenshot_deduplication 
        SET duplicateCount = duplicateCount + 1, 
            spaceSavedMb = spaceSavedMb + :spaceSavedMb,
            totalProcessedCount = totalProcessedCount + 1,
            lastUpdatedTime = :updateTime 
        WHERE id = 1
    """)
    suspend fun updateDuplicateStats(spaceSavedMb: Double, updateTime: Long)
    
    /**
     * 更新处理计数（非重复情况）
     */
    @Query("""
        UPDATE screenshot_deduplication 
        SET totalProcessedCount = totalProcessedCount + 1,
            lastUpdatedTime = :updateTime 
        WHERE id = 1
    """)
    suspend fun updateProcessedCount(updateTime: Long)
    
    /**
     * 更新去重功能启用状态
     */
    @Query("""
        UPDATE screenshot_deduplication 
        SET isEnabled = :enabled, 
            lastUpdatedTime = :updateTime 
        WHERE id = 1
    """)
    suspend fun updateEnabled(enabled: Boolean, updateTime: Long)
    
    /**
     * 更新敏感度级别
     */
    @Query("""
        UPDATE screenshot_deduplication 
        SET sensitivityLevel = :sensitivityLevel, 
            lastUpdatedTime = :updateTime 
        WHERE id = 1
    """)
    suspend fun updateSensitivityLevel(sensitivityLevel: Int, updateTime: Long)
    
    /**
     * 重置统计信息
     */
    @Query("""
        UPDATE screenshot_deduplication 
        SET duplicateCount = 0, 
            spaceSavedMb = 0.0,
            totalProcessedCount = 0,
            lastUpdatedTime = :updateTime 
        WHERE id = 1
    """)
    suspend fun resetStats(updateTime: Long)
    
    /**
     * 获取上一张截图的哈希值
     */
    @Query("SELECT lastScreenshotHash FROM screenshot_deduplication WHERE id = 1")
    suspend fun getLastScreenshotHash(): Long?
    
    /**
     * 获取去重功能启用状态
     */
    @Query("SELECT isEnabled FROM screenshot_deduplication WHERE id = 1")
    suspend fun isDeduplicationEnabled(): Boolean?
    
    /**
     * 获取敏感度级别
     */
    @Query("SELECT sensitivityLevel FROM screenshot_deduplication WHERE id = 1")
    suspend fun getSensitivityLevel(): Int?
    
    /**
     * 删除所有去重数据（用于重置）
     */
    @Query("DELETE FROM screenshot_deduplication")
    suspend fun deleteAll()
}
