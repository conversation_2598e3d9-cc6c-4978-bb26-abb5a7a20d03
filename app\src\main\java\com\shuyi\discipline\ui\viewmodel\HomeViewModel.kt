package com.shuyi.discipline.ui.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.shuyi.discipline.data.model.Screenshot
import com.shuyi.discipline.data.repository.ScreenshotRepository
import com.shuyi.discipline.ui.model.ScreenshotInfo
import com.shuyi.discipline.ui.model.UiState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.Date

/**
 * 首页ViewModel
 */
class HomeViewModel(
    private val screenshotRepository: ScreenshotRepository
) : ViewModel() {
    
    // 今日截图列表
    private val _todayScreenshots = MutableStateFlow<UiState<List<ScreenshotInfo>>>(UiState.Loading)
    val todayScreenshots: StateFlow<UiState<List<ScreenshotInfo>>> = _todayScreenshots
    
    // 当前选中的截图
    private val _selectedScreenshot = MutableStateFlow<ScreenshotInfo?>(null)
    val selectedScreenshot: StateFlow<ScreenshotInfo?> = _selectedScreenshot
    
    // 初始化时加载今日截图
    init {
        loadTodayScreenshots()
    }
    
    /**
     * 加载今日截图
     */
    fun loadTodayScreenshots() {
        viewModelScope.launch {
            try {
                _todayScreenshots.value = UiState.Loading
                
                // 获取今日日期字符串
                val today = LocalDate.now()
                val todayString = today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                
                // 加载今日截图
                val screenshots = screenshotRepository.getScreenshotsForDay(todayString)
                
                // 转换为UI模型
                val screenshotInfos = screenshots.map { screenshot ->
                    ScreenshotInfo(
                        id = screenshot.id,
                        timestamp = screenshot.timestamp,
                        date = Date(screenshot.timestamp),
                        filePath = screenshot.filePath,
                        appPackageName = screenshot.appPackage,
                        appName = null
                    )
                }
                
                // 更新状态
                if (screenshotInfos.isEmpty()) {
                    _todayScreenshots.value = UiState.Empty
                } else {
                    _todayScreenshots.value = UiState.Success(screenshotInfos)
                    
                    // 默认选择第一张截图
                    _selectedScreenshot.value = screenshotInfos.firstOrNull()
                }
            } catch (e: Exception) {
                Timber.e(e, "加载今日截图失败")
                _todayScreenshots.value = UiState.Error(e.message ?: "未知错误")
            }
        }
    }
    
    /**
     * 选择截图
     */
    fun selectScreenshot(screenshot: ScreenshotInfo) {
        _selectedScreenshot.value = screenshot
    }
    
    /**
     * 删除截图
     */
    fun deleteScreenshot(screenshot: ScreenshotInfo) {
        viewModelScope.launch {
            try {
                // 删除截图
                screenshotRepository.deleteScreenshotById(screenshot.id)
                
                // 重新加载今日截图
                loadTodayScreenshots()
                
                // 如果删除的是当前选中的截图，清除选中状态
                if (_selectedScreenshot.value?.id == screenshot.id) {
                    _selectedScreenshot.value = null
                }
            } catch (e: Exception) {
                Timber.e(e, "删除截图失败")
            }
        }
    }
} 