package com.shuyi.discipline.ui.screen.collage

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.shuyi.discipline.data.model.CollageReport
import com.shuyi.discipline.data.model.CollageSettings
import com.shuyi.discipline.data.repository.CollageReportRepository
import com.shuyi.discipline.data.repository.CollageSettingsRepository
import com.shuyi.discipline.data.repository.ScreenshotRepository
import com.shuyi.discipline.data.repository.impl.CollageReportRepositoryImpl
import com.shuyi.discipline.data.repository.impl.CollageSettingsRepositoryImpl
import com.shuyi.discipline.data.repository.impl.ScreenshotRepositoryImpl
import com.shuyi.discipline.data.source.database.AppDatabase
import com.shuyi.discipline.domain.usecase.ScheduleDailyCollageUseCase
import com.shuyi.discipline.ui.model.UiState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import timber.log.Timber
import java.text.SimpleDateFormat
import java.io.File
import java.time.Instant
import java.time.LocalDate
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.Date
import java.util.Locale

/**
 * 拼图列表数据类
 */
data class CollageInfo(
    val id: String,
    val date: String,
    val dateFormatted: String,
    val screenshotCount: Int,
    val collagePath: String,
    val createTime: Long = System.currentTimeMillis()
)

/**
 * 日期选择器项
 */
data class DateItem(
    val date: Date,
    val dayOfWeek: String,
    val day: String,
    val isToday: Boolean,
    val isTomorrow: Boolean
)

/**
 * 拼图列表UI状态
 */
data class CollageListUiState(
    val isLoading: Boolean = true,
    val error: String? = null,
    val dateItems: List<DateItem> = emptyList(),
    val selectedDateIndex: Int = 0,
    val selectedDate: String = "",
    val formattedDate: String = "",
    val totalCollages: Int = 0,
    val allCollages: List<CollageInfo> = emptyList(),
    val morningCollages: List<CollageInfo> = emptyList(),
    val afternoonCollages: List<CollageInfo> = emptyList(),
    val eveningCollages: List<CollageInfo> = emptyList()
)

/**
 * 拼图列表ViewModel
 */
class CollageListViewModel(
    private val application: Application,
    private val collageReportRepository: CollageReportRepository,
    private val screenshotRepository: ScreenshotRepository,
    private val collageSettingsRepository: CollageSettingsRepository,
    private val scheduleDailyCollageUseCase: ScheduleDailyCollageUseCase
) : AndroidViewModel(application) {

    // UI状态
    private val _uiState = MutableStateFlow(CollageListUiState())
    val uiState: StateFlow<CollageListUiState> = _uiState

    // 日期格式化
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    private val dayOfWeekFormat = SimpleDateFormat("E", Locale.getDefault())
    private val dayFormat = SimpleDateFormat("d", Locale.getDefault())
    private val displayDateFormat = SimpleDateFormat("yyyy年MM月dd日", Locale.getDefault())

    // 旧的拼图列表状态，保留以兼容现有代码
    private val _collages = MutableStateFlow<UiState<List<CollageInfo>>>(UiState.Loading)
    val collages: StateFlow<UiState<List<CollageInfo>>> = _collages

    init {
        loadDateSelector()
        loadCollages(dateFormat.format(Date()))
    }

    /**
     * 加载日期选择器
     */
    private fun loadDateSelector() {
        val dateItems = mutableListOf<DateItem>()
        val calendar = java.util.Calendar.getInstance()
        val today = calendar.time
        val todayString = dateFormat.format(today)

        // 添加过去7天和未来2天的日期
        calendar.add(java.util.Calendar.DAY_OF_MONTH, -7)
        for (i in 0 until 10) {
            val date = calendar.time
            val isToday = dateFormat.format(date) == todayString
            val isTomorrow = i == 8 // 今天+1天

            dateItems.add(
                DateItem(
                    date = date,
                    dayOfWeek = dayOfWeekFormat.format(date),
                    day = dayFormat.format(date),
                    isToday = isToday,
                    isTomorrow = isTomorrow
                )
            )
            calendar.add(java.util.Calendar.DAY_OF_MONTH, 1)
        }

        // 找到今天的索引
        val todayIndex = dateItems.indexOfFirst { it.isToday }
        val selectedIndex = if (todayIndex >= 0) todayIndex else 0

        _uiState.value = _uiState.value.copy(
            dateItems = dateItems,
            selectedDateIndex = selectedIndex
        )

        // 加载选中日期的拼图
        if (dateItems.isNotEmpty()) {
            val selectedDate = dateItems[selectedIndex].date
            loadCollages(dateFormat.format(selectedDate))
        }
    }

    /**
     * 加载指定日期的拼图
     */
    fun loadCollages(dateString: String) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                Timber.d("开始加载拼图列表，日期: $dateString")

                val collageList = mutableListOf<CollageInfo>()

                // 从数据库获取所有拼图报告（使用first()而不是collect避免持续监听）
                val collageReports = collageReportRepository.getAllCollageReports().first()
                Timber.d("获取到拼图报告数量: ${collageReports.size}")

                // 转换为UI模型
                collageReports.forEach { collageReport ->
                    val collageDate = dateFormat.format(collageReport.date)
                    if (collageDate == dateString) {
                        // 检查拼图文件是否存在
                        val collageFile = File(collageReport.collagePath)
                        if (collageFile.exists()) {
                            Timber.d("拼图文件存在: ${collageReport.collagePath}")
                            collageList.add(
                                CollageInfo(
                                    id = collageReport.id,
                                    date = collageDate,
                                    dateFormatted = displayDateFormat.format(collageReport.date),
                                    screenshotCount = collageReport.screenshotIds.size,
                                    collagePath = collageReport.collagePath,
                                    createTime = collageReport.createTime
                                )
                            )
                        } else {
                            Timber.e("拼图文件不存在: ${collageReport.collagePath}")
                        }
                    }
                }

                // 按时间段分组（保留原有逻辑，但主要使用allCollages）
                val morningCollages = collageList.filter { isInTimeRange(it.createTime, 0, 12) }
                val afternoonCollages = collageList.filter { isInTimeRange(it.createTime, 12, 18) }
                val eveningCollages = collageList.filter { isInTimeRange(it.createTime, 18, 24) }

                // 按创建时间排序，最早的拼图显示在前面
                val sortedCollages = collageList.sortedBy { it.createTime }

                // 更新UI状态
                _uiState.value = _uiState.value.copy(
                    selectedDate = dateString,
                    formattedDate = formatDateString(dateString),
                    totalCollages = collageList.size,
                    allCollages = sortedCollages,
                    morningCollages = morningCollages,
                    afternoonCollages = afternoonCollages,
                    eveningCollages = eveningCollages,
                    isLoading = false
                )

                // 更新旧的状态，以兼容现有代码
                if (collageList.isEmpty()) {
                    _collages.value = UiState.Empty
                } else {
                    _collages.value = UiState.Success(collageList)
                }
            } catch (e: Exception) {
                Timber.e(e, "加载拼图列表失败")
                _uiState.value = _uiState.value.copy(
                    error = "加载拼图失败: ${e.message}",
                    isLoading = false
                )
                _collages.value = UiState.Error(e.message ?: "未知错误")
            }
        }
    }

    /**
     * 选择日期
     */
    fun selectDate(index: Int) {
        if (index in _uiState.value.dateItems.indices) {
            val dateItem = _uiState.value.dateItems[index]
            val dateString = dateFormat.format(dateItem.date)

            _uiState.value = _uiState.value.copy(
                selectedDateIndex = index,
                isLoading = true
            )

            loadCollages(dateString)
        }
    }

    /**
     * 根据日期对象选择日期
     */
    fun selectDateByDate(selectedDate: Date) {
        val dateString = dateFormat.format(selectedDate)

        // 查找对应的日期项索引
        val dateIndex = _uiState.value.dateItems.indexOfFirst { dateItem ->
            dateFormat.format(dateItem.date) == dateString
        }

        // 如果找到了对应的日期项，更新选中索引
        if (dateIndex >= 0) {
            _uiState.value = _uiState.value.copy(
                selectedDateIndex = dateIndex,
                isLoading = true
            )
        } else {
            // 如果没有找到，说明选择的日期不在当前的日期列表中
            // 我们需要重新生成日期列表，以选中的日期为中心
            loadDateSelectorWithSelectedDate(selectedDate)
        }

        loadCollages(dateString)
    }

    /**
     * 以指定日期为中心重新加载日期选择器
     */
    private fun loadDateSelectorWithSelectedDate(selectedDate: Date) {
        val calendar = java.util.Calendar.getInstance()
        val dateItems = mutableListOf<DateItem>()

        // 生成以选中日期为中心的前7天到后2天
        calendar.time = selectedDate
        calendar.add(java.util.Calendar.DAY_OF_YEAR, -7)

        for (i in 0 until 10) {
            val date = calendar.time
            val isToday = isSameDay(date, Date())

            dateItems.add(
                DateItem(
                    date = date,
                    dayOfWeek = if (isToday) "今天" else dayOfWeekFormat.format(date),
                    day = dayFormat.format(date),
                    isToday = isToday,
                    isTomorrow = false
                )
            )
            calendar.add(java.util.Calendar.DAY_OF_YEAR, 1)
        }

        // 找到选中日期的索引
        val selectedIndex = dateItems.indexOfFirst {
            isSameDay(it.date, selectedDate)
        }.takeIf { it >= 0 } ?: 7 // 默认选中中间位置

        _uiState.value = _uiState.value.copy(
            dateItems = dateItems,
            selectedDateIndex = selectedIndex
        )
    }

    /**
     * 判断两个日期是否是同一天
     */
    private fun isSameDay(date1: Date, date2: Date): Boolean {
        val cal1 = java.util.Calendar.getInstance().apply { time = date1 }
        val cal2 = java.util.Calendar.getInstance().apply { time = date2 }
        return cal1.get(java.util.Calendar.YEAR) == cal2.get(java.util.Calendar.YEAR) &&
               cal1.get(java.util.Calendar.DAY_OF_YEAR) == cal2.get(java.util.Calendar.DAY_OF_YEAR)
    }

    /**
     * 生成拼图
     */
    fun generateCollage() {
        viewModelScope.launch {
            try {
                // 更新UI状态，显示加载中
                _uiState.value = _uiState.value.copy(isLoading = true)

                Timber.d("开始生成拼图")

                // 调用仓库生成拼图
                collageReportRepository.generateDailyCollage()
                    .onSuccess { collageReport ->
                        Timber.d("拼图生成成功: ${collageReport.id}, 路径: ${collageReport.collagePath}")

                        // 更新上次拼图时间
                        try {
                            val settings = collageSettingsRepository.getCollageSettingsSync()
                            collageSettingsRepository.updateLastCollageTime(System.currentTimeMillis())

                            // 如果启用了自动拼图，重新调度拼图任务
                            if (settings.isAutoCollageEnabled) {
                                scheduleDailyCollageUseCase.schedule()
                            }
                        } catch (e: Exception) {
                            Timber.e(e, "更新拼图时间失败")
                        }

                        // 重新加载拼图列表
                        loadCollages(dateFormat.format(Date()))

                        // 显示成功提示
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = null
                        )
                    }
                    .onFailure { error ->
                        Timber.e(error, "拼图生成失败: ${error.message}")

                        // 更新UI状态，显示错误信息
                        _uiState.value = _uiState.value.copy(
                            isLoading = false,
                            error = "拼图生成失败: ${error.message ?: "未知错误"}"
                        )
                    }
            } catch (e: Exception) {
                Timber.e(e, "生成拼图过程出错: ${e.message}")

                // 更新UI状态，显示错误信息
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "生成拼图过程出错: ${e.message ?: "未知错误"}"
                )
            }
        }
    }

    /**
     * 删除当日拼图
     */
    fun deleteCurrentDateCollages() {
        viewModelScope.launch {
            try {
                // 更新UI状态，显示加载中
                _uiState.value = _uiState.value.copy(isLoading = true)

                val selectedDate = _uiState.value.selectedDate
                val currentCollageCount = _uiState.value.totalCollages
                Timber.d("开始删除日期 $selectedDate 的拼图，当前拼图数量: $currentCollageCount")

                // 调用仓库删除拼图
                val deletedCount = collageReportRepository.deleteCollageReportsForDate(selectedDate)
                Timber.d("删除操作完成，删除数量: $deletedCount")

                if (deletedCount > 0) {
                    Timber.d("成功删除 $deletedCount 条拼图记录")

                    // 重新加载拼图列表
                    loadCollages(selectedDate)

                    // 显示成功提示
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = null
                    )
                } else {
                    Timber.w("没有找到需要删除的拼图记录，可能的原因：")
                    Timber.w("1. 该日期没有拼图记录")
                    Timber.w("2. 日期格式不匹配")
                    Timber.w("3. 数据库查询条件有问题")

                    // 重新加载拼图列表以确保UI状态正确
                    loadCollages(selectedDate)

                    // 更新UI状态
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = null
                    )
                }
            } catch (e: Exception) {
                Timber.e(e, "删除拼图过程出错: ${e.message}")

                // 更新UI状态，显示错误信息
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = "删除拼图失败: ${e.message ?: "未知错误"}"
                )
            }
        }
    }

    /**
     * 判断时间戳是否在指定时间范围内
     */
    private fun isInTimeRange(timestamp: Long, startHour: Int, endHour: Int): Boolean {
        val calendar = java.util.Calendar.getInstance()
        calendar.timeInMillis = timestamp
        val hour = calendar.get(java.util.Calendar.HOUR_OF_DAY)
        return hour in startHour until endHour
    }

    /**
     * 格式化日期字符串
     */
    private fun formatDateString(dateString: String): String {
        return try {
            val date = dateFormat.parse(dateString)
            displayDateFormat.format(date ?: Date())
        } catch (e: Exception) {
            Timber.e(e, "格式化日期失败")
            dateString
        }
    }
}

/**
 * 拼图ViewModel工厂
 */
class CollageViewModelFactory(private val application: Application) : ViewModelProvider.Factory {
    @Suppress("UNCHECKED_CAST")
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(CollageListViewModel::class.java)) {
            val database = AppDatabase.getInstance(application)
            val screenshotRepository = ScreenshotRepositoryImpl(application, database.screenshotDao())
            val collageReportRepository = CollageReportRepositoryImpl(
                application,
                database.collageReportDao(),
                screenshotRepository
            )
            val collageSettingsRepository = CollageSettingsRepositoryImpl(database.collageSettingsDao())
            val scheduleDailyCollageUseCase = ScheduleDailyCollageUseCase(application)

            return CollageListViewModel(
                application,
                collageReportRepository,
                screenshotRepository,
                collageSettingsRepository,
                scheduleDailyCollageUseCase
            ) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}
