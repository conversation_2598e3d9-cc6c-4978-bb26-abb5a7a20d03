package com.shuyi.discipline.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 主题设置数据模型
 */
@Entity(tableName = "theme_settings")
data class ThemeSettings(
    @PrimaryKey val id: Int = 1,
    val themeMode: ThemeMode = ThemeMode.FOLLOW_SYSTEM,
    val isDynamicColorEnabled: Boolean = true, // 默认开启动态颜色
    val fontSize: FontSize = FontSize.STANDARD,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

/**
 * 主题模式枚举
 */
enum class ThemeMode(val displayName: String, val value: Int) {
    LIGHT("浅色模式", 0),
    DARK("深色模式", 1),
    FOLLOW_SYSTEM("跟随系统", 2);

    companion object {
        /**
         * 根据显示名称获取主题模式
         */
        fun fromDisplayName(name: String): ThemeMode {
            return values().find { it.displayName == name } ?: FOLLOW_SYSTEM
        }

        /**
         * 根据值获取主题模式
         */
        fun fromValue(value: Int): ThemeMode {
            return values().find { it.value == value } ?: FOLLOW_SYSTEM
        }
    }
}

/**
 * 字体大小枚举
 */
enum class FontSize(val displayName: String, val value: Int, val scaleFactor: Float) {
    SMALL("小", 0, 0.85f),
    STANDARD("标准", 1, 1.0f),
    LARGE("大", 2, 1.15f),
    EXTRA_LARGE("特大", 3, 1.3f);

    companion object {
        /**
         * 根据显示名称获取字体大小
         */
        fun fromDisplayName(name: String): FontSize {
            return values().find { it.displayName == name } ?: STANDARD
        }

        /**
         * 根据值获取字体大小
         */
        fun fromValue(value: Int): FontSize {
            return values().find { it.value == value } ?: STANDARD
        }
    }
}
