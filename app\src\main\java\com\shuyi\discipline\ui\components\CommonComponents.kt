package com.shuyi.discipline.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Analytics
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.Dashboard
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Image
import androidx.compose.material.icons.filled.Schedule
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * 状态栏空间
 * 提供最小的空间，不再使用statusBarsPadding
 */
@Composable
fun StatusBar() {
    // 完全移除状态栏空间，不再添加额外的padding
}

/**
 * 页面标题栏
 */
@Composable
fun PageTitleBar(
    title: String,
    showBackButton: Boolean = false,
    onBackClick: () -> Unit = {},
    actions: @Composable () -> Unit = {}
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 16.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            if (showBackButton) {
                IconButton(
                    onClick = onBackClick,
                    modifier = Modifier.size(24.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.ArrowBack,
                        contentDescription = "返回",
                        tint = MaterialTheme.colorScheme.onSurface
                    )
                }
                Spacer(modifier = Modifier.width(8.dp))
            }

            Text(
                text = title,
                style = MaterialTheme.typography.headlineMedium
            )
        }

        Box {
            actions()
        }
    }
}

/**
 * 设置卡片
 */
@Composable
fun SettingsCard(
    title: String,
    content: @Composable () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(bottom = 16.dp),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleLarge,
                color = MaterialTheme.colorScheme.onSurface,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            content()
        }
    }
}

/**
 * 设置开关项
 */
@Composable
fun SettingsSwitchItem(
    title: String,
    description: String? = null,
    checked: Boolean,
    onCheckedChange: (Boolean) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurface
            )
            if (description != null) {
                Text(
                    text = description,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSecondaryContainer
                )
            }
        }

        Switch(
            checked = checked,
            onCheckedChange = onCheckedChange
        )
    }
}

/**
 * 设置导航项
 */
@Composable
fun SettingsNavigationItem(
    title: String,
    description: String? = null,
    icon: androidx.compose.ui.graphics.vector.ImageVector? = null,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(vertical = 12.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Row(
            modifier = Modifier.weight(1f),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // 左侧图标
            if (icon != null) {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary,
                    modifier = Modifier
                        .size(24.dp)
                        .padding(end = 16.dp)
                )
            }

            // 文字内容
            Column {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                if (description != null) {
                    Text(
                        text = description,
                        style = MaterialTheme.typography.bodyMedium,
                        color = MaterialTheme.colorScheme.onSecondaryContainer
                    )
                }
            }
        }

        // 右侧箭头
        Icon(
            imageVector = Icons.Default.ChevronRight,
            contentDescription = "进入",
            tint = MaterialTheme.colorScheme.onSurfaceVariant
        )
    }
}

/**
 * 底部导航栏
 *
 * 标签索引定义：
 * 0 - 首页
 * 1 - 截图
 * 2 - 拼图
 * 3 - 状态
 * 4 - 设置
 */
@Composable
fun BottomNavBar(
    selectedIndex: Int,
    onItemSelected: (Int) -> Unit,
    onNavigateToHome: () -> Unit = {},
    onNavigateToScreenshots: () -> Unit = {},
    onNavigateToCollages: () -> Unit = {},
    onNavigateToStatus: () -> Unit = {},
    onNavigateToConfig: () -> Unit = {}
) {
    // 记录当前选中的索引，避免重复导航
    val currentSelectedIndex = remember { mutableStateOf(selectedIndex) }

    NavigationBar(
        containerColor = MaterialTheme.colorScheme.surfaceContainer,
        contentColor = MaterialTheme.colorScheme.onSurfaceVariant,
        tonalElevation = 0.dp
    ) {
        // 首页
        NavigationBarItem(
            selected = selectedIndex == 0,
            onClick = {
                // 只有当不在首页时才导航
                if (currentSelectedIndex.value != 0) {
                    onNavigateToHome()
                    currentSelectedIndex.value = 0
                }
                onItemSelected(0)
            },
            icon = { Icon(Icons.Filled.Home, contentDescription = "首页") },
            label = { Text("首页", style = MaterialTheme.typography.labelSmall) }
        )

        // 截图
        NavigationBarItem(
            selected = selectedIndex == 1,
            onClick = {
                // 只有当不在截图页时才导航
                if (currentSelectedIndex.value != 1) {
                    onNavigateToScreenshots()
                    currentSelectedIndex.value = 1
                }
                onItemSelected(1)
            },
            icon = { Icon(Icons.Filled.Image, contentDescription = "截图") },
            label = { Text("截图", style = MaterialTheme.typography.labelSmall) }
        )

        // 拼图
        NavigationBarItem(
            selected = selectedIndex == 2,
            onClick = {
                // 只有当不在拼图页时才导航
                if (currentSelectedIndex.value != 2) {
                    onNavigateToCollages()
                    currentSelectedIndex.value = 2
                }
                onItemSelected(2)
            },
            icon = { Icon(Icons.Filled.Dashboard, contentDescription = "拼图") },
            label = { Text("拼图", style = MaterialTheme.typography.labelSmall) }
        )

        // 状态
        NavigationBarItem(
            selected = selectedIndex == 3,
            onClick = {
                // 只有当不在状态页时才导航
                if (currentSelectedIndex.value != 3) {
                    onNavigateToStatus()
                    currentSelectedIndex.value = 3
                }
                onItemSelected(3)
            },
            icon = { Icon(Icons.Filled.Analytics, contentDescription = "状态") },
            label = { Text("状态", style = MaterialTheme.typography.labelSmall) }
        )

        // 设置
        NavigationBarItem(
            selected = selectedIndex == 4,
            onClick = {
                // 只有当不在设置页时才导航
                if (currentSelectedIndex.value != 4) {
                    onNavigateToConfig()
                    currentSelectedIndex.value = 4
                }
                onItemSelected(4)
            },
            icon = { Icon(Icons.Filled.Settings, contentDescription = "设置") },
            label = { Text("设置", style = MaterialTheme.typography.labelSmall) }
        )
    }
}
