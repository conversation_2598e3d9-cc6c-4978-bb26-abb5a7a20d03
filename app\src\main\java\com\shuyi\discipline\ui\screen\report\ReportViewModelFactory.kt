package com.shuyi.discipline.ui.screen.report

import android.app.Application
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.shuyi.discipline.data.repository.impl.CollageReportRepositoryImpl
import com.shuyi.discipline.data.repository.impl.ScreenshotRepositoryImpl
import com.shuyi.discipline.data.source.database.AppDatabase

/**
 * 报告相关ViewModel工厂
 */
class ReportViewModelFactory(private val application: Application) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        val database = AppDatabase.getInstance(application)
        
        if (modelClass.isAssignableFrom(ReportListViewModel::class.java)) {
            val repository = ScreenshotRepositoryImpl(application, database.screenshotDao())
            @Suppress("UNCHECKED_CAST")
            return ReportListViewModel(repository) as T
        }
        
        if (modelClass.isAssignableFrom(ReportDetailViewModel::class.java)) {
            val screenshotRepository = ScreenshotRepositoryImpl(application, database.screenshotDao())
            val collageRepository = CollageReportRepositoryImpl(application, database.collageReportDao(), screenshotRepository)
            @Suppress("UNCHECKED_CAST")
            return ReportDetailViewModel(application, screenshotRepository, collageRepository) as T
        }
        
        throw IllegalArgumentException("Unknown ViewModel class: ${modelClass.name}")
    }
} 