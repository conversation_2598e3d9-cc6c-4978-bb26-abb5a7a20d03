package com.shuyi.discipline.utils

import java.io.File

/**
 * 文件工具类
 */
object FileUtils {
    
    /**
     * 格式化文件大小（以KB为单位）
     * @param filePath 文件路径
     * @return 格式化后的文件大小字符串
     */
    fun formatFileSize(filePath: String): String {
        return try {
            val file = File(filePath)
            if (file.exists()) {
                val sizeInBytes = file.length()
                val sizeInKB = sizeInBytes / 1024.0
                String.format("%.1f KB", sizeInKB)
            } else {
                "0 KB"
            }
        } catch (e: Exception) {
            "0 KB"
        }
    }
    
    /**
     * 获取文件大小（字节）
     * @param filePath 文件路径
     * @return 文件大小（字节）
     */
    fun getFileSize(filePath: String): Long {
        return try {
            val file = File(filePath)
            if (file.exists()) {
                file.length()
            } else {
                0L
            }
        } catch (e: Exception) {
            0L
        }
    }
}
