package com.shuyi.discipline.di

import android.content.Context
import com.shuyi.discipline.data.repository.QuietPeriodRepository
import com.shuyi.discipline.data.repository.ScheduleRepository
import com.shuyi.discipline.data.repository.ScreenshotDeduplicationRepository
import com.shuyi.discipline.data.repository.ScreenshotRepository
import com.shuyi.discipline.domain.usecase.ScheduleScreenshotUseCase
import com.shuyi.discipline.domain.usecase.ScheduleSystemMonitorUseCase
import com.shuyi.discipline.domain.usecase.ScreenshotDeduplicationUseCase
import com.shuyi.discipline.domain.usecase.TakeScreenshotUseCase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object UseCaseModule {

    @Provides
    @Singleton
    fun provideTakeScreenshotUseCase(
        @ApplicationContext context: Context,
        screenshotRepository: ScreenshotRepository,
        scheduleRepository: ScheduleRepository,
        quietPeriodRepository: QuietPeriodRepository
    ): TakeScreenshotUseCase {
        return TakeScreenshotUseCase(
            context,
            screenshotRepository,
            scheduleRepository,
            quietPeriodRepository
        )
    }

    @Provides
    @Singleton
    fun provideScheduleScreenshotUseCase(
        @ApplicationContext context: Context,
        scheduleRepository: ScheduleRepository
    ): ScheduleScreenshotUseCase {
        return ScheduleScreenshotUseCase(
            context,
            scheduleRepository
        )
    }

    @Provides
    @Singleton
    fun provideScheduleSystemMonitorUseCase(
        @ApplicationContext context: Context
    ): ScheduleSystemMonitorUseCase {
        return ScheduleSystemMonitorUseCase(context)
    }

    @Provides
    @Singleton
    fun provideScreenshotDeduplicationUseCase(
        screenshotDeduplicationRepository: ScreenshotDeduplicationRepository,
        screenshotRepository: ScreenshotRepository
    ): ScreenshotDeduplicationUseCase {
        return ScreenshotDeduplicationUseCase(screenshotDeduplicationRepository, screenshotRepository)
    }
}