package com.shuyi.discipline.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey
import com.shuyi.discipline.utils.SensitivityLevel

/**
 * 截图去重设置数据模型
 */
@Entity(tableName = "screenshot_deduplication")
data class ScreenshotDeduplication(
    @PrimaryKey val id: Int = 1, // 只有一个去重设置，所以ID固定为1
    val lastScreenshotHash: Long = 0L, // 上一张截图的哈希值
    val lastScreenshotPath: String = "", // 上一张截图的路径
    val isEnabled: Boolean = true, // 是否启用去重功能
    val sensitivityLevel: Int = SensitivityLevel.MEDIUM.threshold, // 敏感度级别
    val duplicateCount: Int = 0, // 检测到的重复图片数量
    val spaceSavedMb: Double = 0.0, // 节省的存储空间（MB）
    val lastUpdatedTime: Long = System.currentTimeMillis(), // 最后更新时间
    val totalProcessedCount: Int = 0 // 总处理的截图数量
)

/**
 * 去重统计信息
 */
data class DeduplicationStats(
    val totalProcessed: Int = 0,
    val duplicatesFound: Int = 0,
    val spaceSavedMb: Double = 0.0,
    val deduplicationRate: Double = 0.0 // 去重率
) {
    companion object {
        fun fromDeduplication(deduplication: ScreenshotDeduplication): DeduplicationStats {
            val rate = if (deduplication.totalProcessedCount > 0) {
                (deduplication.duplicateCount.toDouble() / deduplication.totalProcessedCount) * 100
            } else {
                0.0
            }
            
            return DeduplicationStats(
                totalProcessed = deduplication.totalProcessedCount,
                duplicatesFound = deduplication.duplicateCount,
                spaceSavedMb = deduplication.spaceSavedMb,
                deduplicationRate = rate
            )
        }
    }
}

/**
 * 去重结果
 */
data class DeduplicationResult(
    val isDuplicate: Boolean,
    val similarityPercentage: Int,
    val hammingDistance: Int,
    val shouldDelete: Boolean,
    val reason: String
) {
    companion object {
        fun duplicate(similarityPercentage: Int, hammingDistance: Int): DeduplicationResult {
            return DeduplicationResult(
                isDuplicate = true,
                similarityPercentage = similarityPercentage,
                hammingDistance = hammingDistance,
                shouldDelete = true,
                reason = "图片相似度${similarityPercentage}%，超过阈值"
            )
        }
        
        fun unique(similarityPercentage: Int, hammingDistance: Int): DeduplicationResult {
            return DeduplicationResult(
                isDuplicate = false,
                similarityPercentage = similarityPercentage,
                hammingDistance = hammingDistance,
                shouldDelete = false,
                reason = "图片相似度${similarityPercentage}%，低于阈值"
            )
        }
        
        fun firstScreenshot(): DeduplicationResult {
            return DeduplicationResult(
                isDuplicate = false,
                similarityPercentage = 0,
                hammingDistance = 64,
                shouldDelete = false,
                reason = "首张截图，无需对比"
            )
        }
        
        fun error(errorMessage: String): DeduplicationResult {
            return DeduplicationResult(
                isDuplicate = false,
                similarityPercentage = 0,
                hammingDistance = 64,
                shouldDelete = false,
                reason = "处理失败：$errorMessage"
            )
        }
    }
}
