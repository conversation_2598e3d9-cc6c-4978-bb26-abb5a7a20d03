package com.shuyi.discipline.ui.screen.quietperiod

import android.app.Application
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Image
import androidx.compose.material.icons.filled.Dashboard
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.RadioButton
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TimeInput
import androidx.compose.material3.TimePicker
import androidx.compose.material3.TimePickerDefaults
import androidx.compose.material3.TimePickerState
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.rememberTimePickerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.viewmodel.compose.viewModel
import com.shuyi.discipline.R
import com.shuyi.discipline.data.model.QuietPeriod
import com.shuyi.discipline.ui.components.BottomNavBar
import com.shuyi.discipline.ui.components.PageTitleBar
import com.shuyi.discipline.ui.components.StatusBar
import com.shuyi.discipline.ui.components.TimePickerDialog

/**
 * 免打扰时段设置屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun QuietPeriodScreen(
    onNavigateBack: () -> Unit,
    viewModel: QuietPeriodViewModel = viewModel(
        factory = QuietPeriodViewModelFactory(LocalContext.current.applicationContext as Application)
    )
) {
    val quietPeriods by viewModel.quietPeriods.collectAsState(initial = emptyList())
    var showAddDialog by remember { mutableStateOf(false) }
    var showStartTimePicker by remember { mutableStateOf(false) }
    var showEndTimePicker by remember { mutableStateOf(false) }
    val scrollState = rememberScrollState()
    var selectedTabIndex by remember { mutableStateOf(0) } // 首页选项卡

    var newStartHour by remember { mutableStateOf(22) }
    var newStartMinute by remember { mutableStateOf(0) }
    var newEndHour by remember { mutableStateOf(6) }
    var newEndMinute by remember { mutableStateOf(0) }
    var selectedPatternId by remember { mutableStateOf(QuietPeriodPatterns.PATTERN_DAILY) }

    // 定义颜色
    val lightGray = Color(0xFFF8F8F8)
    val primaryBlue = Color(0xFF007AFF)

    Scaffold(
        containerColor = lightGray,
        floatingActionButton = {
            FloatingActionButton(
                onClick = { showAddDialog = true },
                containerColor = primaryBlue
            ) {
                Icon(
                    Icons.Default.Add,
                    contentDescription = "添加免打扰时段",
                    tint = MaterialTheme.colorScheme.onPrimary
                )
            }
        },
        bottomBar = {
            BottomNavBar(
                selectedIndex = selectedTabIndex,
                onItemSelected = { index -> selectedTabIndex = index },
                onNavigateToHome = onNavigateBack,
                onNavigateToScreenshots = { /* 暂时不处理 */ },
                onNavigateToCollages = { /* 暂时不处理 */ },
                onNavigateToConfig = { /* 暂时不处理 */ }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(lightGray)
        ) {
            // 标题 - 直接从顶部开始，不再使用StatusBar
            PageTitleBar(
                title = "免打扰时段",
                showBackButton = true,
                onBackClick = onNavigateBack
            )

            // 内容区域
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp)
            ) {
            if (quietPeriods.isEmpty()) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    Text(
                        text = "暂无免打扰时段",
                        style = MaterialTheme.typography.bodyLarge,
                        textAlign = TextAlign.Center
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = "点击右下角按钮添加免打扰时段",
                        style = MaterialTheme.typography.bodyMedium,
                        textAlign = TextAlign.Center
                    )
                }
            } else {
                LazyColumn(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp)
                ) {
                    items(quietPeriods) { period ->
                        Card(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(vertical = 8.dp),
                            shape = RoundedCornerShape(16.dp),
                            colors = CardDefaults.cardColors(containerColor = Color.White),
                            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(16.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Column(modifier = Modifier.weight(1f)) {
                                    Text(
                                        text = "${QuietPeriodPatterns.formatTimeString(period.startHour, period.startMinute)} - " +
                                                QuietPeriodPatterns.formatTimeString(period.endHour, period.endMinute),
                                        style = MaterialTheme.typography.titleMedium
                                    )
                                    Spacer(modifier = Modifier.height(4.dp))
                                    Text(
                                        text = QuietPeriodPatterns.getPatternDisplayText(period.repeatPattern),
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = MaterialTheme.colorScheme.onSecondaryContainer
                                    )
                                }

                                Box(
                                    modifier = Modifier
                                        .size(36.dp)
                                        .clip(CircleShape)
                                        .background(Color(0xFFFFEBEE))
                                        .clickable { viewModel.deleteQuietPeriod(period) },
                                    contentAlignment = Alignment.Center
                                ) {
                                    Icon(
                                        Icons.Default.Delete,
                                        contentDescription = "删除",
                                        tint = Color(0xFFC62828),
                                        modifier = Modifier.size(20.dp)
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // 添加免打扰时段对话框
    if (showAddDialog) {
        AlertDialog(
            onDismissRequest = { showAddDialog = false },
            title = { Text("添加免打扰时段") },
            text = {
                Column(
                    modifier = Modifier.padding(top = 8.dp)
                ) {
                    Text("在此时段内，应用将不会截取屏幕")

                    Spacer(modifier = Modifier.height(16.dp))

                    // 开始时间选择
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "开始时间:",
                            modifier = Modifier.weight(1f)
                        )
                        Button(
                            onClick = { showStartTimePicker = true }
                        ) {
                            Text(
                                QuietPeriodPatterns.formatTimeString(newStartHour, newStartMinute)
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(8.dp))

                    // 结束时间选择
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "结束时间:",
                            modifier = Modifier.weight(1f)
                        )
                        Button(
                            onClick = { showEndTimePicker = true }
                        ) {
                            Text(
                                QuietPeriodPatterns.formatTimeString(newEndHour, newEndMinute)
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // 重复模式选择
                    Text("重复模式:")
                    Spacer(modifier = Modifier.height(8.dp))

                    val patternOptions = QuietPeriodPatterns.getAllPatternOptions()
                    patternOptions.forEach { option ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable { selectedPatternId = option.id }
                                .padding(vertical = 8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            RadioButton(
                                selected = selectedPatternId == option.id,
                                onClick = { selectedPatternId = option.id }
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(option.label)
                        }
                    }
                }
            },
            confirmButton = {
                Button(
                    onClick = {
                        viewModel.addQuietPeriod(
                            startHour = newStartHour,
                            startMinute = newStartMinute,
                            endHour = newEndHour,
                            endMinute = newEndMinute,
                            patternId = selectedPatternId
                        )
                        showAddDialog = false
                    }
                ) {
                    Text("添加")
                }
            },
            dismissButton = {
                Button(
                    onClick = { showAddDialog = false }
                ) {
                    Text("取消")
                }
            }
        )
    }

    // 开始时间选择器
    if (showStartTimePicker) {
        TimePickerDialog(
            initialHour = newStartHour,
            initialMinute = newStartMinute,
            onDismiss = { showStartTimePicker = false },
            onConfirm = { hour, minute ->
                newStartHour = hour
                newStartMinute = minute
                showStartTimePicker = false
            }
        )
    }

    // 结束时间选择器
    if (showEndTimePicker) {
        TimePickerDialog(
            initialHour = newEndHour,
            initialMinute = newEndMinute,
            onDismiss = { showEndTimePicker = false },
            onConfirm = { hour, minute ->
                newEndHour = hour
                newEndMinute = minute
                showEndTimePicker = false
            }
        )
    }
}



@Preview(showBackground = true)
@Composable
fun QuietPeriodScreenPreview() {
    MaterialTheme {
        QuietPeriodScreen(
            onNavigateBack = {}
        )
    }
}
}