package com.shuyi.discipline.domain.service

import android.app.Activity
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.content.pm.ServiceInfo
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import com.shuyi.discipline.MainActivity
import com.shuyi.discipline.R
import com.shuyi.discipline.data.repository.QuietPeriodRepository
import com.shuyi.discipline.data.repository.ScheduleRepository
import com.shuyi.discipline.data.repository.ScreenshotRepository
import com.shuyi.discipline.domain.monitor.MediaProjectionPermissionMonitor
import com.shuyi.discipline.domain.usecase.ScheduleScreenshotUseCase
import com.shuyi.discipline.domain.usecase.ScreenshotDeduplicationUseCase
import com.shuyi.discipline.domain.usecase.TakeScreenshotUseCase
import com.shuyi.discipline.utils.MediaProjectionUtils
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * 截图服务
 */
@AndroidEntryPoint
class ScreenshotService : Service() {

    private val serviceJob = SupervisorJob()
    private val serviceScope = CoroutineScope(Dispatchers.Main + serviceJob)

    @Inject
    lateinit var screenshotRepository: ScreenshotRepository

    @Inject
    lateinit var scheduleRepository: ScheduleRepository

    @Inject
    lateinit var quietPeriodRepository: QuietPeriodRepository

    @Inject
    lateinit var takeScreenshotUseCase: TakeScreenshotUseCase

    @Inject
    lateinit var scheduleScreenshotUseCase: ScheduleScreenshotUseCase

    @Inject
    lateinit var screenshotDeduplicationUseCase: ScreenshotDeduplicationUseCase

    @Inject
    lateinit var mediaProjectionPermissionMonitor: MediaProjectionPermissionMonitor

    private var mediaProjection: MediaProjection? = null
    private var resultCode: Int = 0
    private var resultData: Intent? = null
    private var isForegroundStarted = false

    // 广播接收器，用于接收清理MediaProjection的指令
    private val clearMediaProjectionReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            when (intent.action) {
                "com.shuyi.discipline.CLEAR_MEDIA_PROJECTION" -> {
                    Timber.w("收到清理MediaProjection的广播，开始清理无效的MediaProjection对象")
                    clearInvalidMediaProjection()
                }
            }
        }
    }

    override fun onCreate() {
        super.onCreate()
        Timber.d("截图服务创建")

        // 注册清理MediaProjection的广播接收器
        val filter = IntentFilter("com.shuyi.discipline.CLEAR_MEDIA_PROJECTION")
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            registerReceiver(clearMediaProjectionReceiver, filter, Context.RECEIVER_NOT_EXPORTED)
        } else {
            registerReceiver(clearMediaProjectionReceiver, filter)
        }
        Timber.d("清理MediaProjection广播接收器已注册")

        // 不在onCreate中启动前台服务，等待具体的启动命令
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Timber.d("服务收到命令: ${intent?.action}")

        // 无论什么情况，都立即启动前台服务，避免 ForegroundServiceDidNotStartInTimeException
        if (!isForegroundStarted) {
            try {
                startForeground()
                isForegroundStarted = true
                Timber.d("前台服务已启动")
            } catch (e: Exception) {
                Timber.e(e, "启动前台服务失败")
                // 如果启动前台服务失败，停止服务避免崩溃
                stopSelf()
                return START_NOT_STICKY
            }
        }

        when (intent?.action) {
            ACTION_START_SERVICE -> {
                Timber.d("开始启动服务")

                // 标记服务为运行状态
                updateServiceRunningStatus(true)

                // 如果已经有媒体投影权限，直接保存，否则需要在MainActivity中申请
                intent.getParcelableExtra<Intent>("media_projection_data")?.let { data ->
                    val code = intent.getIntExtra("media_projection_result_code", 0)
                    Timber.d("从意图中获取媒体投影数据，结果码: $code")
                    saveMediaProjectionPermission(code, data)
                }

                // 发送服务状态广播，通知UI服务已启动
                val statusIntent = Intent(ACTION_SERVICE_STATUS).apply {
                    putExtra("is_running", true)
                    putExtra("has_permission", mediaProjection != null)
                }
                sendBroadcast(statusIntent)
                Timber.d("发送服务状态广播: 运行中，有权限=${mediaProjection != null}")

                // 异步执行耗时操作，避免阻塞主线程
                serviceScope.launch {
                    try {
                        // 更新服务状态为启用
                        scheduleRepository.updateServiceStatus(true)
                        Timber.d("服务状态已更新为启用")

                        // 安排下一次截图
                        scheduleScreenshotUseCase.schedule()
                    } catch (e: Exception) {
                        Timber.e(e, "启动服务时出错: ${e.message}")
                    }
                }
            }
            ACTION_STOP_SERVICE -> {
                Timber.d("停止服务")

                // 标记服务为停止状态
                updateServiceRunningStatus(false)

                serviceScope.launch {
                    try {
                        // 更新服务状态为禁用
                        scheduleRepository.updateServiceStatus(false)
                        Timber.d("服务状态已更新为禁用")

                        // 取消调度
                        scheduleScreenshotUseCase.cancel()
                    } catch (e: CancellationException) {
                        // 协程取消是正常的，不需要记录为错误
                        Timber.d("停止服务协程被取消: ${e.message}")
                    } catch (e: Exception) {
                        Timber.e(e, "停止服务时出错: ${e.message}")
                    }
                }
                stopSelf()
            }
            ACTION_TAKE_SCREENSHOT -> {
                Timber.d("收到截图命令")
                handleTakeScreenshot()
            }
            ACTION_UPDATE_SCHEDULE -> {
                Timber.d("更新截图调度")
                serviceScope.launch {
                    try {
                        scheduleScreenshotUseCase.cancel()
                        scheduleScreenshotUseCase.schedule()
                    } catch (e: Exception) {
                        Timber.e(e, "更新调度时出错: ${e.message}")
                    }
                }
            }
            ACTION_CHECK_STATUS -> {
                Timber.d("检查服务状态")
                // 发送广播通知服务正在运行
                val statusIntent = Intent(ACTION_SERVICE_STATUS).apply {
                    putExtra("is_running", true)
                    putExtra("has_permission", mediaProjection != null)
                }
                sendBroadcast(statusIntent)
                Timber.d("发送服务状态广播: 运行中")
            }
            null -> {
                Timber.d("服务启动，但没有指定动作")

                // 标记服务为运行状态
                updateServiceRunningStatus(true)

                // 检查是否应该重新调度截图
                serviceScope.launch {
                    try {
                        val rule = scheduleRepository.getScheduleRule()
                        if (rule?.isEnabled == true) {
                            Timber.d("服务被重启且截图功能已启用，重新调度截图")
                            // 重新调度截图
                            scheduleScreenshotUseCase.schedule()
                        } else {
                            Timber.d("截图功能未启用，停止服务")
                            stopSelf()
                        }
                    } catch (e: Exception) {
                        Timber.e(e, "服务重启时出错: ${e.message}")
                        stopSelf()
                    }
                }
            }
        }

        return START_STICKY
    }

    private fun startForeground() {
        try {
            // 创建通知渠道
            createNotificationChannel()

            // 创建点击通知的PendingIntent
            val pendingIntent = PendingIntent.getActivity(
                this,
                0,
                Intent(this, MainActivity::class.java),
                PendingIntent.FLAG_IMMUTABLE
            )

            // 创建通知
            val notification = NotificationCompat.Builder(this, CHANNEL_ID)
                .setContentTitle("屏幕监控")
                .setContentText("正在后台运行")
                .setSmallIcon(android.R.drawable.ic_menu_camera)
                .setPriority(NotificationCompat.PRIORITY_DEFAULT)
                .setContentIntent(pendingIntent)
                .setOngoing(true) // 设置为持续通知，不可滑动删除
                .build()

            // 启动前台服务
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                startForeground(
                    NOTIFICATION_ID,
                    notification,
                    ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PROJECTION
                )
            } else {
                startForeground(NOTIFICATION_ID, notification)
            }

            Timber.d("前台服务通知已创建并启动")
        } catch (e: Exception) {
            Timber.e(e, "启动前台服务失败: ${e.message}")
            // 如果启动前台服务失败，停止服务避免崩溃
            stopSelf()
        }
    }

    private fun createNotificationChannel() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                val channel = NotificationChannel(
                    CHANNEL_ID,
                    "屏幕监控服务",
                    NotificationManager.IMPORTANCE_LOW
                ).apply {
                    description = "显示屏幕监控服务的运行状态"
                    setShowBadge(false) // 不显示角标
                    enableLights(false) // 不启用指示灯
                    enableVibration(false) // 不启用震动
                }

                val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
                notificationManager.createNotificationChannel(channel)
                Timber.d("通知渠道已创建")
            }
        } catch (e: Exception) {
            Timber.e(e, "创建通知渠道失败: ${e.message}")
        }
    }

    private fun handleTakeScreenshot() {
        serviceScope.launch {
            try {
                Timber.d("开始处理截图请求")

                // 检查是否在免打扰时段
                if (quietPeriodRepository.isInQuietPeriod()) {
                    Timber.d("当前处于免打扰时段，跳过截图")
                    scheduleScreenshotUseCase.schedule()
                    return@launch
                }

                // 检查媒体投影权限
                if (mediaProjection == null) {
                    Timber.d("媒体投影对象为空，尝试使用结果码和数据创建")
                    if (resultCode == 0 || resultData == null) {
                        Timber.i("没有媒体投影权限，无法截图")
                        scheduleScreenshotUseCase.schedule()
                        return@launch
                    }
                } else {
                    Timber.d("媒体投影对象已存在")
                }

                // 尝试截图
                Timber.d("开始执行截图操作")
                takeScreenshotUseCase.execute(mediaProjection, resultCode, resultData)
                    .onSuccess { screenshot ->
                        Timber.d("截图成功: ${screenshot.filePath}")

                        // 异步处理去重检测
                        serviceScope.launch {
                            try {
                                val deduplicationResult = screenshotDeduplicationUseCase.processScreenshot(screenshot.filePath)
                                Timber.d("去重处理完成: ${deduplicationResult.reason}")

                                if (deduplicationResult.shouldDelete) {
                                    Timber.i("重复截图已删除: ${screenshot.filePath}, 相似度: ${deduplicationResult.similarityPercentage}%")
                                }
                            } catch (e: Exception) {
                                Timber.e(e, "去重处理失败: ${screenshot.filePath}")
                            }
                        }
                    }
                    .onFailure {
                        Timber.e(it, "截图失败: ${it.message}")
                    }

                // 安排下一次截图
                Timber.d("安排下一次截图")
                scheduleScreenshotUseCase.schedule()
            } catch (e: Exception) {
                Timber.e(e, "截图过程出错: ${e.message}")
                // 即使出错也要安排下一次截图
                scheduleScreenshotUseCase.schedule()
            }
        }
    }

    private fun saveMediaProjectionPermission(resultCode: Int, data: Intent) {
        try {
            Timber.d("开始保存媒体投影权限，结果码: $resultCode")

            if (resultCode != Activity.RESULT_OK) {
                Timber.e("媒体投影权限结果码不正确: $resultCode")
                return
            }

            this.resultCode = resultCode
            this.resultData = data

            // 获取MediaProjection对象
            val projectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            mediaProjection = projectionManager.getMediaProjection(resultCode, data)

            if (mediaProjection == null) {
                Timber.e("无法创建MediaProjection对象")
                return
            }

            Timber.d("保存媒体投影权限成功")

            // 启动权限监控
            mediaProjectionPermissionMonitor.startMonitoring(mediaProjection!!)
            Timber.d("MediaProjection权限监控已启动")

            // 发送广播通知UI更新状态
            val statusIntent = Intent(ACTION_SERVICE_STATUS).apply {
                putExtra("is_running", true)
                putExtra("has_permission", true) // 此时已经有权限
            }
            sendBroadcast(statusIntent)
            Timber.d("发送服务状态广播: 运行中且有权限")
        } catch (e: Exception) {
            Timber.e(e, "保存媒体投影权限失败: ${e.message}")
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        Timber.d("截图服务销毁")

        // 重置前台服务标志
        isForegroundStarted = false

        // 标记服务为停止状态
        updateServiceRunningStatus(false)

        // 发送服务停止广播
        val statusIntent = Intent(ACTION_SERVICE_STATUS).apply {
            putExtra("is_running", false)
            putExtra("has_permission", false)
        }
        sendBroadcast(statusIntent)
        Timber.d("发送服务停止广播")

        // 停止权限监控
        mediaProjectionPermissionMonitor.stopMonitoring()
        Timber.d("MediaProjection权限监控已停止")

        // 注销广播接收器
        try {
            unregisterReceiver(clearMediaProjectionReceiver)
            Timber.d("清理MediaProjection广播接收器已注销")
        } catch (e: Exception) {
            Timber.e(e, "注销清理MediaProjection广播接收器失败")
        }

        // 停止媒体投影
        mediaProjection?.stop()
        mediaProjection = null

        // 取消所有协程
        serviceScope.cancel()
    }

    /**
     * 清理无效的MediaProjection对象
     */
    private fun clearInvalidMediaProjection() {
        try {
            Timber.w("开始清理无效的MediaProjection对象")

            // 停止并清理MediaProjection
            mediaProjection?.stop()
            mediaProjection = null

            // 清理相关数据
            resultCode = 0
            resultData = null

            // 更新权限状态
            MediaProjectionUtils.savePermissionStatus(this, false)

            Timber.w("无效的MediaProjection对象已清理完成")
        } catch (e: Exception) {
            Timber.e(e, "清理MediaProjection对象失败")
        }
    }

    /**
     * 更新服务运行状态到SharedPreferences
     */
    private fun updateServiceRunningStatus(isRunning: Boolean) {
        try {
            val sharedPrefs = getSharedPreferences("service_status", Context.MODE_PRIVATE)
            sharedPrefs.edit()
                .putBoolean("screenshot_service_running", isRunning)
                .putLong("last_update_time", System.currentTimeMillis())
                .apply()
            Timber.d("服务运行状态已更新: $isRunning")
        } catch (e: Exception) {
            Timber.e(e, "更新服务运行状态失败")
        }
    }

    override fun onBind(intent: Intent?): IBinder? = null

    companion object {
        const val ACTION_START_SERVICE = "com.shuyi.discipline.ACTION_START_SERVICE"
        const val ACTION_STOP_SERVICE = "com.shuyi.discipline.ACTION_STOP_SERVICE"
        const val ACTION_TAKE_SCREENSHOT = "com.shuyi.discipline.ACTION_TAKE_SCREENSHOT"
        const val ACTION_UPDATE_SCHEDULE = "com.shuyi.discipline.ACTION_UPDATE_SCHEDULE"
        const val ACTION_CHECK_STATUS = "com.shuyi.discipline.ACTION_CHECK_STATUS"
        const val ACTION_SERVICE_STATUS = "com.shuyi.discipline.ACTION_SERVICE_STATUS"

        private const val CHANNEL_ID = "screenshot_service_channel"
        private const val NOTIFICATION_ID = 1001
    }
}