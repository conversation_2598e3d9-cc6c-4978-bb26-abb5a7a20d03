package com.shuyi.discipline.ui.screen.config

import android.app.Application
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.shuyi.discipline.data.model.CollageSettings
import com.shuyi.discipline.ui.components.PageTitleBar
import com.shuyi.discipline.ui.components.SettingsCard
import com.shuyi.discipline.ui.components.SettingsSwitchItem
import com.shuyi.discipline.ui.components.TimePickerDialog
import com.shuyi.discipline.ui.model.UiState
import java.text.SimpleDateFormat
import java.util.*

/**
 * 拼图设置页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CollageSettingsScreen(
    onNavigateBack: () -> Unit
) {
    val scrollState = rememberScrollState()
    
    // 获取拼图设置ViewModel
    val collageSettingsViewModel: CollageSettingsViewModel = viewModel(
        factory = CollageSettingsViewModelFactory(LocalContext.current.applicationContext as Application)
    )
    val collageSettingsState by collageSettingsViewModel.collageSettings.collectAsStateWithLifecycle()

    Scaffold(
        containerColor = MaterialTheme.colorScheme.background,
        topBar = {
            PageTitleBar(
                title = "拼图设置",
                showBackButton = true,
                onBackClick = onNavigateBack
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(MaterialTheme.colorScheme.background)
                .padding(horizontal = 16.dp)
                .verticalScroll(scrollState),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            when (collageSettingsState) {
                is UiState.Loading -> {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(200.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
                is UiState.Error -> {
                    Text(
                        text = "加载拼图设置失败: ${(collageSettingsState as UiState.Error).message}",
                        color = MaterialTheme.colorScheme.error
                    )
                }
                is UiState.Success -> {
                    val settings = (collageSettingsState as UiState.Success<CollageSettings>).data

                    SettingsCard(title = "自动拼图") {
                        Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                            // 自动拼图
                            SettingsSwitchItem(
                                title = "每日自动拼图",
                                description = "在指定时间自动生成当日拼图",
                                checked = settings.isAutoCollageEnabled,
                                onCheckedChange = { collageSettingsViewModel.updateAutoCollageEnabled(it) }
                            )

                            // 拼图时间
                            if (settings.isAutoCollageEnabled) {
                                Text(
                                    text = "拼图时间",
                                    fontSize = 14.sp,
                                    color = MaterialTheme.colorScheme.onSecondaryContainer,
                                    modifier = Modifier.padding(bottom = 4.dp, top = 8.dp)
                                )

                                // 时间选择器
                                val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
                                val calendar = Calendar.getInstance().apply {
                                    set(Calendar.HOUR_OF_DAY, settings.collageHour)
                                    set(Calendar.MINUTE, settings.collageMinute)
                                }
                                val timeString = timeFormat.format(calendar.time)

                                var showTimePicker by remember { mutableStateOf(false) }

                                Card(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .clickable { showTimePicker = true },
                                    shape = RoundedCornerShape(8.dp),
                                    colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surfaceVariant)
                                ) {
                                    Text(
                                        text = timeString,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                                        modifier = Modifier.padding(16.dp)
                                    )
                                }

                                if (showTimePicker) {
                                    TimePickerDialog(
                                        initialHour = settings.collageHour,
                                        initialMinute = settings.collageMinute,
                                        onDismiss = { showTimePicker = false },
                                        onConfirm = { hour, minute ->
                                            val newCalendar = Calendar.getInstance().apply {
                                                set(Calendar.HOUR_OF_DAY, hour)
                                                set(Calendar.MINUTE, minute)
                                            }
                                            collageSettingsViewModel.updateCollageTime(
                                                timeFormat.format(newCalendar.time)
                                            )
                                            showTimePicker = false
                                        }
                                    )
                                }
                            }
                        }
                    }

                    SettingsCard(title = "拼图布局") {
                        Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                            // 拼图布局
                            Text(
                                text = "拼图布局",
                                fontSize = 14.sp,
                                color = MaterialTheme.colorScheme.onSecondaryContainer,
                                modifier = Modifier.padding(bottom = 4.dp, top = 8.dp)
                            )

                            var showLayoutOptions by remember { mutableStateOf(false) }

                            Box {
                                Card(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .clickable { showLayoutOptions = true },
                                    shape = RoundedCornerShape(8.dp),
                                    colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surfaceVariant)
                                ) {
                                    Text(
                                        text = settings.collageLayout.displayName,
                                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                                        modifier = Modifier.padding(16.dp)
                                    )
                                }

                                DropdownMenu(
                                    expanded = showLayoutOptions,
                                    onDismissRequest = { showLayoutOptions = false }
                                ) {
                                    collageSettingsViewModel.layoutOptions.forEach { option ->
                                        DropdownMenuItem(
                                            text = { Text(option) },
                                            onClick = {
                                                collageSettingsViewModel.updateCollageLayout(option)
                                                showLayoutOptions = false
                                            }
                                        )
                                    }
                                }
                            }
                        }
                    }

                    SettingsCard(title = "拼图质量") {
                        Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                            // 拼图质量
                            Text(
                                text = "拼图质量",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSecondaryContainer
                            )

                            var collageQualityValue by remember { mutableFloatStateOf(settings.collageQuality.toFloat()) }
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Slider(
                                    value = collageQualityValue,
                                    onValueChange = { newValue ->
                                        collageQualityValue = newValue
                                    },
                                    onValueChangeFinished = {
                                        collageSettingsViewModel.updateCollageQuality(collageQualityValue.toInt())
                                    },
                                    valueRange = 1f..100f,
                                    modifier = Modifier.weight(1f)
                                )

                                Text(
                                    text = "${collageQualityValue.toInt()}%",
                                    style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
                                    modifier = Modifier.padding(start = 8.dp).width(48.dp),
                                    textAlign = TextAlign.End
                                )
                            }

                            // 分辨率
                            Text(
                                text = "分辨率",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSecondaryContainer
                            )

                            var collageResolutionValue by remember { mutableFloatStateOf(settings.collageResolution.toFloat()) }
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Slider(
                                    value = collageResolutionValue,
                                    onValueChange = { newValue ->
                                        collageResolutionValue = newValue
                                    },
                                    onValueChangeFinished = {
                                        collageSettingsViewModel.updateCollageResolution(collageResolutionValue.toInt())
                                    },
                                    valueRange = 1f..100f,
                                    modifier = Modifier.weight(1f)
                                )

                                Text(
                                    text = "${collageResolutionValue.toInt()}%",
                                    style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
                                    modifier = Modifier.padding(start = 8.dp).width(48.dp),
                                    textAlign = TextAlign.End
                                )
                            }

                            // 高斯模糊
                            Text(
                                text = "高斯模糊",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSecondaryContainer
                            )

                            var collageBlurValue by remember { mutableFloatStateOf(settings.collageBlur.toFloat()) }
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Slider(
                                    value = collageBlurValue,
                                    onValueChange = { newValue ->
                                        collageBlurValue = newValue
                                    },
                                    onValueChangeFinished = {
                                        collageSettingsViewModel.updateCollageBlur(collageBlurValue.toInt())
                                    },
                                    valueRange = 0f..100f,
                                    modifier = Modifier.weight(1f)
                                )

                                Text(
                                    text = "${collageBlurValue.toInt()}%",
                                    style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
                                    modifier = Modifier.padding(start = 8.dp).width(48.dp),
                                    textAlign = TextAlign.End
                                )
                            }
                        }
                    }
                }
                else -> {
                    // 处理其他状态
                    Text("准备加载拼图设置...")
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun CollageSettingsScreenPreview() {
    MaterialTheme {
        CollageSettingsScreen(
            onNavigateBack = {}
        )
    }
}
