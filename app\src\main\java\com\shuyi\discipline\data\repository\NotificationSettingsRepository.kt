package com.shuyi.discipline.data.repository

import com.shuyi.discipline.data.model.NotificationSettings
import kotlinx.coroutines.flow.Flow

/**
 * 通知设置仓库接口
 */
interface NotificationSettingsRepository {
    
    /**
     * 获取通知设置
     */
    fun getNotificationSettings(): Flow<NotificationSettings>
    
    /**
     * 获取通知设置（同步）
     */
    suspend fun getNotificationSettingsSync(): NotificationSettings
    
    /**
     * 保存通知设置
     */
    suspend fun saveNotificationSettings(notificationSettings: NotificationSettings)
    
    /**
     * 更新截屏通知启用状态
     */
    suspend fun updateScreenshotNotificationEnabled(enabled: Boolean)
    
    /**
     * 更新拼图通知启用状态
     */
    suspend fun updateCollageNotificationEnabled(enabled: Boolean)
}
