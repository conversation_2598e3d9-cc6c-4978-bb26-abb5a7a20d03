package com.shuyi.discipline.data.repository

import com.shuyi.discipline.domain.monitor.ExitReason
import com.shuyi.discipline.domain.monitor.RuntimeStats
import com.shuyi.discipline.domain.monitor.SessionStatus
import kotlinx.coroutines.flow.Flow

/**
 * 运行时记录仓库接口
 * 管理应用运行时间和退出记录
 */
interface RuntimeRecordRepository {

    // ==================== 会话管理 ====================
    
    /**
     * 开始新会话
     */
    suspend fun startNewSession(startTime: Long): String

    /**
     * 结束当前会话
     */
    suspend fun endCurrentSession(endTime: Long, exitReason: ExitReason)

    /**
     * 强制结束会话
     */
    suspend fun forceEndSession(sessionId: String, endTime: Long, exitReason: ExitReason)

    /**
     * 更新会话状态
     */
    suspend fun updateSessionStatus(status: SessionStatus)

    /**
     * 获取当前活跃会话
     */
    suspend fun getCurrentSession(): RuntimeSession?

    /**
     * 获取未完成的会话
     */
    suspend fun getUnfinishedSessions(): List<RuntimeSession>

    // ==================== 退出记录 ====================

    /**
     * 记录应用退出
     */
    suspend fun recordExit(
        exitTime: Long,
        exitReason: ExitReason,
        description: String,
        downtimeStart: Long,
        downtimeEnd: Long,
        downtimeDuration: Long
    )

    /**
     * 获取最后处理的退出时间
     */
    suspend fun getLastProcessedExitTime(): Long

    /**
     * 更新最后处理的退出时间
     */
    suspend fun updateLastProcessedExitTime(exitTime: Long)

    /**
     * 获取最后记录的退出时间
     */
    suspend fun getLastRecordedExitTime(): Long

    /**
     * 检查指定时间后是否有退出记录
     */
    suspend fun hasExitRecordAfter(timestamp: Long): Boolean

    // ==================== 运行时统计 ====================

    /**
     * 获取运行时统计
     */
    suspend fun getRuntimeStats(hours: Int = 24): RuntimeStats

    /**
     * 获取指定时间范围的运行时统计
     */
    suspend fun getRuntimeStatsInRange(startTime: Long, endTime: Long): RuntimeStats

    /**
     * 获取总运行时长
     */
    suspend fun getTotalRuntime(hours: Int = 24): Long

    /**
     * 获取总未运行时长
     */
    suspend fun getTotalDowntime(hours: Int = 24): Long

    /**
     * 获取会话数量
     */
    suspend fun getSessionCount(hours: Int = 24): Int

    // ==================== 数据查询 ====================

    /**
     * 获取所有运行记录
     */
    fun getAllRecords(): Flow<List<RuntimeRecord>>

    /**
     * 获取指定时间范围的记录
     */
    suspend fun getRecordsInRange(startTime: Long, endTime: Long): List<RuntimeRecord>

    /**
     * 获取最近的记录
     */
    suspend fun getRecentRecords(limit: Int = 10): List<RuntimeRecord>

    /**
     * 获取所有会话
     */
    fun getAllSessions(): Flow<List<RuntimeSession>>

    /**
     * 获取指定时间范围的会话
     */
    suspend fun getSessionsInRange(startTime: Long, endTime: Long): List<RuntimeSession>

    /**
     * 获取当前会话ID
     */
    suspend fun getCurrentSessionId(): String?

    /**
     * 根据ID获取会话
     */
    suspend fun getSessionById(sessionId: String): RuntimeSession?

    // ==================== 数据维护 ====================

    /**
     * 清理旧数据
     */
    suspend fun cleanOldData(cutoffTime: Long)

    /**
     * 删除所有数据
     */
    suspend fun deleteAllData()

    /**
     * 重置监控状态（保留监控配置，避免重新处理历史数据）
     */
    suspend fun resetMonitoringState()

    /**
     * 获取数据库大小
     */
    suspend fun getDatabaseSize(): Long

    /**
     * 导出数据
     */
    suspend fun exportData(): String

    /**
     * 导入数据
     */
    suspend fun importData(data: String): Boolean
}

/**
 * 运行时记录数据模型
 */
data class RuntimeRecord(
    val id: String,
    val timestamp: Long,
    val type: RecordType,
    val sessionId: String?,
    val exitReason: ExitReason?,
    val duration: Long,
    val description: String,
    val endTime: Long? = null
)

/**
 * 运行时会话数据模型
 */
data class RuntimeSession(
    val sessionId: String,
    val startTime: Long,
    val endTime: Long?,
    val status: SessionStatus,
    val exitReason: ExitReason?,
    val totalDuration: Long,
    val foregroundDuration: Long,
    val backgroundDuration: Long
)

/**
 * 记录类型
 */
enum class RecordType {
    SESSION_START,    // 会话开始
    SESSION_END,      // 会话结束
    APP_EXIT,         // 应用退出
    DOWNTIME,         // 未运行时长
    STATUS_CHANGE     // 状态变化
}
