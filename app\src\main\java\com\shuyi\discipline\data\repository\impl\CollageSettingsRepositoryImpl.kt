package com.shuyi.discipline.data.repository.impl

import com.shuyi.discipline.data.model.CollageLayout
import com.shuyi.discipline.data.model.CollageSettings
import com.shuyi.discipline.data.repository.CollageSettingsRepository
import com.shuyi.discipline.data.source.database.CollageSettingsDao
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

/**
 * 拼图设置仓库实现类
 */
class CollageSettingsRepositoryImpl @Inject constructor(
    private val collageSettingsDao: CollageSettingsDao
) : CollageSettingsRepository {
    
    override fun getCollageSettings(): Flow<CollageSettings> {
        return collageSettingsDao.getCollageSettings().map { 
            it ?: CollageSettings() 
        }
    }
    
    override suspend fun getCollageSettingsSync(): CollageSettings {
        return collageSettingsDao.getCollageSettingsSync() ?: CollageSettings()
    }
    
    override suspend fun saveCollageSettings(collageSettings: CollageSettings) {
        collageSettingsDao.insertCollageSettings(collageSettings)
    }
    
    override suspend fun updateAutoCollageEnabled(enabled: Boolean) {
        val settings = getCollageSettingsSync()
        saveCollageSettings(settings.copy(isAutoCollageEnabled = enabled))
    }
    
    override suspend fun updateCollageTime(hour: Int, minute: Int) {
        val settings = getCollageSettingsSync()
        saveCollageSettings(settings.copy(collageHour = hour, collageMinute = minute))
    }
    
    override suspend fun updateCollageLayout(layout: CollageLayout) {
        val settings = getCollageSettingsSync()
        saveCollageSettings(settings.copy(collageLayout = layout))
    }
    
    override suspend fun updateCollageQuality(quality: Int) {
        val settings = getCollageSettingsSync()
        saveCollageSettings(settings.copy(collageQuality = quality))
    }

    override suspend fun updateCollageResolution(resolution: Int) {
        val settings = getCollageSettingsSync()
        saveCollageSettings(settings.copy(collageResolution = resolution))
    }

    override suspend fun updateCollageBlur(blur: Int) {
        val settings = getCollageSettingsSync()
        saveCollageSettings(settings.copy(collageBlur = blur))
    }

    override suspend fun updateLastCollageTime(time: Long) {
        val settings = getCollageSettingsSync()
        saveCollageSettings(settings.copy(lastCollageTime = time))
    }
}
