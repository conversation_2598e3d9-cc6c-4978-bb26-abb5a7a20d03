package com.shuyi.discipline.ui.screen.report

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowDownward
import androidx.compose.material.icons.filled.ArrowUpward
import androidx.compose.material.icons.filled.Share
import androidx.compose.material.icons.filled.Sort
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Image
import androidx.compose.material.icons.filled.Dashboard
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.viewmodel.compose.viewModel
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.shuyi.discipline.data.model.CollageReport
import com.shuyi.discipline.domain.model.ScreenshotDetail
import com.shuyi.discipline.ui.components.BottomNavBar
import com.shuyi.discipline.ui.components.PageTitleBar
import com.shuyi.discipline.ui.model.UiState
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import timber.log.Timber
import android.app.Application

/**
 * 报告详情屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ReportDetailScreen(
    date: String,
    onNavigateBack: () -> Unit,
    viewModel: ReportDetailViewModel = viewModel(
        factory = ReportViewModelFactory(LocalContext.current.applicationContext as Application)
    )
) {
    val screenshotsState by viewModel.screenshots.collectAsState()
    val sortOrder by viewModel.sortOrder.collectAsState()
    var selectedScreenshot by remember { mutableStateOf<ScreenshotDetail?>(null) }
    var showSortMenu by remember { mutableStateOf(false) }
    val scrollState = rememberScrollState()
    var selectedTabIndex by remember { mutableStateOf(1) } // 截图选项卡

    // 在Composable外处理日期格式化，避免try-catch直接包裹Composable
    val screenTitle = remember(date) {
        try {
            val localDate = LocalDate.parse(date)
            val formatter = DateTimeFormatter.ofPattern("yyyy年MM月dd日")
            localDate.format(formatter)
        } catch (e: Exception) {
            "截图详情"
        }
    }

    LaunchedEffect(key1 = date) {
        viewModel.loadScreenshotsForDate(date)
    }

    // 定义颜色
    val lightGray = Color(0xFFF8F8F8)
    val primaryBlue = Color(0xFF007AFF)

    Scaffold(
        containerColor = lightGray,
        bottomBar = {
            BottomNavBar(
                selectedIndex = selectedTabIndex,
                onItemSelected = { index -> selectedTabIndex = index },
                onNavigateToHome = onNavigateBack,
                onNavigateToScreenshots = { /* 暂时不处理 */ },
                onNavigateToCollages = { /* 已在报告详情页面，无需导航 */ },
                onNavigateToConfig = { /* 暂时不处理 */ }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(lightGray)
        ) {
            // 不再使用StatusBar，直接从顶部开始

            // 标题栏和操作按钮
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    IconButton(
                        onClick = onNavigateBack,
                        modifier = Modifier.size(24.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.ArrowBack,
                            contentDescription = "返回",
                            tint = Color.Black
                        )
                    }
                    Spacer(modifier = Modifier.width(8.dp))

                    Text(
                        text = "${screenTitle}的截图",
                        fontSize = 24.sp,
                        fontWeight = FontWeight.Bold
                    )
                }

                Row {
                    // 排序按钮
                    Box {
                        IconButton(onClick = { showSortMenu = true }) {
                            Icon(
                                imageVector = if (sortOrder == SortOrder.ASCENDING)
                                    Icons.Default.ArrowUpward else Icons.Default.ArrowDownward,
                                contentDescription = "排序",
                                tint = primaryBlue
                            )
                        }

                        DropdownMenu(
                            expanded = showSortMenu,
                            onDismissRequest = { showSortMenu = false }
                        ) {
                            DropdownMenuItem(
                                text = {
                                    Row(verticalAlignment = Alignment.CenterVertically) {
                                        Icon(
                                            imageVector = Icons.Default.ArrowUpward,
                                            contentDescription = null,
                                            tint = primaryBlue
                                        )
                                        Spacer(modifier = Modifier.width(8.dp))
                                        Text("从早到晚")
                                    }
                                },
                                onClick = {
                                    viewModel.setSortOrder(SortOrder.ASCENDING)
                                    showSortMenu = false
                                }
                            )

                            DropdownMenuItem(
                                text = {
                                    Row(verticalAlignment = Alignment.CenterVertically) {
                                        Icon(
                                            imageVector = Icons.Default.ArrowDownward,
                                            contentDescription = null,
                                            tint = primaryBlue
                                        )
                                        Spacer(modifier = Modifier.width(8.dp))
                                        Text("从晚到早")
                                    }
                                },
                                onClick = {
                                    viewModel.setSortOrder(SortOrder.DESCENDING)
                                    showSortMenu = false
                                }
                            )
                        }
                    }

                    // 分享按钮
                    IconButton(onClick = { viewModel.shareReport(date) }) {
                        Icon(
                            Icons.Default.Share,
                            contentDescription = "分享",
                            tint = primaryBlue
                        )
                    }
                }
            }

            // 内容区域
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp)
            ) {
            when (screenshotsState) {
                is UiState.Loading -> {
                    Text("加载中...")
                }
                is UiState.Empty -> {
                    Column(
                        modifier = Modifier.fillMaxSize(),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Text(
                            text = "该日期没有截图",
                            style = MaterialTheme.typography.titleMedium,
                            textAlign = TextAlign.Center
                        )
                    }
                }
                is UiState.Error -> {
                    Text("加载错误: ${(screenshotsState as UiState.Error).message}")
                }
                is UiState.Success -> {
                    val screenshots = (screenshotsState as UiState.Success<List<ScreenshotDetail>>).data

                    if (screenshots.isEmpty()) {
                        Column(
                            modifier = Modifier.fillMaxSize(),
                            horizontalAlignment = Alignment.CenterHorizontally,
                            verticalArrangement = Arrangement.Center
                        ) {
                            Text(
                                text = "该日期没有截图",
                                style = MaterialTheme.typography.titleMedium,
                                textAlign = TextAlign.Center
                            )
                        }
                    } else {
                        Text(
                            text = "本日共 ${screenshots.size} 张截图",
                            style = MaterialTheme.typography.titleMedium
                        )

                        Spacer(modifier = Modifier.height(16.dp))

                        LazyVerticalGrid(
                            columns = GridCells.Fixed(3),
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            items(screenshots) { screenshot ->
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .aspectRatio(0.75f)
                                        .clip(RoundedCornerShape(16.dp))
                                        .clickable { selectedScreenshot = screenshot }
                                ) {
                                    AsyncImage(
                                        model = ImageRequest.Builder(LocalContext.current)
                                            .data(screenshot.path)
                                            .crossfade(true)
                                            .build(),
                                        contentDescription = "截图",
                                        contentScale = ContentScale.Crop,
                                        modifier = Modifier.fillMaxSize()
                                    )

                                    Box(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .background(Color.Black.copy(alpha = 0.6f))
                                            .align(Alignment.BottomCenter)
                                            .padding(8.dp)
                                    ) {
                                        Text(
                                            text = screenshot.timeFormatted,
                                            fontSize = 12.sp,
                                            color = Color.White,
                                            fontWeight = FontWeight.Medium,
                                            textAlign = TextAlign.Center,
                                            modifier = Modifier.fillMaxWidth()
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
                else -> {
                    // 处理其他状态（如Idle）
                    Text("准备加载...")
                }
            }
        }
    }

    // 显示截图详情对话框
    selectedScreenshot?.let { screenshot ->
        Dialog(onDismissRequest = { selectedScreenshot = null }) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(Color.White, RoundedCornerShape(24.dp))
                    .padding(20.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "截图详情",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )

                    Box(
                        modifier = Modifier
                            .size(32.dp)
                            .clip(CircleShape)
                            .background(Color(0xFFE9E9EB))
                            .clickable { selectedScreenshot = null },
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            Icons.Default.ArrowBack,
                            contentDescription = "关闭",
                            tint = Color.Gray,
                            modifier = Modifier.size(16.dp)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                Row(
                    modifier = Modifier.fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "时间:",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color.Gray
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    Text(
                        text = screenshot.timeFormatted,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.SemiBold
                    )
                }

                Spacer(modifier = Modifier.height(8.dp))

                if (screenshot.appName != null) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "应用:",
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium,
                            color = Color.Gray
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        Text(
                            text = screenshot.appName,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.SemiBold
                        )
                    }

                    Spacer(modifier = Modifier.height(16.dp))
                }

                AsyncImage(
                    model = ImageRequest.Builder(LocalContext.current)
                        .data(screenshot.path)
                        .crossfade(true)
                        .build(),
                    contentDescription = "截图详情",
                    contentScale = ContentScale.FillWidth,
                    modifier = Modifier
                        .fillMaxWidth()
                        .clip(RoundedCornerShape(16.dp))
                )
            }
        }
    }
}



@Preview(showBackground = true)
@Composable
fun ReportDetailScreenPreview() {
    MaterialTheme {
        ReportDetailScreen(
            date = "2023-04-15",
            onNavigateBack = {}
        )
    }
}

@Composable
fun ReportDetailContent(
    date: String,
    viewModel: ReportDetailViewModel
) {
    val report by viewModel.report.collectAsState()
    val context = LocalContext.current

    // 加载报告
    LaunchedEffect(date) {
        viewModel.loadReport(date)
    }

    when (report) {
        is UiState.Loading -> {
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "加载中...",
                    style = MaterialTheme.typography.titleMedium,
                    textAlign = TextAlign.Center
                )
            }
        }
        is UiState.Empty -> {
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "${date}没有报告",
                    style = MaterialTheme.typography.titleMedium,
                    textAlign = TextAlign.Center
                )
            }
        }
        is UiState.Success -> {
            val reportData = (report as UiState.Success<CollageReport>).data
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // 日期标题
                Text(
                    text = date,
                    style = MaterialTheme.typography.headlineMedium,
                    modifier = Modifier.padding(bottom = 16.dp)
                )

                // 拼图图片
                val bitmap = BitmapFactory.decodeFile(reportData.collagePath)
                if (bitmap != null) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .fillMaxHeight(0.8f)
                            .clip(RoundedCornerShape(8.dp))
                    ) {
                        androidx.compose.foundation.Image(
                            bitmap = bitmap.asImageBitmap(),
                            contentDescription = "截图详情",
                            contentScale = ContentScale.Fit,
                            modifier = Modifier.fillMaxSize()
                        )
                    }
                } else {
                    Text("无法加载拼图图片")
                }
            }
        }
        is UiState.Error -> {
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "加载错误: ${(report as UiState.Error).message}",
                    style = MaterialTheme.typography.titleMedium,
                    textAlign = TextAlign.Center
                )
            }
        }
        else -> {
            // 处理Idle状态
            Text("准备加载报告...")
        }
    }
}


}