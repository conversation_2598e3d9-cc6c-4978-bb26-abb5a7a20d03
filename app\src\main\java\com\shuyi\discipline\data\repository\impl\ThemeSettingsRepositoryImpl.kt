package com.shuyi.discipline.data.repository.impl

import com.shuyi.discipline.data.model.ThemeSettings
import com.shuyi.discipline.data.repository.ThemeSettingsRepository
import com.shuyi.discipline.data.source.database.ThemeSettingsDao
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import javax.inject.Inject

/**
 * 主题设置仓库实现类
 */
class ThemeSettingsRepositoryImpl @Inject constructor(
    private val themeSettingsDao: ThemeSettingsDao
) : ThemeSettingsRepository {
    
    override fun getThemeSettings(): Flow<ThemeSettings> {
        return themeSettingsDao.getThemeSettings().map { 
            it ?: ThemeSettings() 
        }
    }
    
    override suspend fun getThemeSettingsSync(): ThemeSettings {
        return themeSettingsDao.getThemeSettingsSync() ?: ThemeSettings()
    }
    
    override suspend fun saveThemeSettings(themeSettings: ThemeSettings) {
        themeSettingsDao.insertThemeSettings(themeSettings.copy(updatedAt = System.currentTimeMillis()))
    }
}
