package com.shuyi.discipline.ui.screen.config

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.shuyi.discipline.data.model.DeduplicationStats
import com.shuyi.discipline.data.model.ScreenshotDeduplication
import com.shuyi.discipline.data.repository.ScreenshotDeduplicationRepository
import com.shuyi.discipline.ui.model.UiState
import com.shuyi.discipline.utils.SensitivityLevel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * 截图去重设置ViewModel
 */
@HiltViewModel
class ScreenshotDeduplicationViewModel @Inject constructor(
    private val application: Application,
    private val screenshotDeduplicationRepository: ScreenshotDeduplicationRepository
) : AndroidViewModel(application) {
    
    // 去重设置状态
    private val _deduplicationSettings = MutableStateFlow<UiState<ScreenshotDeduplication>>(UiState.Loading)
    val deduplicationSettings: StateFlow<UiState<ScreenshotDeduplication>> = _deduplicationSettings
    
    // 去重统计状态
    private val _deduplicationStats = MutableStateFlow<UiState<DeduplicationStats>>(UiState.Loading)
    val deduplicationStats: StateFlow<UiState<DeduplicationStats>> = _deduplicationStats
    
    // 敏感度级别选项
    val sensitivityLevelOptions = SensitivityLevel.values().map { it.displayName }
    
    // 初始化
    init {
        loadDeduplicationSettings()
        loadDeduplicationStats()
    }
    
    /**
     * 加载去重设置
     */
    private fun loadDeduplicationSettings() {
        viewModelScope.launch {
            _deduplicationSettings.value = UiState.Loading
            
            screenshotDeduplicationRepository.getDeduplication()
                .catch { e ->
                    Timber.e(e, "加载去重设置失败")
                    _deduplicationSettings.value = UiState.Error(e.message ?: "未知错误")
                }
                .collect { settings ->
                    _deduplicationSettings.value = UiState.Success(settings)
                }
        }
    }
    
    /**
     * 加载去重统计信息
     */
    private fun loadDeduplicationStats() {
        viewModelScope.launch {
            try {
                _deduplicationStats.value = UiState.Loading
                val stats = screenshotDeduplicationRepository.getDeduplicationStats()
                _deduplicationStats.value = UiState.Success(stats)
            } catch (e: Exception) {
                Timber.e(e, "加载去重统计失败")
                _deduplicationStats.value = UiState.Error(e.message ?: "未知错误")
            }
        }
    }
    
    /**
     * 更新去重功能启用状态
     */
    fun updateDeduplicationEnabled(enabled: Boolean) {
        updateSettings { currentSettings ->
            currentSettings.copy(isEnabled = enabled)
        }
        
        // 同时更新Repository中的状态
        viewModelScope.launch {
            try {
                screenshotDeduplicationRepository.updateEnabled(enabled)
                Timber.d("去重功能启用状态已更新: $enabled")
            } catch (e: Exception) {
                Timber.e(e, "更新去重功能启用状态失败")
            }
        }
    }
    
    /**
     * 更新敏感度级别
     */
    fun updateSensitivityLevel(levelName: String) {
        val level = SensitivityLevel.fromDisplayName(levelName)
        updateSettings { currentSettings ->
            currentSettings.copy(sensitivityLevel = level.threshold)
        }
        
        // 同时更新Repository中的状态
        viewModelScope.launch {
            try {
                screenshotDeduplicationRepository.updateSensitivityLevel(level)
                Timber.d("敏感度级别已更新: ${level.displayName}")
            } catch (e: Exception) {
                Timber.e(e, "更新敏感度级别失败")
            }
        }
    }
    
    /**
     * 重置统计信息
     */
    fun resetStats() {
        viewModelScope.launch {
            try {
                screenshotDeduplicationRepository.resetStats()
                loadDeduplicationStats() // 重新加载统计信息
                Timber.d("统计信息已重置")
            } catch (e: Exception) {
                Timber.e(e, "重置统计信息失败")
            }
        }
    }
    
    /**
     * 刷新统计信息
     */
    fun refreshStats() {
        loadDeduplicationStats()
    }
    
    /**
     * 获取当前敏感度级别
     */
    fun getCurrentSensitivityLevel(): SensitivityLevel {
        return when (val state = _deduplicationSettings.value) {
            is UiState.Success -> SensitivityLevel.fromThreshold(state.data.sensitivityLevel)
            else -> SensitivityLevel.MEDIUM
        }
    }
    
    /**
     * 获取当前去重启用状态
     */
    fun isDeduplicationEnabled(): Boolean {
        return when (val state = _deduplicationSettings.value) {
            is UiState.Success -> state.data.isEnabled
            else -> true
        }
    }
    
    /**
     * 通用的设置更新方法
     */
    private fun updateSettings(update: (ScreenshotDeduplication) -> ScreenshotDeduplication) {
        viewModelScope.launch {
            try {
                when (val currentState = _deduplicationSettings.value) {
                    is UiState.Success -> {
                        val updatedSettings = update(currentState.data).copy(
                            lastUpdatedTime = System.currentTimeMillis()
                        )
                        screenshotDeduplicationRepository.saveDeduplication(updatedSettings)
                        _deduplicationSettings.value = UiState.Success(updatedSettings)
                    }
                    else -> {
                        Timber.w("无法更新设置，当前状态不是Success")
                    }
                }
            } catch (e: Exception) {
                Timber.e(e, "更新设置失败")
                _deduplicationSettings.value = UiState.Error(e.message ?: "更新失败")
            }
        }
    }
    
    /**
     * 格式化文件大小显示
     */
    fun formatFileSize(sizeInMB: Double): String {
        return if (sizeInMB < 0.1) {
            String.format("%.1f MB", sizeInMB)
        } else {
            String.format("%.1f MB", sizeInMB)
        }
    }
    
    /**
     * 格式化去重率显示
     */
    fun formatDeduplicationRate(rate: Double): String {
        return String.format("%.1f%%", rate)
    }
}
