package com.shuyi.discipline.ui.screen.config

import android.os.Build
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.shuyi.discipline.data.model.ThemeSettings
import com.shuyi.discipline.ui.components.PageTitleBar
import com.shuyi.discipline.ui.components.SettingsCard
import com.shuyi.discipline.ui.components.SettingsNavigationItem
import com.shuyi.discipline.ui.components.SettingsSwitchItem
import com.shuyi.discipline.ui.model.UiState
import com.shuyi.discipline.ui.navigation.Screen

/**
 * 外观设置页面
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AppearanceSettingsScreen(
    onNavigateBack: () -> Unit,
    navController: androidx.navigation.NavController? = null,
    themeSettingsViewModel: ThemeSettingsViewModel = hiltViewModel()
) {
    val themeSettingsState by themeSettingsViewModel.themeSettings.collectAsStateWithLifecycle()
    val scrollState = rememberScrollState()

    Scaffold(
        containerColor = MaterialTheme.colorScheme.background,
        topBar = {
            PageTitleBar(
                title = "外观设置",
                showBackButton = true,
                onBackClick = onNavigateBack
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(MaterialTheme.colorScheme.background)
                .padding(horizontal = 16.dp)
                .verticalScroll(scrollState),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            when (themeSettingsState) {
                is UiState.Success -> {
                    val themeSettings = (themeSettingsState as UiState.Success<ThemeSettings>).data

                    SettingsCard(title = "主题设置") {
                        Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                            // 主题模式选择
                            Text(
                                text = "主题模式",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSecondaryContainer,
                                modifier = Modifier.padding(bottom = 4.dp)
                            )

                            var expanded by remember { mutableStateOf(false) }

                            Box {
                                Card(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .clickable { expanded = true },
                                    shape = RoundedCornerShape(8.dp),
                                    colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
                                ) {
                                    Text(
                                        text = themeSettings.themeMode.displayName,
                                        modifier = Modifier.padding(16.dp),
                                        color = MaterialTheme.colorScheme.onSurface
                                    )
                                }

                                DropdownMenu(
                                    expanded = expanded,
                                    onDismissRequest = { expanded = false }
                                ) {
                                    themeSettingsViewModel.themeModeOptions.forEach { option ->
                                        DropdownMenuItem(
                                            text = { Text(option) },
                                            onClick = {
                                                themeSettingsViewModel.updateThemeMode(option)
                                                expanded = false
                                            }
                                        )
                                    }
                                }
                            }

                            // 动态颜色开关（仅在 Android 12+ 显示）
                            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                                SettingsSwitchItem(
                                    title = "动态颜色",
                                    description = "根据壁纸自动调整应用颜色",
                                    checked = themeSettings.isDynamicColorEnabled,
                                    onCheckedChange = { themeSettingsViewModel.updateDynamicColorEnabled(it) }
                                )
                            }
                        }
                    }

                    SettingsCard(title = "字体设置") {
                        // 字体大小设置
                        SettingsNavigationItem(
                            title = "字体大小",
                            description = "当前: ${themeSettings.fontSize.displayName}",
                            onClick = {
                                navController?.navigate(Screen.FontSize.route)
                            }
                        )
                    }
                }
                is UiState.Loading -> {
                    SettingsCard(title = "外观设置") {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(100.dp),
                            contentAlignment = androidx.compose.ui.Alignment.Center
                        ) {
                            CircularProgressIndicator()
                        }
                    }
                }
                is UiState.Error -> {
                    SettingsCard(title = "外观设置") {
                        Text(
                            text = "加载错误: ${(themeSettingsState as UiState.Error).message}",
                            color = MaterialTheme.colorScheme.error
                        )
                    }
                }
                else -> {
                    // 其他状态
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun AppearanceSettingsScreenPreview() {
    MaterialTheme {
        AppearanceSettingsScreen(
            onNavigateBack = {}
        )
    }
}
