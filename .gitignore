# 编译输出
*.apk
*.aar
*.ap_
*.aab

# 文件
*.class
*.dex

# 日志文件
*.log

# Android Studio 生成的文件
*.iml
.gradle
/local.properties
/.idea/caches
/.idea/libraries
/.idea/modules.xml
/.idea/workspace.xml
/.idea/navEditor.xml
/.idea/assetWizardSettings.xml
/.idea/dictionaries
/.idea/gradle.xml
/.idea/compiler.xml
/.idea/jarRepositories.xml
/.idea/misc.xml
/.idea/vcs.xml
/.idea/deploymentTargetDropDown.xml
/.idea/kotlinc.xml
/.idea/.name
/.idea/.gitignore
/.idea/androidTestResultsUserPreferences.xml

# 外部原生构建文件夹
.cxx/

# Google Services (例如 API 或 Firebase)
google-services.json

# 版本控制
.DS_Store

# 构建文件夹
/build
/app/build
/captures
.externalNativeBuild

# 本地配置文件 (sdk 路径等)
local.properties

# Proguard 文件
/proguard

# Keystore 文件
*.jks
*.keystore


# Freeline
freeline.py
freeline/
freeline_project_description.json

# fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output
fastlane/readme.md

# 版本控制
vcs.xml

# lint
lint/intermediates/
lint/generated/
lint/outputs/
lint/tmp/

# Kotlin 特定
*.kotlin_module

# 临时文件
*.tmp
*.swp
*~

# 外部工具
.externalToolBuilders/

# 备份文件
*.bak


.cxx
.idea
/kotlin-js-store
.vscode

/_assets
/.kotlin
/gradle/libs.versions.updates.toml


# Eclipse project files
.classpath
.project


# IDEA/Android Studio project files, because
# the project can be imported from settings.gradle.kts
#.idea/*
#!.idea/copyright
## Keep the code styles.
#!/.idea/codeStyles
#/.idea/codeStyles/*
#!/.idea/codeStyles/Project.xml
#!/.idea/codeStyles/codeStyleConfig.xml


# Sandbox stuff
_sandbox

# Android Studio captures folder
captures/

# Kotlin
.kotlin

# YoYo AI version control directory
.yoyo/
