package com.shuyi.discipline.data.model

import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 截图调度规则数据模型
 */
@Entity(tableName = "schedule_rules")
data class ScheduleRule(
    @PrimaryKey val id: Int = 1, // 只有一个调度规则，所以ID固定为1
    val intervalMinutes: Int, // 截图间隔（分钟）
    val intervalSeconds: Int = 0, // 截图间隔（秒）
    val isRandom: Boolean, // 是否使用随机间隔
    val randomRange: Int = 0, // 随机范围（秒，保留兼容性）
    val randomRangeMinutes: Int = 0, // 随机范围（分钟）
    val randomRangeSeconds: Int = 0, // 随机范围（秒）
    val qualitySetting: Int, // 质量设置（1-100）
    val resolutionSetting: Int = 100, // 分辨率设置（1-100%）
    val blurSetting: Int = 0, // 高斯模糊设置（0-100%，数值越大越模糊）
    val lastCaptureTime: Long = 0L, // 上次截图时间
    val isEnabled: Boolean = true // 是否启用截图服务
)