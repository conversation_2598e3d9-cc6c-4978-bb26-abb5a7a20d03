package com.shuyi.discipline.domain.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.shuyi.discipline.domain.service.ScreenshotService
import com.shuyi.discipline.ui.screen.home.HomeViewModel
import timber.log.Timber
import javax.inject.Inject

/**
 * 服务状态广播接收器
 */
class ServiceStatusReceiver : BroadcastReceiver() {

    @Inject
    lateinit var viewModel: HomeViewModel

    override fun onReceive(context: Context, intent: Intent) {
        if (intent.action == ScreenshotService.ACTION_SERVICE_STATUS) {
            val isRunning = intent.getBooleanExtra("is_running", false)
            val hasPermission = intent.getBooleanExtra("has_permission", false)

            Timber.d("收到服务状态广播: 运行中=$isRunning, 有权限=$hasPermission")

            // 通知 ViewModel 更新状态
            // 这里可以使用 EventBus 或其他方式通知 ViewModel
        }
    }
}