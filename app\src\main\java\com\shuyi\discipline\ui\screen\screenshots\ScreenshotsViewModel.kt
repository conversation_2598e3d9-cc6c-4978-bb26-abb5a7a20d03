package com.shuyi.discipline.ui.screen.screenshots

import android.app.Application
import android.content.Intent
import androidx.core.content.FileProvider
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.shuyi.discipline.data.model.Screenshot
import com.shuyi.discipline.data.repository.ScreenshotRepository
import com.shuyi.discipline.data.repository.ScreenshotDeduplicationRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import javax.inject.Inject

/**
 * 截图页面ViewModel
 */
@HiltViewModel
class ScreenshotsViewModel @Inject constructor(
    private val application: Application,
    private val screenshotRepository: ScreenshotRepository,
    private val screenshotDeduplicationRepository: ScreenshotDeduplicationRepository
) : AndroidViewModel(application) {

    // UI状态
    private val _uiState = MutableStateFlow(ScreenshotsUiState())
    val uiState: StateFlow<ScreenshotsUiState> = _uiState.asStateFlow()

    // 日期格式化
    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    private val dayOfWeekFormat = SimpleDateFormat("E", Locale.getDefault())
    private val dayFormat = SimpleDateFormat("d", Locale.getDefault())
    private val timeFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())

    // 当前监听的截图Flow
    private var currentScreenshotsFlow: Job? = null

    // 页面是否可见
    private var isPageVisible = false

    init {
        loadDateSelector()
        // 初始化时立即开始监听今天的截图
        val today = dateFormat.format(Date())
        _uiState.value = _uiState.value.copy(selectedDate = today)
        startObservingScreenshots(today)
    }

    /**
     * 加载日期选择器数据
     */
    private fun loadDateSelector() {
        val calendar = Calendar.getInstance()
        val today = calendar.time
        val dateItems = mutableListOf<DateItem>()

        // 生成前5天的日期
        for (i in 5 downTo 1) {
            calendar.time = today
            calendar.add(Calendar.DAY_OF_YEAR, -i)
            val date = calendar.time
            dateItems.add(
                DateItem(
                    date = date,
                    dayOfWeek = dayOfWeekFormat.format(date),
                    day = dayFormat.format(date),
                    isToday = false,
                    isTomorrow = false
                )
            )
        }

        // 添加今天
        calendar.time = today
        dateItems.add(
            DateItem(
                date = calendar.time,
                dayOfWeek = "今天",
                day = dayFormat.format(calendar.time),
                isToday = true,
                isTomorrow = false
            )
        )

        // 添加明天
        calendar.add(Calendar.DAY_OF_YEAR, 1)
        dateItems.add(
            DateItem(
                date = calendar.time,
                dayOfWeek = "明天",
                day = dayFormat.format(calendar.time),
                isToday = false,
                isTomorrow = true
            )
        )

        _uiState.value = _uiState.value.copy(
            dateItems = dateItems,
            selectedDateIndex = 5 // 默认选中今天
        )
    }

    /**
     * 加载指定日期的截图
     */
    fun loadScreenshots(dateString: String) {
        viewModelScope.launch {
            try {
                val screenshots = screenshotRepository.getScreenshotsForDay(dateString)
                Timber.d("加载了 ${screenshots.size} 张截图，日期: $dateString")

                // 按时间降序排序，最新的在前面
                val sortedScreenshots = screenshots.sortedByDescending { it.timestamp }

                _uiState.value = _uiState.value.copy(
                    selectedDate = dateString,
                    formattedDate = formatDateString(dateString),
                    totalScreenshots = screenshots.size,
                    allScreenshots = sortedScreenshots,
                    isLoading = false
                )
            } catch (e: Exception) {
                Timber.e(e, "加载截图失败")
                _uiState.value = _uiState.value.copy(
                    error = "加载截图失败: ${e.message}",
                    isLoading = false
                )
            }
        }
    }

    /**
     * 选择日期
     */
    fun selectDate(index: Int) {
        if (index in _uiState.value.dateItems.indices) {
            val dateItem = _uiState.value.dateItems[index]
            val dateString = dateFormat.format(dateItem.date)

            _uiState.value = _uiState.value.copy(
                selectedDateIndex = index,
                isLoading = true
            )

            startObservingScreenshots(dateString)
        }
    }

    /**
     * 开始监听指定日期的截图变化
     */
    private fun startObservingScreenshots(dateString: String) {
        // 取消之前的监听
        currentScreenshotsFlow?.cancel()

        // 开始新的监听
        currentScreenshotsFlow = viewModelScope.launch {
            try {
                screenshotRepository.observeScreenshotsForDay(dateString)
                    .flowOn(Dispatchers.IO) // 在IO线程处理数据
                    .distinctUntilChanged() // 避免重复处理相同数据
                    .collect { screenshots ->
                        Timber.d("监听到截图变化，共 ${screenshots.size} 张截图，日期: $dateString")

                        // 在IO线程进行排序，避免阻塞主线程
                        val sortedScreenshots = withContext(Dispatchers.IO) {
                            screenshots.sortedByDescending { it.timestamp }
                        }

                        // 由于重复截图已被删除，当前日期的去重数量为0
                        val duplicateCount = 0

                        _uiState.value = _uiState.value.copy(
                            selectedDate = dateString,
                            formattedDate = formatDateString(dateString),
                            totalScreenshots = screenshots.size,
                            duplicateCount = duplicateCount,
                            allScreenshots = sortedScreenshots,
                            isLoading = false
                        )
                    }
            } catch (e: CancellationException) {
                // 协程取消是正常的，不需要记录为错误
                Timber.d("监听截图变化协程被取消: ${e.message}")
            } catch (e: Exception) {
                Timber.e(e, "监听截图变化失败")
                _uiState.value = _uiState.value.copy(
                    error = "监听截图变化失败: ${e.message}",
                    isLoading = false
                )
            }
        }
    }

    /**
     * 设置页面可见性
     */
    fun setPageVisible(visible: Boolean) {
        isPageVisible = visible
        val currentDate = _uiState.value.selectedDate

        if (visible && currentDate.isNotEmpty()) {
            // 页面可见时确保监听正在运行
            if (currentScreenshotsFlow?.isActive != true) {
                startObservingScreenshots(currentDate)
            }
        }
        // 注意：不再在页面不可见时停止监听，保持数据实时更新
        // 这样可以确保用户切换回来时看到最新数据
    }

    /**
     * 刷新当前日期的截图（保留此方法以兼容现有调用）
     */
    fun refreshCurrentScreenshots() {
        val currentDate = _uiState.value.selectedDate
        if (currentDate.isNotEmpty()) {
            // 总是开始监听，确保数据实时更新
            startObservingScreenshots(currentDate)
        }
    }

    /**
     * 根据日期对象选择日期
     */
    fun selectDateByDate(selectedDate: Date) {
        val dateString = dateFormat.format(selectedDate)

        // 查找对应的日期项索引
        val dateIndex = _uiState.value.dateItems.indexOfFirst { dateItem ->
            dateFormat.format(dateItem.date) == dateString
        }

        // 如果找到了对应的日期项，更新选中索引
        if (dateIndex >= 0) {
            _uiState.value = _uiState.value.copy(
                selectedDateIndex = dateIndex,
                isLoading = true
            )
        } else {
            // 如果没有找到，说明选择的日期不在当前的日期列表中
            // 我们需要重新生成日期列表，以选中的日期为中心
            loadDateSelectorWithSelectedDate(selectedDate)
        }

        startObservingScreenshots(dateString)
    }

    /**
     * 以指定日期为中心重新加载日期选择器
     */
    private fun loadDateSelectorWithSelectedDate(selectedDate: Date) {
        val calendar = Calendar.getInstance()
        val dateItems = mutableListOf<DateItem>()

        // 生成以选中日期为中心的前5天到后1天
        calendar.time = selectedDate
        calendar.add(Calendar.DAY_OF_YEAR, -5)

        for (i in 0 until 7) {
            val date = calendar.time
            val isToday = isSameDay(date, Date())

            dateItems.add(
                DateItem(
                    date = date,
                    dayOfWeek = if (isToday) "今天" else dayOfWeekFormat.format(date),
                    day = dayFormat.format(date),
                    isToday = isToday,
                    isTomorrow = false
                )
            )
            calendar.add(Calendar.DAY_OF_YEAR, 1)
        }

        // 找到选中日期的索引
        val selectedIndex = dateItems.indexOfFirst {
            isSameDay(it.date, selectedDate)
        }.takeIf { it >= 0 } ?: 5 // 默认选中中间位置

        _uiState.value = _uiState.value.copy(
            dateItems = dateItems,
            selectedDateIndex = selectedIndex
        )
    }

    /**
     * 判断两个日期是否是同一天
     */
    private fun isSameDay(date1: Date, date2: Date): Boolean {
        val cal1 = Calendar.getInstance().apply { time = date1 }
        val cal2 = Calendar.getInstance().apply { time = date2 }
        return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
               cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR)
    }

    /**
     * 判断时间戳是否在指定时间范围内
     */
    private fun isInTimeRange(timestamp: Long, startHour: Int, endHour: Int): Boolean {
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = timestamp
        val hour = calendar.get(Calendar.HOUR_OF_DAY)
        return hour in startHour until endHour
    }



    /**
     * 格式化日期字符串
     */
    private fun formatDateString(dateString: String): String {
        try {
            val date = dateFormat.parse(dateString) ?: return dateString
            val calendar = Calendar.getInstance()
            calendar.time = date

            val year = calendar.get(Calendar.YEAR)
            val month = calendar.get(Calendar.MONTH) + 1
            val day = calendar.get(Calendar.DAY_OF_MONTH)

            return "${year}年${month}月${day}日"
        } catch (e: Exception) {
            Timber.e(e, "日期格式化失败")
            return dateString
        }
    }

    /**
     * 分享截图
     */
    fun shareScreenshot(filePath: String) {
        try {
            val file = File(filePath)
            if (!file.exists()) {
                Timber.e("文件不存在: $filePath")
                return
            }

            val authority = "${application.packageName}.provider"
            val uri = FileProvider.getUriForFile(application, authority, file)

            val shareIntent = Intent().apply {
                action = Intent.ACTION_SEND
                putExtra(Intent.EXTRA_STREAM, uri)
                type = "image/jpeg"
                flags = Intent.FLAG_GRANT_READ_URI_PERMISSION
            }

            val chooserIntent = Intent.createChooser(shareIntent, "分享截图").apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK
            }

            application.startActivity(chooserIntent)
        } catch (e: Exception) {
            Timber.e(e, "分享截图失败: ${e.message}")
        }
    }

    override fun onCleared() {
        super.onCleared()
        // 取消截图监听
        currentScreenshotsFlow?.cancel()
    }
}

/**
 * 截图页面UI状态
 */
data class ScreenshotsUiState(
    val isLoading: Boolean = true,
    val error: String? = null,
    val dateItems: List<DateItem> = emptyList(),
    val selectedDateIndex: Int = 0,
    val selectedDate: String = "",
    val formattedDate: String = "",
    val totalScreenshots: Int = 0,
    val duplicateCount: Int = 0, // 去重数量
    val allScreenshots: List<Screenshot> = emptyList() // 统一的截图列表，按时间降序排序
)

/**
 * 日期选择器项
 */
data class DateItem(
    val date: Date,
    val dayOfWeek: String,
    val day: String,
    val isToday: Boolean,
    val isTomorrow: Boolean
)