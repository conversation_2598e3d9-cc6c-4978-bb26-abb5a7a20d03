package com.shuyi.discipline.data.repository

import com.shuyi.discipline.data.model.MonitorStatistics
import com.shuyi.discipline.data.model.MonitorStatus
import com.shuyi.discipline.data.model.MonitorType
import com.shuyi.discipline.data.model.SystemMonitorRecord
import kotlinx.coroutines.flow.Flow

/**
 * 系统监控仓库接口
 */
interface SystemMonitorRepository {

    /**
     * 插入监控记录
     */
    suspend fun insertRecord(record: SystemMonitorRecord)

    /**
     * 更新监控记录
     */
    suspend fun updateRecord(record: SystemMonitorRecord)

    /**
     * 根据ID获取记录
     */
    suspend fun getRecordById(id: String): SystemMonitorRecord?

    /**
     * 根据类型获取记录
     */
    suspend fun getRecordsByType(type: MonitorType): List<SystemMonitorRecord>

    /**
     * 根据类型获取记录流
     */
    fun getRecordsByTypeFlow(type: MonitorType): Flow<List<SystemMonitorRecord>>

    /**
     * 根据类型和时间范围获取记录
     */
    suspend fun getRecordsByTypeAndTimeRange(
        type: MonitorType,
        startTime: Long,
        endTime: Long
    ): List<SystemMonitorRecord>

    /**
     * 获取最新的未完成记录
     */
    suspend fun getLatestUnfinishedRecord(
        type: MonitorType,
        status: MonitorStatus
    ): SystemMonitorRecord?

    /**
     * 完成记录
     */
    suspend fun finishRecord(
        id: String,
        endTime: Long,
        duration: Long,
        status: MonitorStatus
    )

    /**
     * 记录异常开始
     */
    suspend fun recordAbnormalStart(
        type: MonitorType,
        description: String,
        isDeviceShutdown: Boolean = false
    ): String

    /**
     * 记录异常结束
     */
    suspend fun recordAbnormalEnd(recordId: String)

    /**
     * 获取24小时内的监控统计
     */
    suspend fun get24HourStatistics(type: MonitorType): MonitorStatistics

    /**
     * 清理旧记录
     */
    suspend fun cleanOldRecords(cutoffTime: Long)

    /**
     * 删除所有记录
     */
    suspend fun deleteAllRecords()

    /**
     * 插入测试数据（仅用于演示）
     */
    suspend fun insertTestData()
}
