package com.shuyi.discipline.data.repository

import com.shuyi.discipline.data.model.CollageLayout
import com.shuyi.discipline.data.model.CollageSettings
import kotlinx.coroutines.flow.Flow

/**
 * 拼图设置仓库接口
 */
interface CollageSettingsRepository {
    
    /**
     * 获取拼图设置
     */
    fun getCollageSettings(): Flow<CollageSettings>
    
    /**
     * 获取拼图设置（同步）
     */
    suspend fun getCollageSettingsSync(): CollageSettings
    
    /**
     * 保存拼图设置
     */
    suspend fun saveCollageSettings(collageSettings: CollageSettings)
    
    /**
     * 更新自动拼图启用状态
     */
    suspend fun updateAutoCollageEnabled(enabled: Boolean)
    
    /**
     * 更新拼图时间
     */
    suspend fun updateCollageTime(hour: Int, minute: Int)
    
    /**
     * 更新拼图布局
     */
    suspend fun updateCollageLayout(layout: CollageLayout)
    
    /**
     * 更新拼图质量
     */
    suspend fun updateCollageQuality(quality: Int)

    /**
     * 更新拼图分辨率
     */
    suspend fun updateCollageResolution(resolution: Int)

    /**
     * 更新拼图高斯模糊
     */
    suspend fun updateCollageBlur(blur: Int)
    
    /**
     * 更新上次拼图时间
     */
    suspend fun updateLastCollageTime(time: Long)
}
