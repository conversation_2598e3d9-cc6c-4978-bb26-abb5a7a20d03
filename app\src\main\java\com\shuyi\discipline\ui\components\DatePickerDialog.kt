package com.shuyi.discipline.ui.components

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import java.text.SimpleDateFormat
import java.util.*

/**
 * 日期选择器对话框
 */
@Composable
fun DatePickerDialog(
    isVisible: Boolean,
    selectedDate: Date,
    onDateSelected: (Date) -> Unit,
    onDismiss: () -> Unit
) {
    if (isVisible) {
        var currentMonth by remember { mutableStateOf(Calendar.getInstance().apply { time = selectedDate }) }

        Dialog(onDismissRequest = onDismiss) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                shape = RoundedCornerShape(16.dp),
                elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    // 标题栏
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "选择日期",
                            style = MaterialTheme.typography.titleLarge,
                            fontWeight = FontWeight.Bold
                        )

                        IconButton(
                            onClick = onDismiss,
                            modifier = Modifier.size(24.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "关闭",
                                tint = MaterialTheme.colorScheme.onSurface
                            )
                        }
                    }

                    Spacer(modifier = Modifier.height(16.dp))

                    // 月份导航
                    MonthNavigationHeader(
                        currentMonth = currentMonth,
                        onPreviousMonth = {
                            currentMonth = Calendar.getInstance().apply {
                                time = currentMonth.time
                                add(Calendar.MONTH, -1)
                            }
                        },
                        onNextMonth = {
                            currentMonth = Calendar.getInstance().apply {
                                time = currentMonth.time
                                add(Calendar.MONTH, 1)
                            }
                        }
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // 星期标题
                    WeekDaysHeader()

                    Spacer(modifier = Modifier.height(8.dp))

                    // 日历网格
                    CalendarGrid(
                        currentMonth = currentMonth,
                        selectedDate = selectedDate,
                        onDateSelected = { date ->
                            onDateSelected(date)
                            onDismiss()
                        }
                    )
                }
            }
        }
    }
}

/**
 * 月份导航标题
 */
@Composable
private fun MonthNavigationHeader(
    currentMonth: Calendar,
    onPreviousMonth: () -> Unit,
    onNextMonth: () -> Unit
) {
    val monthYearFormat = SimpleDateFormat("yyyy年MM月", Locale.getDefault())

    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        IconButton(onClick = onPreviousMonth) {
            Icon(
                imageVector = Icons.Default.ArrowBack,
                contentDescription = "上个月",
                tint = MaterialTheme.colorScheme.onSurface
            )
        }

        Text(
            text = monthYearFormat.format(currentMonth.time),
            style = MaterialTheme.typography.titleMedium,
            fontWeight = FontWeight.SemiBold,
            color = MaterialTheme.colorScheme.onSurface
        )

        IconButton(onClick = onNextMonth) {
            Icon(
                imageVector = Icons.Default.ArrowForward,
                contentDescription = "下个月",
                tint = MaterialTheme.colorScheme.onSurface
            )
        }
    }
}

/**
 * 星期标题
 */
@Composable
private fun WeekDaysHeader() {
    val weekDays = listOf("日", "一", "二", "三", "四", "五", "六")

    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        weekDays.forEach { day ->
            Text(
                text = day,
                style = MaterialTheme.typography.bodySmall,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSecondaryContainer,
                textAlign = TextAlign.Center,
                modifier = Modifier.weight(1f)
            )
        }
    }
}

/**
 * 日历网格
 */
@Composable
private fun CalendarGrid(
    currentMonth: Calendar,
    selectedDate: Date,
    onDateSelected: (Date) -> Unit
) {
    val daysInMonth = generateCalendarDays(currentMonth)

    LazyVerticalGrid(
        columns = GridCells.Fixed(7),
        modifier = Modifier.height(240.dp),
        verticalArrangement = Arrangement.spacedBy(4.dp),
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        items(daysInMonth) { dayInfo ->
            CalendarDayItem(
                dayInfo = dayInfo,
                isSelected = isSameDay(dayInfo.date, selectedDate),
                onClick = { onDateSelected(dayInfo.date) }
            )
        }
    }
}

/**
 * 日历日期项
 */
@Composable
private fun CalendarDayItem(
    dayInfo: CalendarDayInfo,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val today = Calendar.getInstance().time
    val isToday = isSameDay(dayInfo.date, today)

    Box(
        modifier = Modifier
            .size(40.dp)
            .clip(CircleShape)
            .background(
                when {
                    isSelected -> MaterialTheme.colorScheme.primary
                    isToday -> MaterialTheme.colorScheme.primaryContainer
                    else -> MaterialTheme.colorScheme.surface
                }
            )
            .clickable(enabled = dayInfo.isCurrentMonth, onClick = onClick),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = dayInfo.day.toString(),
            style = MaterialTheme.typography.bodyMedium,
            fontWeight = if (isToday || isSelected) FontWeight.Bold else FontWeight.Normal,
            color = when {
                isSelected -> MaterialTheme.colorScheme.onPrimary
                isToday -> MaterialTheme.colorScheme.onPrimaryContainer
                !dayInfo.isCurrentMonth -> MaterialTheme.colorScheme.onSurface.copy(alpha = 0.3f)
                else -> MaterialTheme.colorScheme.onSurface
            }
        )
    }
}

/**
 * 日历日期信息
 */
private data class CalendarDayInfo(
    val date: Date,
    val day: Int,
    val isCurrentMonth: Boolean
)

/**
 * 生成日历天数
 */
private fun generateCalendarDays(currentMonth: Calendar): List<CalendarDayInfo> {
    val calendar = Calendar.getInstance()
    calendar.time = currentMonth.time

    // 设置为当月第一天
    calendar.set(Calendar.DAY_OF_MONTH, 1)
    val firstDayOfMonth = calendar.clone() as Calendar

    // 设置为当月最后一天
    calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
    val lastDayOfMonth = calendar.clone() as Calendar

    val days = mutableListOf<CalendarDayInfo>()

    // 添加上个月的尾部天数（填充第一周）
    val firstDayOfWeek = firstDayOfMonth.get(Calendar.DAY_OF_WEEK) - 1 // 0=周日
    if (firstDayOfWeek > 0) {
        val prevMonth = firstDayOfMonth.clone() as Calendar
        prevMonth.add(Calendar.MONTH, -1)
        val daysInPrevMonth = prevMonth.getActualMaximum(Calendar.DAY_OF_MONTH)

        for (i in firstDayOfWeek - 1 downTo 0) {
            val day = daysInPrevMonth - i
            prevMonth.set(Calendar.DAY_OF_MONTH, day)
            days.add(
                CalendarDayInfo(
                    date = prevMonth.time,
                    day = day,
                    isCurrentMonth = false
                )
            )
        }
    }

    // 添加当月的所有天数
    val daysInCurrentMonth = currentMonth.getActualMaximum(Calendar.DAY_OF_MONTH)
    for (day in 1..daysInCurrentMonth) {
        calendar.set(Calendar.DAY_OF_MONTH, day)
        days.add(
            CalendarDayInfo(
                date = calendar.time,
                day = day,
                isCurrentMonth = true
            )
        )
    }

    // 添加下个月的开头天数（填充最后一周）
    val remainingDays = 42 - days.size // 6周 * 7天 = 42天
    if (remainingDays > 0) {
        val nextMonth = lastDayOfMonth.clone() as Calendar
        nextMonth.add(Calendar.MONTH, 1)

        for (day in 1..remainingDays) {
            nextMonth.set(Calendar.DAY_OF_MONTH, day)
            days.add(
                CalendarDayInfo(
                    date = nextMonth.time,
                    day = day,
                    isCurrentMonth = false
                )
            )
        }
    }

    return days
}

/**
 * 判断两个日期是否是同一天
 */
private fun isSameDay(date1: Date, date2: Date): Boolean {
    val cal1 = Calendar.getInstance().apply { time = date1 }
    val cal2 = Calendar.getInstance().apply { time = date2 }
    return cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR) &&
           cal1.get(Calendar.DAY_OF_YEAR) == cal2.get(Calendar.DAY_OF_YEAR)
}
