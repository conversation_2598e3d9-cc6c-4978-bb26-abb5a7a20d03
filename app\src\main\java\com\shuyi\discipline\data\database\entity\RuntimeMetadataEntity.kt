package com.shuyi.discipline.data.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 运行时元数据实体
 * 存储监控系统的配置和状态信息
 */
@Entity(tableName = "runtime_metadata")
data class RuntimeMetadataEntity(
    @PrimaryKey
    @ColumnInfo(name = "key")
    val key: String,

    @ColumnInfo(name = "value")
    val value: String,

    @ColumnInfo(name = "updated_at")
    val updatedAt: Long = System.currentTimeMillis()
) {
    companion object {
        // 元数据键定义
        const val KEY_LAST_PROCESSED_EXIT_TIME = "last_processed_exit_time"
        const val KEY_LAST_RECORDED_EXIT_TIME = "last_recorded_exit_time"
        const val KEY_CURRENT_SESSION_ID = "current_session_id"
        const val KEY_MONITORING_VERSION = "monitoring_version"
        const val KEY_LAST_CLEANUP_TIME = "last_cleanup_time"
    }
}
