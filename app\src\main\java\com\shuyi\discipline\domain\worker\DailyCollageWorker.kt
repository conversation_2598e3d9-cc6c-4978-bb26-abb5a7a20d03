package com.shuyi.discipline.domain.worker

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.work.CoroutineWorker
import androidx.work.ForegroundInfo
import androidx.work.WorkerParameters
import com.shuyi.discipline.MainActivity
import com.shuyi.discipline.R
import com.shuyi.discipline.data.repository.CollageReportRepository
import com.shuyi.discipline.data.repository.impl.CollageReportRepositoryImpl
import com.shuyi.discipline.data.repository.impl.ScreenshotRepositoryImpl
import com.shuyi.discipline.data.source.database.AppDatabase
import timber.log.Timber

/**
 * 每日拼图生成工作器
 */
class DailyCollageWorker(
    private val context: Context,
    workerParams: WorkerParameters
) : CoroutineWorker(context, workerParams) {
    
    private val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
    
    override suspend fun doWork(): Result {
        setForeground(createForegroundInfo("正在生成每日拼图..."))
        
        try {
            // 获取应用数据库
            val database = AppDatabase.getInstance(context)
            
            // 初始化仓库
            val screenshotRepository = ScreenshotRepositoryImpl(context, database.screenshotDao())
            val collageReportRepository = CollageReportRepositoryImpl(
                context,
                database.collageReportDao(),
                screenshotRepository
            )
            
            // 生成拼图
            collageReportRepository.generateDailyCollage()
                .onSuccess { report ->
                    Timber.d("拼图生成成功: ${report.date}")
                    
                    // 显示成功通知
                    showResultNotification(
                        "每日拼图已生成",
                        "点击查看今日活动摘要"
                    )
                    
                    return Result.success()
                }
                .onFailure { error ->
                    Timber.e(error, "拼图生成失败")
                    
                    // 显示失败通知
                    showResultNotification(
                        "每日拼图生成失败",
                        error.message ?: "未知错误"
                    )
                    
                    return Result.failure()
                }
            
            return Result.success()
        } catch (e: Exception) {
            Timber.e(e, "拼图生成过程出错")
            return Result.failure()
        }
    }
    
    private fun createForegroundInfo(progress: String): ForegroundInfo {
        // 创建通知渠道
        createNotificationChannel()
        
        // 创建Intent
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent, PendingIntent.FLAG_IMMUTABLE
        )
        
        // 创建通知
        val notification = NotificationCompat.Builder(context, CHANNEL_ID)
            .setContentTitle("正在生成拼图")
            .setContentText(progress)
            .setSmallIcon(android.R.drawable.ic_menu_gallery)
            .setOngoing(true)
            .setContentIntent(pendingIntent)
            .build()
        
        return ForegroundInfo(NOTIFICATION_ID, notification)
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "拼图生成服务",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "显示拼图生成进度和结果"
            }
            
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun showResultNotification(title: String, content: String) {
        // 创建Intent
        val intent = Intent(context, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            context, 0, intent, PendingIntent.FLAG_IMMUTABLE
        )
        
        // 创建通知
        val notification = NotificationCompat.Builder(context, CHANNEL_ID)
            .setContentTitle(title)
            .setContentText(content)
            .setSmallIcon(android.R.drawable.ic_menu_gallery)
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
            .build()
        
        // 显示通知
        notificationManager.notify(RESULT_NOTIFICATION_ID, notification)
    }
    
    companion object {
        private const val CHANNEL_ID = "collage_generation_channel"
        private const val NOTIFICATION_ID = 2001
        private const val RESULT_NOTIFICATION_ID = 2002
    }
} 