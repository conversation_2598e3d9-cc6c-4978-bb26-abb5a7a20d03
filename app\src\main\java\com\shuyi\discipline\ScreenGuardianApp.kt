package com.shuyi.discipline

import android.app.Application
import android.content.Context
import androidx.hilt.work.HiltWorkerFactory
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ProcessLifecycleOwner
import androidx.work.Configuration
import com.shuyi.discipline.data.model.MonitorStatus
import com.shuyi.discipline.data.model.MonitorType
import com.shuyi.discipline.data.repository.SystemMonitorRepository
import com.shuyi.discipline.di.ApplicationScope
import com.shuyi.discipline.domain.monitor.AppRuntimeMonitorManager
import com.shuyi.discipline.domain.monitor.PersistentAppStateManager
import com.shuyi.discipline.domain.usecase.ScheduleSystemMonitorUseCase
import dagger.hilt.android.HiltAndroidApp
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * 应用主类
 */
@HiltAndroidApp
class ScreenGuardianApp : Application(), Configuration.Provider, DefaultLifecycleObserver {

    @Inject
    lateinit var scheduleSystemMonitorUseCase: ScheduleSystemMonitorUseCase

    @Inject
    lateinit var systemMonitorRepository: SystemMonitorRepository

    @Inject
    lateinit var appRuntimeMonitorManager: AppRuntimeMonitorManager

    @Inject
    lateinit var persistentAppStateManager: PersistentAppStateManager

    @Inject
    @ApplicationScope
    lateinit var applicationScope: CoroutineScope

    @Inject
    lateinit var workerFactory: HiltWorkerFactory

    // 跟踪应用是否在前台（用于区分主动后台和完全退出）
    private var isAppInForeground = false

    override fun onCreate() {
        super<Application>.onCreate()

        // 🎯 第一件事：记录应用启动时间到持久化状态管理器
        persistentAppStateManager.recordAppStart()

        Timber.d("🚀 Application.onCreate() 被调用")

        // 初始化Timber日志库
        if (isDebugBuild()) {
            Timber.plant(Timber.DebugTree())
            Timber.d("Timber 调试日志已启用")
        } else {
            // 即使在发布版本也启用日志，用于调试监控系统
            Timber.plant(Timber.DebugTree())
            Timber.d("Timber 日志已启用（发布版本）")
        }

        // 🎯 初始化服务状态，避免首次启动时的误判
        initializeServiceStatus()

        // 注册生命周期监听器
        ProcessLifecycleOwner.get().lifecycle.addObserver(this)
        Timber.d("已注册应用生命周期监听器")

        // 启动应用运行时监控
        appRuntimeMonitorManager.setRealAppStartTime(System.currentTimeMillis())
        appRuntimeMonitorManager.startMonitoring()

        // 启动系统监控（定期检查）
        scheduleSystemMonitorUseCase.startMonitoring()

        // 记录应用启动时间（保留原有逻辑作为备份）
        recordAppStartTime()
    }

    /**
     * 判断是否为调试构建
     */
    private fun isDebugBuild(): Boolean {
        return try {
            // 使用反射访问BuildConfig，避免直接引用可能不存在的类
            val buildConfigClass = Class.forName("${packageName}.BuildConfig")
            val debugField = buildConfigClass.getField("DEBUG")
            debugField.getBoolean(null)
        } catch (e: Exception) {
            // 无法确定，默认为非调试模式
            false
        }
    }

    /**
     * 应用进入前台
     */
    override fun onStart(owner: LifecycleOwner) {
        super<DefaultLifecycleObserver>.onStart(owner)
        isAppInForeground = true

        // 🎯 记录应用进入前台时间到持久化状态管理器
        persistentAppStateManager.recordAppForeground()

        // 🎯 记录真实的应用启动时间（用户实际操作的时间）
        val realStartTime = System.currentTimeMillis()
        Timber.d("🎯 应用进入前台，记录真实启动时间: ${java.util.Date(realStartTime)}")

        // 🎯 立即设置真实的应用启动时间
        appRuntimeMonitorManager.setRealAppStartTime(realStartTime)

        // 通知新监控系统应用进入前台
        appRuntimeMonitorManager.onAppForeground()

        // 保持原有逻辑作为备份
        applicationScope.launch {
            try {
                // 延迟500ms确保应用完全进入前台状态
                kotlinx.coroutines.delay(500)

                // 检查应用是否异常终止，如果是则补充记录未运行时长
                checkAndHandleAppTermination()

                scheduleSystemMonitorUseCase.executeImmediateCheck()
                Timber.d("应用进入前台，已检查异常终止并执行监控检查")
            } catch (e: Exception) {
                Timber.e(e, "应用进入前台处理失败")
            }
        }
    }

    /**
     * 应用进入后台
     */
    override fun onStop(owner: LifecycleOwner) {
        super<DefaultLifecycleObserver>.onStop(owner)
        isAppInForeground = false

        // 🎯 记录应用进入后台时间到持久化状态管理器
        persistentAppStateManager.recordAppBackground()

        Timber.d("应用进入后台（正常的后台状态，不记录为未运行异常）")

        // 通知新监控系统应用进入后台
        appRuntimeMonitorManager.onAppBackground()

        // 记录应用进入后台的时间，用于后续检测异常终止（保留原有逻辑作为备份）
        recordAppBackgroundTime()

        // 重要：应用进入后台是正常状态，不应该立即记录为"未运行异常"
        // 只有当应用进程真正停止时，才应该记录为异常
        // 这个判断完全由新的监控系统处理
    }

    /**
     * 记录应用进入后台的时间
     */
    private fun recordAppBackgroundTime() {
        try {
            val sharedPrefs = getSharedPreferences("app_lifecycle", MODE_PRIVATE)
            val currentTime = System.currentTimeMillis()
            sharedPrefs.edit()
                .putLong("last_background_time", currentTime)
                .apply()
            Timber.d("记录应用进入后台时间: ${java.util.Date(currentTime)}")
        } catch (e: Exception) {
            Timber.e(e, "记录应用后台时间失败")
        }
    }

    /**
     * 检查并处理应用异常终止的情况
     * 当应用重新启动时，检查是否有未完成的异常记录，如果有则说明应用被强制终止了
     */
    private suspend fun checkAndHandleAppTermination() {
        try {
            // 🎯 处理App后台运行异常记录
            val latestAppRecord = systemMonitorRepository.getLatestUnfinishedRecord(
                MonitorType.APP_BACKGROUND_RUNNING,
                MonitorStatus.ABNORMAL
            )

            latestAppRecord?.let { record ->
                // 如果有未完成的异常记录，说明应用在上次运行期间被强制终止了
                // 需要将这个记录标记为已恢复，并记录终止到重启之间的时长
                val currentTime = System.currentTimeMillis()
                val duration = currentTime - record.startTime

                systemMonitorRepository.finishRecord(
                    record.id,
                    currentTime,
                    duration,
                    MonitorStatus.RECOVERED
                )

                Timber.d("检测到应用异常终止，已补充记录未运行时长: ${duration}ms (${duration / 1000}秒)")
                Timber.d("异常开始时间: ${java.util.Date(record.startTime)}")
                Timber.d("应用重启时间: ${java.util.Date(currentTime)}")
            }

            // 🎯 处理截图服务异常记录
            val latestScreenshotRecord = systemMonitorRepository.getLatestUnfinishedRecord(
                MonitorType.SCREENSHOT_FUNCTION,
                MonitorStatus.ABNORMAL
            )

            latestScreenshotRecord?.let { record ->
                // 如果有未完成的截图服务异常记录，需要正确处理
                // 结束上一次的记录，记录从上次开始到app关闭的时长
                val currentTime = System.currentTimeMillis()

                // 🎯 关键修复：获取上次app关闭的时间
                val lastBackgroundTime = getSharedPreferences("app_lifecycle", MODE_PRIVATE)
                    .getLong("last_background_time", 0)

                val endTime = if (lastBackgroundTime > record.startTime) {
                    // 如果有记录的后台时间，使用后台时间作为结束时间
                    lastBackgroundTime
                } else {
                    // 否则使用当前时间（可能是异常终止）
                    currentTime
                }

                val duration = endTime - record.startTime

                systemMonitorRepository.finishRecord(
                    record.id,
                    endTime,
                    duration,
                    MonitorStatus.RECOVERED
                )

                Timber.d("检测到截图服务异常记录未完成，已补充记录: ${duration}ms (${duration / 1000}秒)")
                Timber.d("异常开始时间: ${java.util.Date(record.startTime)}")
                Timber.d("记录结束时间: ${java.util.Date(endTime)}")
            }

            // 如果没有未完成的App异常记录，检查是否有应用后台时间记录
            if (latestAppRecord == null) {
                checkAndRecordBackgroundTermination()
            }
        } catch (e: Exception) {
            Timber.e(e, "检查应用异常终止状态失败")
        }
    }

    /**
     * 检查并记录后台终止的情况
     */
    private suspend fun checkAndRecordBackgroundTermination() {
        try {
            val sharedPrefs = getSharedPreferences("app_lifecycle", MODE_PRIVATE)
            val lastBackgroundTime = sharedPrefs.getLong("last_background_time", 0)

            if (lastBackgroundTime > 0) {
                val currentTime = System.currentTimeMillis()
                val backgroundDuration = currentTime - lastBackgroundTime

                // 如果应用在后台超过5分钟，认为可能被系统终止了，需要记录这段时间
                if (backgroundDuration > 5 * 60 * 1000L) { // 5分钟
                    // 🎯 检查PersistentAppStateManager是否已经处理过这个时间点，避免重复记录
                    // 注意：这里我们不能直接调用PersistentAppStateManager的私有方法，
                    // 但可以通过检查其状态来避免重复

                    // 先让PersistentAppStateManager处理，如果它已经处理过，就不再重复记录
                    val wasAlreadyProcessed = try {
                        // 通过检查PersistentAppStateManager的状态来判断是否已处理
                        val persistentPrefs = getSharedPreferences("app_state_manager", MODE_PRIVATE)
                        val processedTimes = persistentPrefs.getStringSet("processed_exit_times", emptySet()) ?: emptySet()
                        processedTimes.contains(lastBackgroundTime.toString())
                    } catch (e: Exception) {
                        false
                    }

                    if (wasAlreadyProcessed) {
                        Timber.d("🔄 [ScreenGuardianApp] 后台终止时间已被其他监控系统处理，跳过重复记录: ${java.util.Date(lastBackgroundTime)}")
                    } else {
                        val recordId = systemMonitorRepository.recordAbnormalStart(
                            MonitorType.APP_BACKGROUND_RUNNING,
                            "应用在后台被系统终止",
                            false
                        )

                        // 立即结束这个记录，记录从后台开始到现在的时长
                        systemMonitorRepository.finishRecord(
                            recordId,
                            currentTime,
                            backgroundDuration,
                            MonitorStatus.RECOVERED
                        )

                        Timber.d("检测到应用后台终止，已记录未运行时长: ${backgroundDuration}ms (${backgroundDuration / 1000}秒)")
                        Timber.d("后台开始时间: ${java.util.Date(lastBackgroundTime)}")
                        Timber.d("应用重启时间: ${java.util.Date(currentTime)}")

                        // 🎯 标记这个时间已处理，避免其他系统重复记录
                        try {
                            val persistentPrefs = getSharedPreferences("app_state_manager", MODE_PRIVATE)
                            val processedTimes = persistentPrefs.getStringSet("processed_exit_times", emptySet())?.toMutableSet() ?: mutableSetOf()
                            processedTimes.add(lastBackgroundTime.toString())

                            // 只保留最近24小时的记录
                            val cutoffTime = System.currentTimeMillis() - 24 * 60 * 60 * 1000L
                            processedTimes.removeAll { it.toLongOrNull()?.let { time -> time < cutoffTime } == true }

                            persistentPrefs.edit()
                                .putStringSet("processed_exit_times", processedTimes)
                                .apply()
                        } catch (e: Exception) {
                            Timber.w(e, "标记处理时间失败")
                        }
                    }
                } else {
                    Timber.d("应用后台时间较短(${backgroundDuration}ms)，认为是正常重启")
                }

                // 清除后台时间记录
                sharedPrefs.edit().remove("last_background_time").apply()
            }
        } catch (e: Exception) {
            Timber.e(e, "检查后台终止状态失败")
        }
    }

    /**
     * 配置WorkManager
     */
    override val workManagerConfiguration: Configuration
        get() = Configuration.Builder()
            .setMinimumLoggingLevel(android.util.Log.INFO)
            .setWorkerFactory(workerFactory)
            .build()

    /**
     * 初始化服务状态
     * 避免首次启动时因为没有状态数据而误判服务异常
     */
    private fun initializeServiceStatus() {
        try {
            val sharedPrefs = getSharedPreferences("service_status", Context.MODE_PRIVATE)
            val isInitialized = sharedPrefs.getBoolean("status_initialized", false)

            if (!isInitialized) {
                Timber.d("首次启动，初始化服务状态")

                // 🎯 关键修改：检查数据库中的实际截图功能状态
                applicationScope.launch {
                    try {
                        val database = com.shuyi.discipline.data.source.database.AppDatabase.getInstance(this@ScreenGuardianApp)
                        val scheduleRule = database.scheduleRuleDao().getScheduleRule()
                        val isScreenshotEnabled = scheduleRule?.isEnabled ?: false

                        // 根据实际状态设置服务运行状态
                        sharedPrefs.edit()
                            .putBoolean("status_initialized", true)
                            .putBoolean("screenshot_service_running", isScreenshotEnabled)
                            .putLong("last_update_time", System.currentTimeMillis())
                            .putLong("app_first_launch_time", System.currentTimeMillis())
                            .apply()

                        Timber.d("服务状态初始化完成，截图功能状态: $isScreenshotEnabled")
                    } catch (e: Exception) {
                        Timber.e(e, "检查截图功能状态失败，使用默认值")
                        // 降级处理：如果无法获取状态，默认为未启用
                        sharedPrefs.edit()
                            .putBoolean("status_initialized", true)
                            .putBoolean("screenshot_service_running", false)
                            .putLong("last_update_time", System.currentTimeMillis())
                            .putLong("app_first_launch_time", System.currentTimeMillis())
                            .apply()

                        Timber.d("服务状态初始化完成（降级处理），截图功能状态: false")
                    }
                }
            } else {
                Timber.d("服务状态已初始化，跳过")
            }
        } catch (e: Exception) {
            Timber.e(e, "初始化服务状态失败")
        }
    }

    /**
     * 记录应用启动时间
     */
    private fun recordAppStartTime() {
        try {
            val sharedPrefs = getSharedPreferences("app_lifecycle", MODE_PRIVATE)
            val currentTime = System.currentTimeMillis()
            sharedPrefs.edit()
                .putLong("app_first_launch_time", currentTime)
                .apply()
            Timber.d("记录应用启动时间: ${java.util.Date(currentTime)}")
        } catch (e: Exception) {
            Timber.e(e, "记录应用启动时间失败")
        }
    }
}