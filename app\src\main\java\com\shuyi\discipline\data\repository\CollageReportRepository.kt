package com.shuyi.discipline.data.repository

import com.shuyi.discipline.data.model.CollageReport
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * 拼图报告仓库接口
 */
interface CollageReportRepository {

    /**
     * 获取所有拼图报告
     */
    fun getAllCollageReports(): Flow<List<CollageReport>>

    /**
     * 保存拼图报告
     */
    suspend fun saveCollageReport(collageReport: CollageReport)

    /**
     * 获取特定日期的拼图报告
     * @param date 日期字符串，格式为"yyyy-MM-dd"
     */
    suspend fun getCollageReportForDate(date: String): CollageReport?

    /**
     * 获取特定日期的拼图报告流
     * @param date 日期字符串，格式为"yyyy-MM-dd"
     */
    fun getCollageReportForDateFlow(date: String): Flow<CollageReport?>

    /**
     * 删除旧的拼图报告
     * @param daysToKeep 保留的天数
     */
    suspend fun deleteOldCollageReports(daysToKeep: Int)

    /**
     * 删除指定日期的拼图报告
     * @param date 日期字符串，格式为"yyyy-MM-dd"
     * @return 删除的记录数
     */
    suspend fun deleteCollageReportsForDate(date: String): Int

    /**
     * 生成当日拼图
     */
    suspend fun generateDailyCollage(): Result<CollageReport>
}