package com.shuyi.discipline.ui.screen.quietperiod

import android.app.Application
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.shuyi.discipline.data.repository.impl.QuietPeriodRepositoryImpl
import com.shuyi.discipline.data.source.database.AppDatabase

/**
 * 免打扰时段ViewModel工厂
 */
class QuietPeriodViewModelFactory(private val application: Application) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(QuietPeriodViewModel::class.java)) {
            val database = AppDatabase.getInstance(application)
            val repository = QuietPeriodRepositoryImpl(database.quietPeriodDao())
            @Suppress("UNCHECKED_CAST")
            return QuietPeriodViewModel(repository) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
} 