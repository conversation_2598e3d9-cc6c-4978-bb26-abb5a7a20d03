package com.shuyi.discipline.ui.screen.config

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Image
import androidx.compose.material.icons.filled.Dashboard
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Slider
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.hilt.navigation.compose.hiltViewModel
import com.shuyi.discipline.data.model.CollageLayout
import com.shuyi.discipline.data.model.CollageQuality
import com.shuyi.discipline.data.model.CollageSettings
import com.shuyi.discipline.data.model.NotificationSettings
import com.shuyi.discipline.data.model.ThemeSettings
import com.shuyi.discipline.ui.components.BottomNavBar
import com.shuyi.discipline.ui.components.PageTitleBar
import com.shuyi.discipline.ui.components.SettingsCard
import com.shuyi.discipline.ui.components.SettingsNavigationItem
import com.shuyi.discipline.ui.components.SettingsSwitchItem
import com.shuyi.discipline.ui.components.StatusBar
import com.shuyi.discipline.ui.components.TimePickerDialog
import com.shuyi.discipline.ui.model.UiState
import com.shuyi.discipline.ui.navigation.Screen
import android.app.Application
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.ExposedDropdownMenuDefaults
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.TextButton
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.platform.LocalContext
import android.os.Build
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Locale
import kotlin.math.roundToInt

/**
 * 配置屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ConfigScreen(
    onNavigateBack: () -> Unit,
    onNavigateToHome: () -> Unit = {},
    onNavigateToScreenshots: () -> Unit = {},
    onNavigateToReports: () -> Unit = {},
    viewModel: ConfigViewModel = viewModel(),
    themeSettingsViewModel: ThemeSettingsViewModel = hiltViewModel(),
    navController: androidx.navigation.NavController? = null
) {
    val uiState by viewModel.scheduleRule.collectAsState()
    val themeSettingsState by themeSettingsViewModel.themeSettings.collectAsState()
    val scrollState = rememberScrollState()
    var selectedTabIndex by remember { mutableStateOf(3) } // 设置页面是第4个选项卡

    LaunchedEffect(key1 = Unit) {
        viewModel.loadScheduleRule()
    }

    Scaffold(
        containerColor = MaterialTheme.colorScheme.background,
        bottomBar = {
            BottomNavBar(
                selectedIndex = 4, // 固定为设置标签
                onItemSelected = { index ->
                    selectedTabIndex = index
                    // 根据选中的标签执行相应的导航
                    when(index) {
                        0 -> onNavigateToHome()
                        1 -> onNavigateToScreenshots()
                        2 -> onNavigateToReports() // 导航到拼图页面
                        3 -> navController?.navigate(Screen.Status.route) {
                            // 避免创建多个实例
                            launchSingleTop = true
                        }
                        // 4是设置页面，已经在此页面，无需导航
                    }
                },
                onNavigateToHome = onNavigateToHome,
                onNavigateToScreenshots = onNavigateToScreenshots,
                onNavigateToCollages = onNavigateToReports,
                onNavigateToStatus = {
                    navController?.navigate(Screen.Status.route) {
                        // 避免创建多个实例
                        launchSingleTop = true
                    }
                },
                onNavigateToConfig = { /* 已在设置页面，无需导航 */ }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(MaterialTheme.colorScheme.background)
        ) {
            // 内容区域 - 直接从顶部开始，不再使用StatusBar
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp, vertical = 8.dp) // 减少顶部padding
                    .verticalScroll(scrollState),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 标题
                PageTitleBar(
                    title = "设置",
                    showBackButton = false,
                    onBackClick = {}
                )

                // 主题设置卡片 - 放在最顶部
                when (themeSettingsState) {
                    is UiState.Success -> {
                        val themeSettings = (themeSettingsState as UiState.Success<ThemeSettings>).data

                        SettingsCard(title = "外观设置") {
                            Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                                // 主题模式选择
                                Text(
                                    text = "主题模式",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSecondaryContainer,
                                    modifier = Modifier.padding(bottom = 4.dp)
                                )

                                var expanded by remember { mutableStateOf(false) }

                                Box {
                                    Card(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .clickable { expanded = true },
                                        shape = RoundedCornerShape(8.dp),
                                        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface)
                                    ) {
                                        Text(
                                            text = themeSettings.themeMode.displayName,
                                            modifier = Modifier.padding(16.dp),
                                            color = MaterialTheme.colorScheme.onSurface
                                        )
                                    }

                                    DropdownMenu(
                                        expanded = expanded,
                                        onDismissRequest = { expanded = false }
                                    ) {
                                        themeSettingsViewModel.themeModeOptions.forEach { option ->
                                            DropdownMenuItem(
                                                text = { Text(option) },
                                                onClick = {
                                                    themeSettingsViewModel.updateThemeMode(option)
                                                    expanded = false
                                                }
                                            )
                                        }
                                    }
                                }

                                // 动态颜色开关（仅在 Android 12+ 显示）
                                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                                    SettingsSwitchItem(
                                        title = "动态颜色",
                                        description = "根据壁纸自动调整应用颜色",
                                        checked = themeSettings.isDynamicColorEnabled,
                                        onCheckedChange = { themeSettingsViewModel.updateDynamicColorEnabled(it) }
                                    )
                                }

                                // 字体大小设置
                                SettingsNavigationItem(
                                    title = "字体大小",
                                    description = "当前: ${themeSettings.fontSize.displayName}",
                                    onClick = {
                                        navController?.navigate(com.shuyi.discipline.ui.navigation.Screen.FontSize.route)
                                    }
                                )
                            }
                        }
                    }
                    is UiState.Loading -> {
                        SettingsCard(title = "外观设置") {
                            Text("加载中...")
                        }
                    }
                    is UiState.Error -> {
                        SettingsCard(title = "外观设置") {
                            Text("加载错误: ${(themeSettingsState as UiState.Error).message}")
                        }
                    }
                    else -> {
                        // 其他状态
                    }
                }

            when (uiState) {
                is UiState.Loading -> {
                    Text("加载中...")
                }
                is UiState.Error -> {
                    Text("加载错误: ${(uiState as UiState.Error).message}")
                }
                is UiState.Success<*> -> {
                    val rule = (uiState as UiState.Success<ScheduleRuleUi>).data

                    // 截图设置卡片
                    SettingsCard(title = "截图设置") {
                        Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                            // 截图间隔时间标题和完整时间显示
                            var minutesSliderValue by remember { mutableFloatStateOf(rule.intervalMinutes.toFloat()) }
                            var secondsSliderValue by remember { mutableFloatStateOf(rule.intervalSeconds.toFloat()) }
                            var showIntervalEditDialog by remember { mutableStateOf(false) }

                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween,
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Text(
                                    text = "截屏间隔时间",
                                    style = MaterialTheme.typography.bodyMedium,
                                    color = MaterialTheme.colorScheme.onSecondaryContainer
                                )

                                // 完整时间显示
                                Text(
                                    text = "${minutesSliderValue.toInt()}分${secondsSliderValue.toInt()}秒",
                                    style = MaterialTheme.typography.bodyLarge.copy(
                                        fontWeight = FontWeight.Bold,
                                        fontSize = 18.sp
                                    ),
                                    color = MaterialTheme.colorScheme.onSurface,
                                    modifier = Modifier.clickable {
                                        showIntervalEditDialog = true
                                    }
                                )
                            }

                            Spacer(modifier = Modifier.height(8.dp))

                            // 分钟选择器
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                // 当秒数为0时，分钟最小为1；当秒数不为0时，分钟可以为0
                                val minMinutes = if (secondsSliderValue.toInt() == 0) 1f else 0f
                                val adjustedMinutesValue = if (minutesSliderValue < minMinutes) minMinutes else minutesSliderValue

                                Slider(
                                    value = adjustedMinutesValue,
                                    onValueChange = { newValue ->
                                        val validValue = if (newValue < minMinutes) minMinutes else newValue
                                        minutesSliderValue = validValue
                                    },
                                    onValueChangeFinished = {
                                        val validValue = if (minutesSliderValue < minMinutes) minMinutes else minutesSliderValue
                                        minutesSliderValue = validValue
                                        viewModel.updateIntervalMinutes(minutesSliderValue.toInt())
                                    },
                                    valueRange = minMinutes..60f,
                                    steps = (60 - minMinutes).toInt(),
                                    modifier = Modifier.weight(1f)
                                )

                                Text(
                                    text = "${minutesSliderValue.toInt()}分钟",
                                    style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
                                    modifier = Modifier.padding(start = 8.dp)
                                )
                            }

                            // 秒数选择器
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Slider(
                                    value = secondsSliderValue,
                                    onValueChange = { newValue ->
                                        // 如果分钟为0且秒数也要设为0，则不允许
                                        if (minutesSliderValue.toInt() == 0 && newValue.toInt() == 0) {
                                            // 保持秒数至少为1
                                            secondsSliderValue = 1f
                                        } else {
                                            secondsSliderValue = newValue
                                        }
                                    },
                                    onValueChangeFinished = {
                                        // 确保分钟和秒数不能同时为0
                                        if (minutesSliderValue.toInt() == 0 && secondsSliderValue.toInt() == 0) {
                                            secondsSliderValue = 1f
                                        }
                                        viewModel.updateIntervalSeconds(secondsSliderValue.toInt())
                                    },
                                    valueRange = 0f..59f,
                                    steps = 59,
                                    modifier = Modifier.weight(1f)
                                )

                                Text(
                                    text = "${secondsSliderValue.toInt()}秒",
                                    style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
                                    modifier = Modifier.padding(start = 8.dp)
                                )
                            }

                            // 随机间隔
                            SettingsSwitchItem(
                                title = "随机间隔时间",
                                description = "在设定时间基础上随机增减",
                                checked = rule.isRandom,
                                onCheckedChange = { viewModel.updateIsRandom(it) }
                            )

                            // 随机范围
                            if (rule.isRandom) {
                                // 随机范围时间标题和完整时间显示
                                var randomRangeMinutesSliderValue by remember { mutableFloatStateOf(rule.randomRangeMinutes.toFloat()) }
                                var randomRangeSecondsSliderValue by remember { mutableFloatStateOf(rule.randomRangeSeconds.toFloat()) }
                                var showRandomRangeEditDialog by remember { mutableStateOf(false) }

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween,
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Text(
                                        text = "随机范围时间",
                                        style = MaterialTheme.typography.bodyMedium,
                                        color = MaterialTheme.colorScheme.onSecondaryContainer
                                    )

                                    // 完整时间显示
                                    Text(
                                        text = "±${randomRangeMinutesSliderValue.toInt()}分${randomRangeSecondsSliderValue.toInt()}秒",
                                        style = MaterialTheme.typography.bodyLarge.copy(
                                            fontWeight = FontWeight.Bold,
                                            fontSize = 18.sp
                                        ),
                                        color = MaterialTheme.colorScheme.onSurface,
                                        modifier = Modifier.clickable {
                                            showRandomRangeEditDialog = true
                                        }
                                    )
                                }

                                Spacer(modifier = Modifier.height(8.dp))

                                // 分钟选择器
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Slider(
                                        value = randomRangeMinutesSliderValue,
                                        onValueChange = { newValue ->
                                            randomRangeMinutesSliderValue = newValue
                                        },
                                        onValueChangeFinished = {
                                            viewModel.updateRandomRangeMinutes(randomRangeMinutesSliderValue.toInt())
                                        },
                                        valueRange = 0f..60f,
                                        steps = 60,
                                        modifier = Modifier.weight(1f)
                                    )

                                    Text(
                                        text = "${randomRangeMinutesSliderValue.toInt()}分钟",
                                        style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
                                        modifier = Modifier.padding(start = 8.dp)
                                    )
                                }

                                // 秒数选择器
                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    verticalAlignment = Alignment.CenterVertically
                                ) {
                                    Slider(
                                        value = randomRangeSecondsSliderValue,
                                        onValueChange = { newValue ->
                                            randomRangeSecondsSliderValue = newValue
                                        },
                                        onValueChangeFinished = {
                                            viewModel.updateRandomRangeSeconds(randomRangeSecondsSliderValue.toInt())
                                        },
                                        valueRange = 0f..59f,
                                        steps = 59,
                                        modifier = Modifier.weight(1f)
                                    )

                                    Text(
                                        text = "${randomRangeSecondsSliderValue.toInt()}秒",
                                        style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
                                        modifier = Modifier.padding(start = 8.dp)
                                    )
                                }

                                // 随机范围编辑对话框
                                if (showRandomRangeEditDialog) {
                                    RandomRangeEditDialog(
                                        currentMinutes = randomRangeMinutesSliderValue.toInt(),
                                        currentSeconds = randomRangeSecondsSliderValue.toInt(),
                                        onDismiss = { showRandomRangeEditDialog = false },
                                        onConfirm = { minutes: Int, seconds: Int ->
                                            randomRangeMinutesSliderValue = minutes.toFloat()
                                            randomRangeSecondsSliderValue = seconds.toFloat()
                                            viewModel.updateRandomRangeMinutes(minutes)
                                            viewModel.updateRandomRangeSeconds(seconds)
                                            showRandomRangeEditDialog = false
                                        }
                                    )
                                }
                            }

                            // 截屏质量
                            Text(
                                text = "截屏质量",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSecondaryContainer,
                                modifier = Modifier.padding(bottom = 4.dp, top = 8.dp)
                            )

                            var qualityValue by remember { mutableFloatStateOf(rule.qualitySetting.toFloat()) }
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Slider(
                                    value = qualityValue,
                                    onValueChange = { newValue ->
                                        qualityValue = newValue.roundToInt().toFloat()
                                    },
                                    onValueChangeFinished = { viewModel.updateQualitySetting(qualityValue.toInt()) },
                                    valueRange = 1f..100f,
                                    steps = 98, // 100档：1%-100%
                                    modifier = Modifier.weight(1f)
                                )

                                Text(
                                    text = "${qualityValue.toInt()}%",
                                    style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
                                    modifier = Modifier.padding(start = 8.dp)
                                )
                            }

                            // 分辨率
                            Text(
                                text = "分辨率",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSecondaryContainer,
                                modifier = Modifier.padding(bottom = 4.dp, top = 8.dp)
                            )

                            var resolutionValue by remember { mutableFloatStateOf(rule.resolutionSetting.toFloat()) }
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Slider(
                                    value = resolutionValue,
                                    onValueChange = { newValue ->
                                        resolutionValue = newValue.roundToInt().toFloat()
                                    },
                                    onValueChangeFinished = { viewModel.updateResolutionSetting(resolutionValue.toInt()) },
                                    valueRange = 1f..100f,
                                    steps = 98, // 100档：1%-100%
                                    modifier = Modifier.weight(1f)
                                )

                                Text(
                                    text = "${resolutionValue.toInt()}%",
                                    style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
                                    modifier = Modifier.padding(start = 8.dp)
                                )
                            }

                            // 高斯模糊（数值越大越模糊）
                            Text(
                                text = "高斯模糊（数值越大越模糊）",
                                style = MaterialTheme.typography.bodyMedium,
                                color = MaterialTheme.colorScheme.onSecondaryContainer,
                                modifier = Modifier.padding(bottom = 4.dp, top = 8.dp)
                            )

                            var blurValue by remember { mutableFloatStateOf(rule.blurSetting.toFloat()) }
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Slider(
                                    value = blurValue,
                                    onValueChange = { newValue ->
                                        blurValue = newValue.roundToInt().toFloat()
                                    },
                                    onValueChangeFinished = { viewModel.updateBlurSetting(blurValue.toInt()) },
                                    valueRange = 0f..100f,
                                    steps = 99, // 101档：0%-100%
                                    modifier = Modifier.weight(1f)
                                )

                                Text(
                                    text = "${blurValue.toInt()}%",
                                    style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
                                    modifier = Modifier.padding(start = 8.dp)
                                )
                            }

                            // 时间编辑对话框
                            if (showIntervalEditDialog) {
                                IntervalEditDialog(
                                    currentMinutes = minutesSliderValue.toInt(),
                                    currentSeconds = secondsSliderValue.toInt(),
                                    onDismiss = { showIntervalEditDialog = false },
                                    onConfirm = { minutes: Int, seconds: Int ->
                                        minutesSliderValue = minutes.toFloat()
                                        secondsSliderValue = seconds.toFloat()
                                        viewModel.updateIntervalMinutes(minutes)
                                        viewModel.updateIntervalSeconds(seconds)
                                        showIntervalEditDialog = false
                                    }
                                )
                            }
                        }
                    }

                    // 拼图设置卡片
                    SettingsCard(title = "拼图设置") {
                        // 获取拼图设置ViewModel
                        val collageSettingsViewModel: CollageSettingsViewModel = viewModel(
                            factory = CollageSettingsViewModelFactory(LocalContext.current.applicationContext as Application)
                        )
                        val collageSettingsState by collageSettingsViewModel.collageSettings.collectAsState()

                        when (collageSettingsState) {
                            is UiState.Loading -> {
                                Box(
                                    modifier = Modifier.fillMaxWidth().height(100.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    CircularProgressIndicator()
                                }
                            }
                            is UiState.Error -> {
                                Text(
                                    text = "加载拼图设置失败: ${(collageSettingsState as UiState.Error).message}",
                                    color = MaterialTheme.colorScheme.error
                                )
                            }
                            is UiState.Success -> {
                                val settings = (collageSettingsState as UiState.Success<CollageSettings>).data

                                Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                                    // 自动拼图
                                    SettingsSwitchItem(
                                        title = "每日自动拼图",
                                        description = "在指定时间自动生成当日拼图",
                                        checked = settings.isAutoCollageEnabled,
                                        onCheckedChange = { collageSettingsViewModel.updateAutoCollageEnabled(it) }
                                    )

                                    // 拼图时间
                                    if (settings.isAutoCollageEnabled) {
                                        Text(
                                            text = "拼图时间",
                                            fontSize = 14.sp,
                                            color = MaterialTheme.colorScheme.onSecondaryContainer,
                                            modifier = Modifier.padding(bottom = 4.dp, top = 8.dp)
                                        )

                                        // 时间选择器
                                        val timeFormat = SimpleDateFormat("HH:mm", Locale.getDefault())
                                        val calendar = Calendar.getInstance().apply {
                                            set(Calendar.HOUR_OF_DAY, settings.collageHour)
                                            set(Calendar.MINUTE, settings.collageMinute)
                                        }
                                        val timeString = timeFormat.format(calendar.time)

                                        var showTimePicker by remember { mutableStateOf(false) }

                                        Card(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .clickable { showTimePicker = true },
                                            shape = RoundedCornerShape(8.dp),
                                            colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surfaceVariant)
                                        ) {
                                            Text(
                                                text = timeString,
                                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                                modifier = Modifier.padding(16.dp)
                                            )
                                        }

                                        if (showTimePicker) {
                                            TimePickerDialog(
                                                initialHour = settings.collageHour,
                                                initialMinute = settings.collageMinute,
                                                onDismiss = { showTimePicker = false },
                                                onConfirm = { hour, minute ->
                                                    val newCalendar = Calendar.getInstance().apply {
                                                        set(Calendar.HOUR_OF_DAY, hour)
                                                        set(Calendar.MINUTE, minute)
                                                    }
                                                    collageSettingsViewModel.updateCollageTime(
                                                        timeFormat.format(newCalendar.time)
                                                    )
                                                    showTimePicker = false
                                                }
                                            )
                                        }
                                    }

                                    // 拼图布局
                                    Text(
                                        text = "拼图布局",
                                        fontSize = 14.sp,
                                        color = MaterialTheme.colorScheme.onSecondaryContainer,
                                        modifier = Modifier.padding(bottom = 4.dp, top = 8.dp)
                                    )

                                    var showLayoutOptions by remember { mutableStateOf(false) }

                                    Box {
                                        Card(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .clickable { showLayoutOptions = true },
                                            shape = RoundedCornerShape(8.dp),
                                            colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surfaceVariant)
                                        ) {
                                            Text(
                                                text = settings.collageLayout.displayName,
                                                color = MaterialTheme.colorScheme.onSurfaceVariant,
                                                modifier = Modifier.padding(16.dp)
                                            )
                                        }

                                        DropdownMenu(
                                            expanded = showLayoutOptions,
                                            onDismissRequest = { showLayoutOptions = false }
                                        ) {
                                            collageSettingsViewModel.layoutOptions.forEach { option ->
                                                DropdownMenuItem(
                                                    text = { Text(option) },
                                                    onClick = {
                                                        collageSettingsViewModel.updateCollageLayout(option)
                                                        showLayoutOptions = false
                                                    }
                                                )
                                            }
                                        }
                                    }

                                    // 拼图质量设置 - 三个独立滑块
                                    Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                                        // 拼图质量
                                        Text(
                                            text = "拼图质量",
                                            style = MaterialTheme.typography.bodyMedium,
                                            color = MaterialTheme.colorScheme.onSecondaryContainer
                                        )

                                        var collageQualityValue by remember { mutableFloatStateOf(settings.collageQuality.toFloat()) }
                                        Row(
                                            modifier = Modifier.fillMaxWidth(),
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            Slider(
                                                value = collageQualityValue,
                                                onValueChange = { newValue ->
                                                    collageQualityValue = newValue
                                                },
                                                onValueChangeFinished = {
                                                    collageSettingsViewModel.updateCollageQuality(collageQualityValue.toInt())
                                                },
                                                valueRange = 1f..100f,
                                                modifier = Modifier.weight(1f)
                                            )

                                            Text(
                                                text = "${collageQualityValue.toInt()}%",
                                                style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
                                                modifier = Modifier.padding(start = 8.dp).width(48.dp),
                                                textAlign = TextAlign.End
                                            )
                                        }

                                        // 分辨率
                                        Text(
                                            text = "分辨率",
                                            style = MaterialTheme.typography.bodyMedium,
                                            color = MaterialTheme.colorScheme.onSecondaryContainer
                                        )

                                        var collageResolutionValue by remember { mutableFloatStateOf(settings.collageResolution.toFloat()) }
                                        Row(
                                            modifier = Modifier.fillMaxWidth(),
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            Slider(
                                                value = collageResolutionValue,
                                                onValueChange = { newValue ->
                                                    collageResolutionValue = newValue
                                                },
                                                onValueChangeFinished = {
                                                    collageSettingsViewModel.updateCollageResolution(collageResolutionValue.toInt())
                                                },
                                                valueRange = 1f..100f,
                                                modifier = Modifier.weight(1f)
                                            )

                                            Text(
                                                text = "${collageResolutionValue.toInt()}%",
                                                style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
                                                modifier = Modifier.padding(start = 8.dp).width(48.dp),
                                                textAlign = TextAlign.End
                                            )
                                        }

                                        // 高斯模糊
                                        Text(
                                            text = "高斯模糊",
                                            style = MaterialTheme.typography.bodyMedium,
                                            color = MaterialTheme.colorScheme.onSecondaryContainer
                                        )

                                        var collageBlurValue by remember { mutableFloatStateOf(settings.collageBlur.toFloat()) }
                                        Row(
                                            modifier = Modifier.fillMaxWidth(),
                                            verticalAlignment = Alignment.CenterVertically
                                        ) {
                                            Slider(
                                                value = collageBlurValue,
                                                onValueChange = { newValue ->
                                                    collageBlurValue = newValue
                                                },
                                                onValueChangeFinished = {
                                                    collageSettingsViewModel.updateCollageBlur(collageBlurValue.toInt())
                                                },
                                                valueRange = 0f..100f,
                                                modifier = Modifier.weight(1f)
                                            )

                                            Text(
                                                text = "${collageBlurValue.toInt()}%",
                                                style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
                                                modifier = Modifier.padding(start = 8.dp).width(48.dp),
                                                textAlign = TextAlign.End
                                            )
                                        }
                                    }
                                }
                            }
                            else -> {
                                // 处理其他状态
                                Text("准备加载拼图设置...")
                            }
                        }
                    }

                    // 通知设置卡片
                    SettingsCard(title = "通知设置") {
                        // 获取通知设置ViewModel
                        val notificationSettingsViewModel: NotificationSettingsViewModel = viewModel(
                            factory = NotificationSettingsViewModelFactory(LocalContext.current.applicationContext as Application)
                        )
                        val notificationSettingsState by notificationSettingsViewModel.notificationSettings.collectAsState()

                        when (notificationSettingsState) {
                            is UiState.Loading -> {
                                Box(
                                    modifier = Modifier.fillMaxWidth().height(100.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    CircularProgressIndicator()
                                }
                            }
                            is UiState.Error -> {
                                Text(
                                    text = "加载通知设置失败: ${(notificationSettingsState as UiState.Error).message}",
                                    color = MaterialTheme.colorScheme.error
                                )
                            }
                            is UiState.Success -> {
                                val settings = (notificationSettingsState as UiState.Success<NotificationSettings>).data

                                Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                                    // 截屏通知
                                    SettingsSwitchItem(
                                        title = "截屏通知",
                                        description = "每次截屏时显示通知",
                                        checked = settings.isScreenshotNotificationEnabled,
                                        onCheckedChange = { notificationSettingsViewModel.updateScreenshotNotificationEnabled(it) }
                                    )

                                    // 拼图通知
                                    SettingsSwitchItem(
                                        title = "拼图通知",
                                        description = "拼图完成时显示通知",
                                        checked = settings.isCollageNotificationEnabled,
                                        onCheckedChange = { notificationSettingsViewModel.updateCollageNotificationEnabled(it) }
                                    )
                                }
                            }
                            else -> {
                                // 处理其他状态
                                Text("准备加载通知设置...")
                            }
                        }
                    }



                    // 关于卡片
                    SettingsCard(title = "关于") {
                        Column(verticalArrangement = Arrangement.spacedBy(16.dp)) {
                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text(
                                    text = "版本",
                                    color = MaterialTheme.colorScheme.onSecondaryContainer
                                )
                                Text(
                                    text = "1.0.0"
                                )
                            }

                            Row(
                                modifier = Modifier.fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceBetween
                            ) {
                                Text(
                                    text = "开发者",
                                    color = MaterialTheme.colorScheme.onSecondaryContainer
                                )
                                Text(
                                    text = "自律监督团队"
                                )
                            }
                        }
                    }
                }
                is UiState.Empty -> {
                    Text("没有配置数据")
                }
                is UiState.Idle -> {
                    Text("等待加载...")
                }
                else -> {
                    Text("未知状态")
                }
            }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ConfigScreenPreview() {
    MaterialTheme {
        ConfigScreen(
            onNavigateBack = {}
        )
    }
}

// 移除旧的设置部分，因为已经在新的UI中重新实现
// 保留这些函数的定义，但不再使用它们
@Composable
fun IntervalSettingSection(
    intervalMinutes: Int,
    isRandom: Boolean,
    randomRange: Int,
    onIntervalChanged: (Int) -> Unit,
    onRandomToggled: (Boolean) -> Unit,
    onRandomRangeChanged: (Int) -> Unit
) {
    // 不再使用此函数
}

@Composable
fun QualitySettingSection(
    quality: Int,
    onQualityChanged: (Int) -> Unit
) {
    // 不再使用此函数
}

/**
 * 时间间隔编辑对话框
 */
@Composable
fun IntervalEditDialog(
    currentMinutes: Int,
    currentSeconds: Int,
    onDismiss: () -> Unit,
    onConfirm: (minutes: Int, seconds: Int) -> Unit
) {
    var minutes by remember { mutableStateOf(currentMinutes.toString()) }
    var seconds by remember { mutableStateOf(currentSeconds.toString()) }
    var showError by remember { mutableStateOf(false) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "编辑截图间隔时间",
                style = MaterialTheme.typography.headlineSmall
            )
        },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 分钟输入
                OutlinedTextField(
                    value = minutes,
                    onValueChange = { newValue ->
                        if (newValue.isEmpty() || (newValue.toIntOrNull() != null && newValue.toInt() in 0..60)) {
                            minutes = newValue
                            showError = false
                        }
                    },
                    label = { Text("分钟") },
                    suffix = { Text("分") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth()
                )

                // 秒数输入
                OutlinedTextField(
                    value = seconds,
                    onValueChange = { newValue ->
                        if (newValue.isEmpty() || (newValue.toIntOrNull() != null && newValue.toInt() in 0..59)) {
                            seconds = newValue
                            showError = false
                        }
                    },
                    label = { Text("秒数") },
                    suffix = { Text("秒") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth()
                )

                // 错误提示
                if (showError) {
                    Text(
                        text = "分钟和秒数不能同时为0",
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodySmall
                    )
                }

                // 说明文字
                Text(
                    text = "分钟范围：0-60，秒数范围：0-59\n注意：分钟和秒数不能同时为0",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val minutesInt = minutes.toIntOrNull() ?: 0
                    val secondsInt = seconds.toIntOrNull() ?: 0

                    // 验证：分钟和秒数不能同时为0
                    if (minutesInt == 0 && secondsInt == 0) {
                        showError = true
                        return@TextButton
                    }

                    // 验证范围
                    if (minutesInt in 0..60 && secondsInt in 0..59) {
                        onConfirm(minutesInt, secondsInt)
                    } else {
                        showError = true
                    }
                }
            ) {
                Text("确定")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}

/**
 * 随机范围编辑对话框
 */
@Composable
fun RandomRangeEditDialog(
    currentMinutes: Int,
    currentSeconds: Int,
    onDismiss: () -> Unit,
    onConfirm: (minutes: Int, seconds: Int) -> Unit
) {
    var minutes by remember { mutableStateOf(currentMinutes.toString()) }
    var seconds by remember { mutableStateOf(currentSeconds.toString()) }
    var showError by remember { mutableStateOf(false) }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = {
            Text(
                text = "编辑随机范围时间",
                style = MaterialTheme.typography.headlineSmall
            )
        },
        text = {
            Column(
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 分钟输入
                OutlinedTextField(
                    value = minutes,
                    onValueChange = { newValue ->
                        if (newValue.isEmpty() || (newValue.toIntOrNull() != null && newValue.toInt() in 0..60)) {
                            minutes = newValue
                            showError = false
                        }
                    },
                    label = { Text("分钟") },
                    suffix = { Text("分") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth()
                )

                // 秒数输入
                OutlinedTextField(
                    value = seconds,
                    onValueChange = { newValue ->
                        if (newValue.isEmpty() || (newValue.toIntOrNull() != null && newValue.toInt() in 0..59)) {
                            seconds = newValue
                            showError = false
                        }
                    },
                    label = { Text("秒数") },
                    suffix = { Text("秒") },
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                    singleLine = true,
                    modifier = Modifier.fillMaxWidth()
                )

                // 错误提示
                if (showError) {
                    Text(
                        text = "输入范围无效",
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodySmall
                    )
                }

                // 说明文字
                Text(
                    text = "分钟范围：0-60，秒数范围：0-59\n随机范围表示在基础间隔时间上的随机增减幅度",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    val minutesInt = minutes.toIntOrNull() ?: 0
                    val secondsInt = seconds.toIntOrNull() ?: 0

                    // 验证范围
                    if (minutesInt in 0..60 && secondsInt in 0..59) {
                        onConfirm(minutesInt, secondsInt)
                    } else {
                        showError = true
                    }
                }
            ) {
                Text("确定")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("取消")
            }
        }
    )
}