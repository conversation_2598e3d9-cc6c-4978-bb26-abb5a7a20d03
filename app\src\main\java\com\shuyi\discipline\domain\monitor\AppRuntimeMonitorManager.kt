package com.shuyi.discipline.domain.monitor

import android.content.Context
import android.os.Build
import com.shuyi.discipline.data.repository.RuntimeRecordRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 应用运行时间监控管理器
 * 统一管理不同版本的监控策略
 */
@Singleton
class AppRuntimeMonitorManager @Inject constructor(
    private val context: Context,
    private val repository: RuntimeRecordRepository,
    private val applicationScope: CoroutineScope,
    private val exitInfoMonitor: ExitInfoMonitor,
    private val heartbeatMonitor: HeartbeatMonitor,
    private val recoveryManager: RecoveryManager,
    private val persistentAppStateManager: PersistentAppStateManager
) {

    // 🎯 移除重复的时间记录变量，使用统一的状态管理器
    // private var appStartTime: Long = 0L
    // private var realAppStartTime: Long = 0L

    /**
     * 设置真实的应用启动时间（由Application.onCreate调用）
     * 保留此方法以保持向后兼容，但实际使用状态管理器
     */
    fun setRealAppStartTime(startTime: Long) {
        // 🎯 委托给状态管理器处理
        // persistentAppStateManager.recordAppForeground() 会在 onStart 中调用
        Timber.d("🎯 [AppRuntimeMonitorManager] 设置真实应用启动时间: ${java.util.Date(startTime)} (委托给状态管理器)")
    }

    /**
     * 启动监控
     */
    fun startMonitoring() {
        applicationScope.launch {
            try {
                Timber.d("🚀 [AppRuntimeMonitorManager] 启动应用运行时间监控")

                // 🎯 使用状态管理器的启动时间
                val actualStartTime = if (persistentAppStateManager.isValidTime()) {
                    val stateManagerTime = persistentAppStateManager.getAppStartTime()
                    Timber.d("✅ 使用状态管理器的启动时间: ${java.util.Date(stateManagerTime)}")
                    stateManagerTime
                } else {
                    val fallbackTime = System.currentTimeMillis()
                    Timber.w("⚠️ 状态管理器时间无效，使用当前时间作为降级方案: ${java.util.Date(fallbackTime)}")
                    Timber.w(persistentAppStateManager.getDebugInfo())
                    fallbackTime
                }

                // 1. 首先尝试恢复可能丢失的数据
                recoveryManager.recoverMissingData()

                // 2. 修复现有DOWNTIME记录的endTime字段（一次性修复）
                (repository as? com.shuyi.discipline.data.repository.impl.RuntimeRecordRepositoryImpl)?.fixDowntimeEndTimes()

                // 3. 根据系统版本选择监控策略
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    Timber.d("使用 ApplicationExitInfo 监控策略")
                    exitInfoMonitor.startMonitoring()
                } else {
                    Timber.d("使用 WorkManager 心跳监控策略")
                    heartbeatMonitor.startMonitoring()
                }

                // 4. 记录当前会话开始（使用状态管理器的启动时间）
                recordSessionStart(actualStartTime)

            } catch (e: Exception) {
                Timber.e(e, "[AppRuntimeMonitorManager] 启动监控失败")
            }
        }
    }

    /**
     * 停止监控
     */
    fun stopMonitoring() {
        applicationScope.launch {
            try {
                Timber.d("停止应用运行时间监控")
                
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    exitInfoMonitor.stopMonitoring()
                } else {
                    heartbeatMonitor.stopMonitoring()
                }
                
                // 记录当前会话结束
                recordSessionEnd()
                
            } catch (e: Exception) {
                Timber.e(e, "停止监控失败")
            }
        }
    }

    /**
     * 应用进入前台
     */
    fun onAppForeground() {
        applicationScope.launch {
            try {
                Timber.d("[AppRuntimeMonitorManager] 应用进入前台")

                // 🎯 检查并处理可能的退出记录，使用状态管理器的启动时间
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    if (persistentAppStateManager.isValidTime()) {
                        val actualStartTime = persistentAppStateManager.getAppStartTime()
                        Timber.d("🔍 应用进入前台检查，使用状态管理器启动时间: ${java.util.Date(actualStartTime)}")
                        exitInfoMonitor.checkAndRecordExitInfo(actualStartTime)
                    } else {
                        Timber.w("⚠️ 状态管理器时间无效，跳过退出信息检查")
                        Timber.w(persistentAppStateManager.getDebugInfo())
                    }
                }

                // 更新会话状态
                repository.updateSessionStatus(SessionStatus.FOREGROUND)

                // 更新心跳（低版本需要）
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.R) {
                    heartbeatMonitor.updateHeartbeat(isInForeground = true)
                }

            } catch (e: Exception) {
                Timber.e(e, "[AppRuntimeMonitorManager] 处理前台事件失败")
            }
        }
    }

    /**
     * 应用进入后台
     */
    fun onAppBackground() {
        applicationScope.launch {
            try {
                Timber.d("[AppRuntimeMonitorManager] 应用进入后台")

                // 更新会话状态
                repository.updateSessionStatus(SessionStatus.BACKGROUND)

                // 更新心跳（低版本需要）
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.R) {
                    heartbeatMonitor.updateHeartbeat(isInForeground = false)
                }

            } catch (e: Exception) {
                Timber.e(e, "[AppRuntimeMonitorManager] 处理后台事件失败")
            }
        }
    }

    /**
     * 获取运行时间统计
     */
    suspend fun getRuntimeStats(hours: Int = 24): RuntimeStats {
        return try {
            repository.getRuntimeStats(hours)
        } catch (e: Exception) {
            Timber.e(e, "获取运行时间统计失败")
            RuntimeStats.empty()
        }
    }

    /**
     * 立即执行一次检查
     */
    fun executeImmediateCheck() {
        applicationScope.launch {
            try {
                // 🎯 使用状态管理器的启动时间进行检查
                if (persistentAppStateManager.isValidTime()) {
                    val actualStartTime = persistentAppStateManager.getAppStartTime()
                    Timber.d("🔍 [AppRuntimeMonitorManager] 执行立即检查，使用状态管理器启动时间: ${java.util.Date(actualStartTime)}")

                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                        exitInfoMonitor.checkAndRecordExitInfo(actualStartTime)
                    } else {
                        heartbeatMonitor.performHealthCheck()
                    }
                } else {
                    Timber.w("⚠️ [AppRuntimeMonitorManager] 状态管理器时间无效，跳过立即检查")
                    Timber.w(persistentAppStateManager.getDebugInfo())
                }
            } catch (e: Exception) {
                Timber.e(e, "[AppRuntimeMonitorManager] 立即检查失败")
            }
        }
    }

    /**
     * 记录会话开始
     */
    private suspend fun recordSessionStart(startTime: Long = System.currentTimeMillis()) {
        repository.startNewSession(startTime)
        Timber.d("📝 记录会话开始: ${java.util.Date(startTime)}")
    }

    /**
     * 记录会话结束
     */
    private suspend fun recordSessionEnd() {
        val currentTime = System.currentTimeMillis()
        repository.endCurrentSession(currentTime, ExitReason.USER_EXIT)
        Timber.d("记录会话结束: ${java.util.Date(currentTime)}")
    }

    /**
     * 获取监控状态
     */
    fun getMonitoringStatus(): MonitoringStatus {
        return MonitoringStatus(
            isActive = true,
            strategy = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                MonitoringStrategy.EXIT_INFO
            } else {
                MonitoringStrategy.HEARTBEAT
            },
            lastCheckTime = System.currentTimeMillis(),
            workManagerStatus = if (Build.VERSION.SDK_INT < Build.VERSION_CODES.R) {
                heartbeatMonitor.getWorkManagerStatus()
            } else {
                WorkManagerStatus.NOT_APPLICABLE
            }
        )
    }
}

/**
 * 会话状态
 */
enum class SessionStatus {
    FOREGROUND,    // 前台
    BACKGROUND,    // 后台
    ENDED          // 已结束
}

/**
 * 退出原因
 */
enum class ExitReason {
    USER_EXIT,     // 用户主动退出
    SYSTEM_KILLED, // 系统杀死
    APP_CRASH,     // 应用崩溃
    UNKNOWN        // 未知原因
}

/**
 * 监控策略
 */
enum class MonitoringStrategy {
    EXIT_INFO,     // ApplicationExitInfo
    HEARTBEAT      // WorkManager 心跳
}

/**
 * WorkManager 状态
 */
enum class WorkManagerStatus {
    RUNNING,           // 正常运行
    STOPPED,           // 已停止
    FAILED,            // 失败
    NOT_APPLICABLE     // 不适用
}

/**
 * 监控状态
 */
data class MonitoringStatus(
    val isActive: Boolean,
    val strategy: MonitoringStrategy,
    val lastCheckTime: Long,
    val workManagerStatus: WorkManagerStatus
)

/**
 * 运行时间统计
 */
data class RuntimeStats(
    val totalRuntime: Long,        // 总运行时间
    val totalDowntime: Long,       // 总未运行时间
    val sessionCount: Int,         // 会话次数
    val averageSessionDuration: Long, // 平均会话时长
    val lastExitTime: Long?        // 最后退出时间
) {
    companion object {
        fun empty() = RuntimeStats(0, 0, 0, 0, null)
    }
}
