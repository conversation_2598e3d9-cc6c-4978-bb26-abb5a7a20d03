package com.shuyi.discipline.domain.monitor

import android.content.Context
import android.content.SharedPreferences
import androidx.hilt.work.HiltWorker
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.shuyi.discipline.data.repository.RuntimeRecordRepository
import dagger.assisted.Assisted
import dagger.assisted.AssistedInject
import timber.log.Timber

/**
 * 心跳检查 Worker
 * 定期检查应用心跳状态，检测应用是否异常退出
 */
@HiltWorker
class HeartbeatCheckWorker @AssistedInject constructor(
    @Assisted private val context: Context,
    @Assisted workerParams: WorkerParameters,
    private val repository: RuntimeRecordRepository,
    private val stateManager: PersistentAppStateManager
) : CoroutineWorker(context, workerParams) {

    private val heartbeatPrefs: SharedPreferences by lazy {
        context.getSharedPreferences(HEARTBEAT_PREFS_NAME, Context.MODE_PRIVATE)
    }

    override suspend fun doWork(): Result {
        return try {
            Timber.d("开始执行心跳检查")
            
            val checkResult = performHeartbeatCheck()
            
            when (checkResult.status) {
                HeartbeatStatus.HEALTHY -> {
                    Timber.d("心跳检查正常")
                    updateLastCheckTime()
                    Result.success()
                }
                HeartbeatStatus.APP_EXITED -> {
                    Timber.d("检测到应用退出，记录未运行时长")
                    recordAppExit(checkResult)
                    updateLastCheckTime()
                    Result.success()
                }
                HeartbeatStatus.STALE_HEARTBEAT -> {
                    Timber.w("心跳过期，可能应用异常")
                    handleStaleHeartbeat(checkResult)
                    updateLastCheckTime()
                    Result.success()
                }
                HeartbeatStatus.NO_HEARTBEAT -> {
                    Timber.w("未找到心跳数据")
                    updateLastCheckTime()
                    Result.success()
                }
            }
            
        } catch (e: Exception) {
            Timber.e(e, "心跳检查失败")
            Result.failure()
        }
    }

    /**
     * 执行心跳检查
     */
    private fun performHeartbeatCheck(): HeartbeatCheckResult {
        val currentTime = System.currentTimeMillis()
        val lastHeartbeat = heartbeatPrefs.getLong(KEY_LAST_HEARTBEAT, 0)
        val isInForeground = heartbeatPrefs.getBoolean(KEY_IS_FOREGROUND, false)
        val isExiting = heartbeatPrefs.getBoolean(KEY_IS_EXITING, false)
        val lastCheckTime = getLastCheckTime()

        Timber.d("心跳检查详情:")
        Timber.d("  当前时间: ${java.util.Date(currentTime)}")
        Timber.d("  上次心跳: ${if (lastHeartbeat > 0) java.util.Date(lastHeartbeat) else "无"}")
        Timber.d("  上次检查: ${if (lastCheckTime > 0) java.util.Date(lastCheckTime) else "无"}")
        Timber.d("  前台状态: $isInForeground")
        Timber.d("  退出标记: $isExiting")

        return when {
            lastHeartbeat == 0L -> {
                // 没有心跳数据
                HeartbeatCheckResult(
                    status = HeartbeatStatus.NO_HEARTBEAT,
                    currentTime = currentTime,
                    lastHeartbeat = lastHeartbeat,
                    heartbeatAge = 0,
                    isInForeground = isInForeground,
                    isExiting = isExiting
                )
            }
            
            isExiting -> {
                // 应用标记为正在退出
                val heartbeatAge = currentTime - lastHeartbeat
                HeartbeatCheckResult(
                    status = HeartbeatStatus.APP_EXITED,
                    currentTime = currentTime,
                    lastHeartbeat = lastHeartbeat,
                    heartbeatAge = heartbeatAge,
                    isInForeground = isInForeground,
                    isExiting = isExiting
                )
            }
            
            else -> {
                val heartbeatAge = currentTime - lastHeartbeat
                
                when {
                    heartbeatAge <= HEALTHY_THRESHOLD -> {
                        // 心跳正常
                        HeartbeatCheckResult(
                            status = HeartbeatStatus.HEALTHY,
                            currentTime = currentTime,
                            lastHeartbeat = lastHeartbeat,
                            heartbeatAge = heartbeatAge,
                            isInForeground = isInForeground,
                            isExiting = isExiting
                        )
                    }
                    
                    heartbeatAge <= STALE_THRESHOLD -> {
                        // 心跳过期但可能还在运行
                        HeartbeatCheckResult(
                            status = HeartbeatStatus.STALE_HEARTBEAT,
                            currentTime = currentTime,
                            lastHeartbeat = lastHeartbeat,
                            heartbeatAge = heartbeatAge,
                            isInForeground = isInForeground,
                            isExiting = isExiting
                        )
                    }
                    
                    else -> {
                        // 心跳严重过期，认为应用已退出
                        HeartbeatCheckResult(
                            status = HeartbeatStatus.APP_EXITED,
                            currentTime = currentTime,
                            lastHeartbeat = lastHeartbeat,
                            heartbeatAge = heartbeatAge,
                            isInForeground = isInForeground,
                            isExiting = isExiting
                        )
                    }
                }
            }
        }
    }

    /**
     * 记录应用退出
     */
    private suspend fun recordAppExit(result: HeartbeatCheckResult) {
        try {
            val exitTime = result.lastHeartbeat
            val currentTime = result.currentTime

            Timber.d("[HeartbeatCheckWorker] 检测到应用退出:")
            Timber.d("  退出时间: ${java.util.Date(exitTime)}")
            Timber.d("  当前时间: ${java.util.Date(currentTime)}")

            // 🎯 通知状态管理器应用已关闭
            val shouldRecord = stateManager.recordAppShutdown(exitTime, "心跳监控检测到应用退出")

            if (!shouldRecord) {
                Timber.d("[HeartbeatCheckWorker] 关闭时间已被处理过，跳过重复记录")
                return
            }

            // 🎯 使用状态管理器的启动时间
            val actualStartTime = if (stateManager.isValidTime()) {
                stateManager.getAppStartTime()
            } else {
                Timber.w("[HeartbeatCheckWorker] 状态管理器时间无效，使用当前时间")
                Timber.w(stateManager.getDebugInfo())
                currentTime
            }

            val downtimeDuration = actualStartTime - exitTime

            val exitReason = if (result.isExiting) {
                ExitReason.USER_EXIT
            } else {
                ExitReason.UNKNOWN
            }

            // 🎯 记录退出和未运行时长（由监控系统记录，与状态管理器协作）
            repository.recordExit(
                exitTime = exitTime,
                exitReason = exitReason,
                description = "HeartbeatCheckWorker检测: 心跳监控检测到应用退出",
                downtimeStart = exitTime,
                downtimeEnd = actualStartTime,
                downtimeDuration = downtimeDuration
            )

            Timber.d("✅ [HeartbeatCheckWorker] 已记录应用退出:")
            Timber.d("  退出时间: ${java.util.Date(exitTime)}")
            Timber.d("  重启时间: ${java.util.Date(actualStartTime)}")
            Timber.d("  未运行时长: ${downtimeDuration}ms (${downtimeDuration / 1000}秒)")

            // 清除心跳数据，避免重复记录
            clearHeartbeatData()

        } catch (e: Exception) {
            Timber.e(e, "[HeartbeatCheckWorker] 记录应用退出失败")
        }
    }

    /**
     * 处理过期心跳
     */
    private suspend fun handleStaleHeartbeat(result: HeartbeatCheckResult) {
        try {
            Timber.w("心跳过期，心跳年龄: ${result.heartbeatAge}ms")
            
            // 如果心跳过期时间超过阈值，也记录为退出
            if (result.heartbeatAge > EXIT_THRESHOLD) {
                Timber.w("心跳过期时间过长，记录为应用退出")
                recordAppExit(result)
            }
            
        } catch (e: Exception) {
            Timber.e(e, "处理过期心跳失败")
        }
    }

    /**
     * 清除心跳数据
     */
    private fun clearHeartbeatData() {
        heartbeatPrefs.edit()
            .remove(KEY_LAST_HEARTBEAT)
            .remove(KEY_IS_FOREGROUND)
            .remove(KEY_IS_EXITING)
            .apply()
    }

    /**
     * 更新最后检查时间
     */
    private fun updateLastCheckTime() {
        heartbeatPrefs.edit()
            .putLong(KEY_LAST_CHECK_TIME, System.currentTimeMillis())
            .apply()
    }

    /**
     * 获取最后检查时间
     */
    private fun getLastCheckTime(): Long {
        return heartbeatPrefs.getLong(KEY_LAST_CHECK_TIME, 0)
    }

    companion object {
        private const val HEARTBEAT_PREFS_NAME = "app_heartbeat"
        private const val KEY_LAST_HEARTBEAT = "last_heartbeat"
        private const val KEY_IS_FOREGROUND = "is_foreground"
        private const val KEY_IS_EXITING = "is_exiting"
        private const val KEY_LAST_CHECK_TIME = "last_check_time"
        
        // 阈值定义
        private const val HEALTHY_THRESHOLD = 3 * 60 * 1000L      // 3分钟内为健康
        private const val STALE_THRESHOLD = 8 * 60 * 1000L        // 8分钟内为过期但可能运行
        private const val EXIT_THRESHOLD = 10 * 60 * 1000L        // 10分钟以上认为已退出
    }
}

/**
 * 心跳状态
 */
enum class HeartbeatStatus {
    HEALTHY,           // 健康
    STALE_HEARTBEAT,   // 心跳过期
    APP_EXITED,        // 应用已退出
    NO_HEARTBEAT       // 无心跳数据
}

/**
 * 心跳检查结果
 */
data class HeartbeatCheckResult(
    val status: HeartbeatStatus,
    val currentTime: Long,
    val lastHeartbeat: Long,
    val heartbeatAge: Long,
    val isInForeground: Boolean,
    val isExiting: Boolean
)
