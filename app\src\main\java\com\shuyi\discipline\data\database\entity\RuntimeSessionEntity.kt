package com.shuyi.discipline.data.database.entity

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Index
import androidx.room.PrimaryKey
import com.shuyi.discipline.domain.monitor.ExitReason
import com.shuyi.discipline.domain.monitor.SessionStatus

/**
 * 运行时会话实体
 */
@Entity(
    tableName = "runtime_sessions",
    indices = [
        Index(value = ["start_time"]),
        Index(value = ["status"])
    ]
)
data class RuntimeSessionEntity(
    @PrimaryKey
    @ColumnInfo(name = "session_id")
    val sessionId: String,

    @ColumnInfo(name = "start_time")
    val startTime: Long,

    @ColumnInfo(name = "end_time")
    val endTime: Long?,

    @ColumnInfo(name = "status")
    val status: SessionStatus,

    @ColumnInfo(name = "exit_reason")
    val exitReason: ExitReason?,

    @ColumnInfo(name = "total_duration")
    val totalDuration: Long,

    @ColumnInfo(name = "foreground_duration")
    val foregroundDuration: Long,

    @ColumnInfo(name = "background_duration")
    val backgroundDuration: Long,

    @ColumnInfo(name = "created_at")
    val createdAt: Long = System.currentTimeMillis(),

    @ColumnInfo(name = "updated_at")
    val updatedAt: Long = System.currentTimeMillis()
)
