package com.shuyi.discipline.data.database.converter

import androidx.room.TypeConverter
import com.shuyi.discipline.data.repository.RecordType
import com.shuyi.discipline.domain.monitor.ExitReason
import com.shuyi.discipline.domain.monitor.SessionStatus

/**
 * 运行时监控相关的类型转换器
 */
class RuntimeTypeConverters {

    @TypeConverter
    fun fromRecordType(recordType: RecordType): String {
        return recordType.name
    }

    @TypeConverter
    fun toRecordType(recordType: String): RecordType {
        return try {
            RecordType.valueOf(recordType)
        } catch (e: IllegalArgumentException) {
            RecordType.STATUS_CHANGE // 默认值
        }
    }

    @TypeConverter
    fun fromExitReason(exitReason: ExitReason?): String? {
        return exitReason?.name
    }

    @TypeConverter
    fun toExitReason(exitReason: String?): ExitReason? {
        return if (exitReason != null) {
            try {
                ExitReason.valueOf(exitReason)
            } catch (e: IllegalArgumentException) {
                ExitReason.UNKNOWN // 默认值
            }
        } else {
            null
        }
    }

    @TypeConverter
    fun fromSessionStatus(sessionStatus: SessionStatus): String {
        return sessionStatus.name
    }

    @TypeConverter
    fun toSessionStatus(sessionStatus: String): SessionStatus {
        return try {
            SessionStatus.valueOf(sessionStatus)
        } catch (e: IllegalArgumentException) {
            SessionStatus.ENDED // 默认值
        }
    }
}
