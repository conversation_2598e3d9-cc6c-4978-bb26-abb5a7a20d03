package com.shuyi.discipline.domain.usecase

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.PixelFormat
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.Image
import android.media.ImageReader
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Handler
import android.os.Looper
import com.shuyi.discipline.data.model.Screenshot
import com.shuyi.discipline.data.repository.QuietPeriodRepository
import com.shuyi.discipline.data.repository.ScheduleRepository
import com.shuyi.discipline.data.repository.ScreenshotRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.nio.ByteBuffer

/**
 * 截图用例
 */
class TakeScreenshotUseCase(
    private val context: Context,
    private val screenshotRepository: ScreenshotRepository,
    private val scheduleRepository: ScheduleRepository,
    private val quietPeriodRepository: QuietPeriodRepository
) {

    /**
     * 执行截图操作
     * @param mediaProjection 媒体投影对象
     * @param resultCode 媒体投影授权结果码
     * @param data 媒体投影授权数据
     */
    suspend fun execute(
        mediaProjection: MediaProjection? = null,
        resultCode: Int = 0,
        data: Intent? = null
    ): Result<Screenshot> = withContext(Dispatchers.IO) {
        try {
            // 检查是否在免打扰时段
            if (quietPeriodRepository.isInQuietPeriod()) {
                return@withContext Result.failure(IllegalStateException("当前处于免打扰时段"))
            }

            // 获取调度规则
            val scheduleRule = scheduleRepository.getScheduleRule()
                ?: return@withContext Result.failure(IllegalStateException("未找到调度规则"))

            // 更新上次截图时间
            val timestamp = System.currentTimeMillis()
            scheduleRepository.updateLastCaptureTime(timestamp)

            // 获取屏幕尺寸
            val metrics = context.resources.displayMetrics
            val width = metrics.widthPixels
            val height = metrics.heightPixels
            val density = metrics.densityDpi

            // 设置截图参数
            val imageReader = ImageReader.newInstance(width, height, PixelFormat.RGBA_8888, 2)
            var virtualDisplay: VirtualDisplay? = null
            var bitmap: Bitmap? = null
            var result: Result<Screenshot>

            try {
                // 获取媒体投影
                val projection = mediaProjection ?: getMediaProjection(resultCode, data)
                    ?: return@withContext Result.failure(IllegalStateException("无法获取媒体投影权限"))

                // 检查MediaProjection是否仍然有效
                if (!isMediaProjectionValid(projection)) {
                    Timber.w("MediaProjection权限已失效，无法进行截图")
                    return@withContext Result.failure(SecurityException("MediaProjection权限已失效"))
                }

                // 创建虚拟显示
                virtualDisplay = projection.createVirtualDisplay(
                    "ScreenCapture",
                    width, height, density,
                    DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                    imageReader.surface, null, null
                )

                // 获取截图
                bitmap = captureImage(imageReader)
                    ?: return@withContext Result.failure(IllegalStateException("截图失败"))

                // 保存截图
                val file = screenshotRepository.saveBitmapToFile(
                    bitmap = bitmap,
                    quality = scheduleRule.qualitySetting,
                    resolution = scheduleRule.resolutionSetting,
                    blur = scheduleRule.blurSetting
                )

                // 获取前台应用包名
                val appPackage = getCurrentAppPackage()

                // 创建截图记录
                val screenshot = Screenshot(
                    timestamp = timestamp,
                    filePath = file.absolutePath,
                    quality = scheduleRule.qualitySetting,
                    appPackage = appPackage
                )

                // 保存截图记录
                screenshotRepository.saveScreenshot(screenshot)
                Timber.d("截图记录保存成功，ID: ${screenshot.id}")

                result = Result.success(screenshot)
            } catch (e: Exception) {
                Timber.e(e, "保存截图记录失败")
                result = Result.failure(e)
            } finally {
                // 确保所有资源都被正确释放
                try {
                    bitmap?.recycle()
                    virtualDisplay?.release()
                    imageReader.close()

                    // 如果是临时创建的MediaProjection，需要停止
                    if (mediaProjection == null) {
                        val projection = getMediaProjection(resultCode, data)
                        projection?.stop()
                    }
                } catch (e: Exception) {
                    Timber.e(e, "清理资源时出错")
                }
            }

            result
        } catch (e: Exception) {
            Timber.e(e, "截图失败")
            Result.failure(e)
        }
    }

    /**
     * 获取MediaProjection对象
     */
    private fun getMediaProjection(resultCode: Int, data: Intent?): MediaProjection? {
        if (resultCode != Activity.RESULT_OK || data == null) {
            return null
        }

        val projectionManager = context.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
        return projectionManager.getMediaProjection(resultCode, data)
    }

    /**
     * 检查MediaProjection是否仍然有效
     */
    private fun isMediaProjectionValid(mediaProjection: MediaProjection): Boolean {
        return try {
            // 尝试创建一个临时的虚拟显示来测试权限是否有效
            // 如果权限已被撤销，这里会抛出SecurityException
            val testDisplay = mediaProjection.createVirtualDisplay(
                "TestDisplay",
                1, 1, 1,
                DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
                null, null, null
            )
            testDisplay?.release() // 立即释放测试显示
            true
        } catch (e: SecurityException) {
            Timber.w("MediaProjection权限检查失败: ${e.message}")
            false
        } catch (e: Exception) {
            Timber.w("MediaProjection权限检查出现异常: ${e.message}")
            false
        }
    }

    /**
     * 从ImageReader捕获图像并转换为Bitmap
     */
    private fun captureImage(imageReader: ImageReader): Bitmap? {
        // 使用Handler确保在主线程关闭
        val handler = Handler(Looper.getMainLooper())

        // 尝试获取图像
        var image: Image? = null
        var retryCount = 0
        val maxRetries = 5

        while (retryCount < maxRetries) {
            try {
                Timber.d("尝试获取图像，当前重试次数: $retryCount")

                // 等待图像可用
                image = imageReader.acquireLatestImage()
                if (image == null) {
                    // 等待一段时间后再次尝试
                    Timber.d("图像为空，等待100ms后重试")
                    Thread.sleep(100L)
                    image = imageReader.acquireLatestImage()
                }

                // 如果仍然无法获取图像，重试
                if (image == null) {
                    Timber.d("仍然无法获取图像，重试")
                    retryCount++
                    Thread.sleep(200L * retryCount) // 每次重试等待时间增加
                    continue
                }

                Timber.d("成功获取图像，开始处理")

                // 获取图像平面
                val planes = image.planes
                if (planes.isEmpty()) {
                    Timber.e("图像平面为空")
                    image.close()
                    retryCount++
                    continue
                }

                val buffer = planes[0].buffer.duplicate()
                val pixelStride = planes[0].pixelStride
                val rowStride = planes[0].rowStride
                val rowPadding = rowStride - pixelStride * image.width

                // 创建Bitmap
                val bitmap = Bitmap.createBitmap(
                    image.width + rowPadding / pixelStride,
                    image.height, Bitmap.Config.ARGB_8888
                )

                // 将图像数据复制到Bitmap
                buffer.rewind()
                bitmap.copyPixelsFromBuffer(buffer)

                // 保存图像尺寸
                val imageWidth = image.width
                val imageHeight = image.height

                // 关闭图像
                image.close()
                image = null

                // 裁剪Bitmap以移除填充
                return if (rowPadding > 0) {
                    Bitmap.createBitmap(bitmap, 0, 0, imageWidth, imageHeight)
                } else {
                    bitmap
                }
            } catch (e: Exception) {
                Timber.e(e, "捕获图像失败，重试次数: $retryCount")
                image?.close()
                image = null
                retryCount++

                if (retryCount >= maxRetries) {
                    Timber.e("超过最大重试次数，放弃捕获")
                    return null
                }

                // 等待一段时间后重试
                Thread.sleep(300L * retryCount)
            }
        }

        return null
    }

    /**
     * 获取当前前台应用的包名（简单实现，实际环境可能需要额外权限）
     */
    private fun getCurrentAppPackage(): String? {
        return try {
            val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as android.app.ActivityManager
            val appTasks = activityManager.appTasks
            if (appTasks.isNotEmpty()) {
                appTasks[0].taskInfo?.topActivity?.packageName
            } else {
                null
            }
        } catch (e: Exception) {
            Timber.e(e, "获取前台应用包名失败")
            null
        }
    }
}