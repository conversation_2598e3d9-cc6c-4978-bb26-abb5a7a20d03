package com.shuyi.discipline.domain.monitor

import android.content.Context
import android.content.SharedPreferences
import com.shuyi.discipline.data.repository.RuntimeRecordRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 持久化应用状态管理器
 * 专门处理应用完全关闭和重启的时间计算
 * 
 * 核心功能：
 * 1. 持久化存储关键时间点，防止应用被杀死时数据丢失
 * 2. 准确区分后台运行和完全关闭状态
 * 3. 与现有监控系统协作，避免重复记录
 */
@Singleton
class PersistentAppStateManager @Inject constructor(
    private val context: Context,
    private val repository: RuntimeRecordRepository
) {
    
    companion object {
        // SharedPreferences键名
        private const val PREFS_NAME = "app_state_manager"
        private const val KEY_LAST_SHUTDOWN_TIME = "last_shutdown_time"
        private const val KEY_APP_START_TIME = "app_start_time"
        private const val KEY_IS_APP_RUNNING = "is_app_running"
        private const val KEY_LAST_BACKGROUND_TIME = "last_background_time"
        private const val KEY_PROCESSED_EXIT_TIMES = "processed_exit_times"
        
        // 异常关闭检测阈值（5分钟）
        private const val ABNORMAL_SHUTDOWN_THRESHOLD = 5 * 60 * 1000L
    }
    
    private val prefs: SharedPreferences by lazy {
        context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    }

    // 协程作用域用于异步操作
    private val scope = CoroutineScope(SupervisorJob() + Dispatchers.IO)

    // 内存中的当前状态
    @Volatile private var currentAppStartTime: Long = 0L
    @Volatile private var isInForeground: Boolean = false
    
    /**
     * 记录应用启动（Application.onCreate）
     */
    fun recordAppStart() {
        currentAppStartTime = System.currentTimeMillis()
        
        // 🎯 持久化启动时间和运行状态
        prefs.edit()
            .putLong(KEY_APP_START_TIME, currentAppStartTime)
            .putBoolean(KEY_IS_APP_RUNNING, true)
            .apply()
            
        Timber.d("🚀 [PersistentAppStateManager] 记录应用启动: ${Date(currentAppStartTime)}")

        // 🎯 异步检查上次是否正常关闭，计算未运行时长
        scope.launch {
            checkAndCalculateDowntime()
        }
    }
    
    /**
     * 记录应用进入前台
     */
    fun recordAppForeground() {
        isInForeground = true
        val foregroundTime = System.currentTimeMillis()
        
        // 更新启动时间为前台时间（更准确）
        currentAppStartTime = foregroundTime
        
        prefs.edit()
            .putLong(KEY_APP_START_TIME, foregroundTime)
            .apply()
            
        Timber.d("📱 [PersistentAppStateManager] 应用进入前台: ${Date(foregroundTime)}")
    }
    
    /**
     * 记录应用进入后台
     * 注意：这不是关闭，应用仍在后台运行，不计入未运行时长
     */
    fun recordAppBackground() {
        isInForeground = false
        val backgroundTime = System.currentTimeMillis()
        
        // 🎯 只记录后台时间，不改变运行状态
        prefs.edit()
            .putLong(KEY_LAST_BACKGROUND_TIME, backgroundTime)
            // 注意：保持 KEY_IS_APP_RUNNING = true，因为应用还在运行
            .apply()
            
        Timber.d("🌙 [PersistentAppStateManager] 应用进入后台（仍在运行，不计入未运行时长）: ${Date(backgroundTime)}")
    }
    
    /**
     * 记录应用完全关闭
     * 只有监控系统检测到真正的应用关闭时才调用此方法
     */
    suspend fun recordAppShutdown(shutdownTime: Long, reason: String): Boolean {
        // 检查是否已经处理过这个关闭时间
        if (isShutdownTimeProcessed(shutdownTime)) {
            Timber.d("[PersistentAppStateManager] 关闭时间已处理过，跳过: ${Date(shutdownTime)}")
            return false
        }
        
        // 🎯 持久化关闭时间和状态
        prefs.edit()
            .putLong(KEY_LAST_SHUTDOWN_TIME, shutdownTime)
            .putBoolean(KEY_IS_APP_RUNNING, false)
            .apply()
            
        // 标记这个关闭时间已处理
        markShutdownTimeProcessed(shutdownTime)
            
        Timber.d("🔴 [PersistentAppStateManager] 应用完全关闭: ${Date(shutdownTime)}, 原因: $reason")

        // 🎯 不在这里记录到数据库，让监控系统来记录，避免重复
        // 这里只负责持久化状态，实际的数据库记录由监控系统完成

        return true
    }
    
    /**
     * 检查并计算未运行时长（在应用启动时调用）
     */
    private suspend fun checkAndCalculateDowntime() {
        try {
            val wasRunning = prefs.getBoolean(KEY_IS_APP_RUNNING, false)
            val lastShutdownTime = prefs.getLong(KEY_LAST_SHUTDOWN_TIME, 0L)

            if (!wasRunning && lastShutdownTime > 0L) {
                // 🎯 检查是否已经处理过这个关闭时间，避免重复记录
                if (isShutdownTimeProcessed(lastShutdownTime)) {
                    Timber.d("📊 [PersistentAppStateManager] 正常关闭时间已处理过，跳过重复记录: ${Date(lastShutdownTime)}")
                    return
                }

                // 🎯 上次是正常关闭的，计算未运行时长
                val downtime = currentAppStartTime - lastShutdownTime

                if (downtime > 1000) { // 至少1秒才算有效
                    Timber.d("📊 [PersistentAppStateManager] 检测到正常关闭，未运行时长: ${downtime}ms (${downtime/1000}秒)")
                    Timber.d("  上次关闭时间: ${Date(lastShutdownTime)}")
                    Timber.d("  本次启动时间: ${Date(currentAppStartTime)}")

                    // 🎯 记录未运行时长到数据库
                    recordDowntimeToDatabase(lastShutdownTime, currentAppStartTime, downtime, "正常关闭")

                    // 🎯 标记这个关闭时间已处理，避免其他监控系统重复记录
                    markShutdownTimeProcessed(lastShutdownTime)
                }

            } else if (wasRunning) {
                // 🎯 上次是异常关闭的（应用被杀死，没有调用recordAppShutdown）
                val lastBackgroundTime = prefs.getLong(KEY_LAST_BACKGROUND_TIME, 0L)
                val lastActiveTime = maxOf(lastShutdownTime, lastBackgroundTime)

                if (lastActiveTime > 0L) {
                    // 🎯 检查是否已经处理过这个活跃时间，避免重复记录
                    if (isShutdownTimeProcessed(lastActiveTime)) {
                        Timber.d("⚠️ [PersistentAppStateManager] 异常关闭时间已处理过，跳过重复记录: ${Date(lastActiveTime)}")
                        return
                    }

                    val downtime = currentAppStartTime - lastActiveTime

                    if (downtime > ABNORMAL_SHUTDOWN_THRESHOLD) {
                        Timber.w("⚠️ [PersistentAppStateManager] 检测到异常关闭，未运行时长: ${downtime}ms (${downtime/1000}秒)")
                        Timber.w("  上次活跃时间: ${Date(lastActiveTime)}")
                        Timber.w("  本次启动时间: ${Date(currentAppStartTime)}")

                        // 🎯 记录异常关闭的未运行时长
                        recordDowntimeToDatabase(lastActiveTime, currentAppStartTime, downtime, "异常关闭")

                        // 🎯 标记这个活跃时间已处理，避免其他监控系统重复记录
                        markShutdownTimeProcessed(lastActiveTime)
                    } else {
                        Timber.d("📱 [PersistentAppStateManager] 应用快速重启，时长较短(${downtime}ms)，不记录为异常关闭")
                    }
                }
            } else {
                Timber.d("📱 [PersistentAppStateManager] 首次启动或无有效的上次关闭记录")
            }
            
        } catch (e: Exception) {
            Timber.e(e, "[PersistentAppStateManager] 检查未运行时长失败")
        }
    }
    
    /**
     * 记录未运行时长到数据库
     */
    private suspend fun recordDowntimeToDatabase(shutdownTime: Long, startupTime: Long, duration: Long, reason: String) {
        try {
            val exitReason = when {
                reason.contains("正常关闭") -> ExitReason.USER_EXIT
                reason.contains("异常关闭") -> ExitReason.UNKNOWN
                reason.contains("用户") -> ExitReason.USER_EXIT
                reason.contains("系统") -> ExitReason.SYSTEM_KILLED
                reason.contains("崩溃") -> ExitReason.APP_CRASH
                else -> ExitReason.UNKNOWN
            }
            
            repository.recordExit(
                exitTime = shutdownTime,
                exitReason = exitReason,
                description = "状态管理器检测: $reason",
                downtimeStart = shutdownTime,
                downtimeEnd = startupTime,
                downtimeDuration = duration
            )
            
            Timber.d("✅ [PersistentAppStateManager] 已记录未运行时长到数据库: ${duration}ms")
            
        } catch (e: Exception) {
            Timber.e(e, "[PersistentAppStateManager] 记录未运行时长到数据库失败")
        }
    }
    
    /**
     * 获取当前应用启动时间（供监控系统使用）
     */
    fun getAppStartTime(): Long = currentAppStartTime
    
    /**
     * 检查时间是否有效
     */
    fun isValidTime(): Boolean {
        return currentAppStartTime > 0L && currentAppStartTime <= System.currentTimeMillis()
    }
    
    // 🎯 防重复处理的机制
    private fun isShutdownTimeProcessed(shutdownTime: Long): Boolean {
        val processedTimes = prefs.getStringSet(KEY_PROCESSED_EXIT_TIMES, emptySet()) ?: emptySet()
        return processedTimes.contains(shutdownTime.toString())
    }
    
    private fun markShutdownTimeProcessed(shutdownTime: Long) {
        val processedTimes = prefs.getStringSet(KEY_PROCESSED_EXIT_TIMES, emptySet())?.toMutableSet() ?: mutableSetOf()
        processedTimes.add(shutdownTime.toString())
        
        // 只保留最近24小时的记录
        val cutoffTime = System.currentTimeMillis() - 24 * 60 * 60 * 1000L
        processedTimes.removeAll { it.toLongOrNull()?.let { time -> time < cutoffTime } == true }
        
        prefs.edit()
            .putStringSet(KEY_PROCESSED_EXIT_TIMES, processedTimes)
            .apply()
    }
    
    /**
     * 获取详细的调试信息
     */
    fun getDebugInfo(): String {
        return buildString {
            appendLine("=== PersistentAppStateManager 状态 ===")
            appendLine("当前应用启动时间: ${if (currentAppStartTime > 0) Date(currentAppStartTime) else "未设置"}")
            appendLine("是否在前台: $isInForeground")
            appendLine("时间有效性: ${isValidTime()}")
            
            appendLine("--- 持久化状态 ---")
            val persistedStartTime = prefs.getLong(KEY_APP_START_TIME, 0L)
            val persistedLastShutdown = prefs.getLong(KEY_LAST_SHUTDOWN_TIME, 0L)
            val persistedIsRunning = prefs.getBoolean(KEY_IS_APP_RUNNING, false)
            val persistedLastBackground = prefs.getLong(KEY_LAST_BACKGROUND_TIME, 0L)
            
            appendLine("持久化启动时间: ${if (persistedStartTime > 0) Date(persistedStartTime) else "未设置"}")
            appendLine("持久化关闭时间: ${if (persistedLastShutdown > 0) Date(persistedLastShutdown) else "未设置"}")
            appendLine("持久化后台时间: ${if (persistedLastBackground > 0) Date(persistedLastBackground) else "未设置"}")
            appendLine("持久化运行状态: $persistedIsRunning")
            
            val processedTimes = prefs.getStringSet(KEY_PROCESSED_EXIT_TIMES, emptySet()) ?: emptySet()
            appendLine("已处理的退出时间数量: ${processedTimes.size}")
        }
    }
}
