package com.shuyi.discipline.data.database.dao

import androidx.room.*
import com.shuyi.discipline.data.database.entity.RuntimeRecordEntity
import com.shuyi.discipline.data.repository.RecordType
import com.shuyi.discipline.domain.monitor.ExitReason
import kotlinx.coroutines.flow.Flow

/**
 * 运行时记录 DAO
 */
@Dao
interface RuntimeRecordDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRecord(record: RuntimeRecordEntity)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRecords(records: List<RuntimeRecordEntity>)

    @Update
    suspend fun updateRecord(record: RuntimeRecordEntity)

    @Delete
    suspend fun deleteRecord(record: RuntimeRecordEntity)

    @Query("SELECT * FROM runtime_records WHERE id = :id")
    suspend fun getRecordById(id: String): RuntimeRecordEntity?

    @Query("SELECT * FROM runtime_records ORDER BY timestamp DESC")
    fun getAllRecords(): Flow<List<RuntimeRecordEntity>>

    @Query("SELECT * FROM runtime_records WHERE timestamp BETWEEN :startTime AND :endTime ORDER BY timestamp DESC")
    suspend fun getRecordsInRange(startTime: Long, endTime: Long): List<RuntimeRecordEntity>

    @Query("SELECT * FROM runtime_records WHERE type = :type ORDER BY timestamp DESC")
    suspend fun getRecordsByType(type: RecordType): List<RuntimeRecordEntity>

    @Query("SELECT * FROM runtime_records WHERE type = :type AND timestamp BETWEEN :startTime AND :endTime ORDER BY timestamp DESC")
    suspend fun getRecordsByTypeInRange(type: RecordType, startTime: Long, endTime: Long): List<RuntimeRecordEntity>

    @Query("SELECT * FROM runtime_records ORDER BY timestamp DESC LIMIT :limit")
    suspend fun getRecentRecords(limit: Int): List<RuntimeRecordEntity>

    @Query("SELECT * FROM runtime_records WHERE session_id = :sessionId ORDER BY timestamp DESC")
    suspend fun getRecordsBySession(sessionId: String): List<RuntimeRecordEntity>

    @Query("SELECT * FROM runtime_records WHERE type = 'APP_EXIT' ORDER BY timestamp DESC LIMIT 1")
    suspend fun getLatestExitRecord(): RuntimeRecordEntity?

    @Query("SELECT * FROM runtime_records WHERE type = 'APP_EXIT' AND timestamp > :afterTime ORDER BY timestamp DESC LIMIT 1")
    suspend fun getExitRecordAfter(afterTime: Long): RuntimeRecordEntity?

    @Query("SELECT COUNT(*) > 0 FROM runtime_records WHERE type = 'APP_EXIT' AND timestamp > :afterTime")
    suspend fun hasExitRecordAfter(afterTime: Long): Boolean

    @Query("SELECT SUM(duration) FROM runtime_records WHERE type = 'DOWNTIME' AND timestamp BETWEEN :startTime AND :endTime")
    suspend fun getTotalDowntimeInRange(startTime: Long, endTime: Long): Long?

    @Query("SELECT COUNT(*) FROM runtime_records WHERE type = 'SESSION_START' AND timestamp BETWEEN :startTime AND :endTime")
    suspend fun getSessionCountInRange(startTime: Long, endTime: Long): Int

    @Query("SELECT AVG(duration) FROM runtime_records WHERE type = 'SESSION_END' AND timestamp BETWEEN :startTime AND :endTime")
    suspend fun getAverageSessionDurationInRange(startTime: Long, endTime: Long): Long?

    @Query("DELETE FROM runtime_records WHERE timestamp < :cutoffTime")
    suspend fun deleteOldRecords(cutoffTime: Long): Int

    @Query("DELETE FROM runtime_records")
    suspend fun deleteAllRecords()

    @Query("SELECT COUNT(*) FROM runtime_records")
    suspend fun getRecordCount(): Int

    @Query("SELECT MAX(timestamp) FROM runtime_records WHERE type = 'APP_EXIT'")
    suspend fun getLastExitTime(): Long?

    @Query("SELECT * FROM runtime_records WHERE exit_reason = :exitReason AND timestamp BETWEEN :startTime AND :endTime ORDER BY timestamp DESC")
    suspend fun getRecordsByExitReason(exitReason: ExitReason, startTime: Long, endTime: Long): List<RuntimeRecordEntity>

    @Query("""
        SELECT type, COUNT(*) as count, SUM(duration) as total_duration 
        FROM runtime_records 
        WHERE timestamp BETWEEN :startTime AND :endTime 
        GROUP BY type
    """)
    suspend fun getRecordStatistics(startTime: Long, endTime: Long): List<RecordStatistics>
}

/**
 * 记录统计数据类
 */
data class RecordStatistics(
    val type: RecordType,
    val count: Int,
    val total_duration: Long
)
