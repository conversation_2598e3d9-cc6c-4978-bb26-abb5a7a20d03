package com.shuyi.discipline.ui.screen.home

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material.icons.filled.History
import androidx.compose.material.icons.filled.Schedule
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Image
import androidx.compose.material.icons.filled.Dashboard
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Security
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.FloatingActionButton
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.NavigationBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Switch
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.compose.ui.platform.LocalLifecycleOwner
import com.shuyi.discipline.ui.components.BottomNavBar
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import coil.compose.AsyncImage
import coil.request.ImageRequest
import com.shuyi.discipline.ui.model.ScreenshotInfo
import com.shuyi.discipline.ui.model.ServiceStatus
import com.shuyi.discipline.ui.model.UiState
import com.shuyi.discipline.ui.navigation.Screen
import timber.log.Timber

/**
 * 首页屏幕
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    onNavigateToConfig: () -> Unit,
    onNavigateToQuietPeriod: () -> Unit,
    onNavigateToReportList: () -> Unit,
    onNavigateToScreenshots: () -> Unit = {},
    onNavigateToPermissions: () -> Unit = {},
    viewModel: HomeViewModel = hiltViewModel(),
    navController: NavController? = null
) {
    val todayScreenshotsState by viewModel.todayScreenshots.collectAsState()
    val serviceStatusState by viewModel.serviceStatus.collectAsState()
    val batteryOptimizationIgnored by viewModel.batteryOptimizationIgnored.collectAsState()
    val scrollState = rememberScrollState()
    var selectedTabIndex by remember { mutableStateOf(0) }

    val context = LocalContext.current

    // 监听页面生命周期，确保返回首页时刷新状态
    val lifecycleOwner = LocalLifecycleOwner.current
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            when (event) {
                Lifecycle.Event.ON_RESUME -> {
                    Timber.d("HomeScreen ON_RESUME - 刷新服务状态和截图列表")
                    // 页面可见时刷新状态，确保权限撤销后的状态同步
                    viewModel.loadServiceStatus()
                    viewModel.loadTodayScreenshots()
                }
                else -> {}
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    LaunchedEffect(key1 = Unit) {
        Timber.d("HomeScreen LaunchedEffect被调用")
        viewModel.loadTodayScreenshots()
        viewModel.loadServiceStatus()
    }

    Scaffold(
        containerColor = MaterialTheme.colorScheme.background,
        bottomBar = {
            BottomNavBar(
                selectedIndex = selectedTabIndex,
                onItemSelected = { index ->
                    selectedTabIndex = index
                    // 根据选中的标签执行相应的导航
                    when(index) {
                        // 0是首页，已经在此页面，无需导航
                        1 -> onNavigateToScreenshots()
                        2 -> onNavigateToReportList()
                        3 -> navController?.navigate(Screen.Status.route) {
                            // 避免创建多个实例
                            launchSingleTop = true
                        }
                        4 -> onNavigateToConfig()
                    }
                },
                onNavigateToHome = { /* 已在首页，无需导航 */ },
                onNavigateToScreenshots = onNavigateToScreenshots,
                onNavigateToCollages = onNavigateToReportList,
                onNavigateToStatus = {
                    navController?.navigate(Screen.Status.route) {
                        // 避免创建多个实例
                        launchSingleTop = true
                    }
                },
                onNavigateToConfig = onNavigateToConfig
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(MaterialTheme.colorScheme.background)
        ) {
            // 内容区域 - 直接从顶部开始，不再使用StatusBar
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp, vertical = 8.dp) // 减少顶部padding
                    .verticalScroll(scrollState),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 标题和权限按钮
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "自动截屏",
                        style = MaterialTheme.typography.headlineMedium
                    )

                    // 权限按钮 - 移到右侧
                    IconButton(
                        onClick = { onNavigateToPermissions() },
                        modifier = Modifier
                            .size(40.dp)
                            .clip(CircleShape)
                            .background(MaterialTheme.colorScheme.primaryContainer)
                            .wrapContentSize(Alignment.Center)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Security,
                            contentDescription = "权限管理",
                            tint = MaterialTheme.colorScheme.onPrimaryContainer,
                            modifier = Modifier.size(24.dp)
                        )
                    }
                }

                // 开启自动截屏开关 - 单独一行
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = 8.dp),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "开启自动截屏",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )

                    Switch(
                        checked = serviceStatusState == ServiceStatus.Running,
                        onCheckedChange = { viewModel.toggleServiceStatus() }
                    )
                }

                // 当前状态卡片
                CurrentStatusCard(
                    serviceStatusState = serviceStatusState,
                    batteryOptimizationIgnored = batteryOptimizationIgnored,
                    screenshotsCount = if (todayScreenshotsState is UiState.Success<List<ScreenshotInfo>>) {
                        (todayScreenshotsState as UiState.Success<List<ScreenshotInfo>>).data.size
                    } else {
                        0
                    },
                    onRequestIgnoreBatteryOptimization = { viewModel.requestIgnoreBatteryOptimization() }
                )

                // 24小时状态卡片
                DailyStatusCard()

                // 隐藏的功能按钮 - 使用不可见的卡片保留功能
                Card(
                    onClick = { viewModel.refreshTodayScreenshots() },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(0.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
                ) {
                    Box {}
                }

                Card(
                    onClick = { viewModel.takeTestScreenshot() },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(0.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
                ) {
                    Box {}
                }

                Card(
                    onClick = { viewModel.triggerActualScreenshot() },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(0.dp),
                    elevation = CardDefaults.cardElevation(defaultElevation = 0.dp)
                ) {
                    Box {}
                }

                // 今日截图部分 - 隐藏但保留功能
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(0.dp)
                ) {
                    TodayScreenshotsSection(todayScreenshotsState)
                }

                // 操作按钮 - 隐藏但保留功能
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(0.dp)
                ) {
                    ActionButtons(
                        onQuietPeriodClick = onNavigateToQuietPeriod,
                        onReportClick = onNavigateToReportList
                    )
                }

                // 底部空间，确保内容不被底部导航栏遮挡
                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }
}

/**
 * 服务状态指示器
 */
@Composable
fun ServiceStatusIndicator(status: ServiceStatus) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .size(16.dp)
                    .clip(CircleShape)
                    .background(
                        when (status) {
                            ServiceStatus.Running -> MaterialTheme.colorScheme.secondary
                            ServiceStatus.Stopped -> MaterialTheme.colorScheme.error
                            ServiceStatus.Unauthorized -> Color(0xFFFFA000)
                            is ServiceStatus.Error -> Color(0xFFFFA000)
                        }
                    )
            )

            Text(
                text = when (status) {
                    ServiceStatus.Running -> "服务正在运行"
                    ServiceStatus.Stopped -> "服务已停止"
                    ServiceStatus.Unauthorized -> "服务未授权"
                    is ServiceStatus.Error -> "服务错误: ${status.message}"
                },
                modifier = Modifier.padding(start = 16.dp)
            )
        }
    }
}

/**
 * 今日截图部分
 */
@Composable
fun TodayScreenshotsSection(state: UiState<List<ScreenshotInfo>>) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Text(
                text = "今日截图",
                style = MaterialTheme.typography.titleMedium
            )

            Spacer(modifier = Modifier.height(8.dp))

            when (state) {
                is UiState.Loading -> {
                    Text("加载中...")
                }
                is UiState.Empty -> {
                    Text("今天还没有截图记录")
                }
                is UiState.Error -> {
                    Text("加载错误: ${state.message}")
                }
                is UiState.Success -> {
                    LazyVerticalGrid(
                        columns = GridCells.Fixed(3),
                        contentPadding = PaddingValues(4.dp),
                        modifier = Modifier.height(200.dp)
                    ) {
                        items(state.data) { screenshot ->
                            ScreenshotItem(screenshot)
                        }
                    }
                }
                else -> { // 处理其他可能的状态，如Idle
                    Text("等待加载...")
                }
            }
        }
    }
}

/**
 * 截图项
 */
@Composable
fun ScreenshotItem(screenshot: ScreenshotInfo) {
    Box(
        modifier = Modifier
            .padding(4.dp)
            .clip(RoundedCornerShape(4.dp))
    ) {
        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current)
                .data(screenshot.filePath)
                .crossfade(true)
                .build(),
            contentDescription = "截图",
            contentScale = ContentScale.Crop,
            modifier = Modifier.fillMaxSize()
        )
    }
}

/**
 * 操作按钮
 */
@Composable
fun ActionButtons(
    onQuietPeriodClick: () -> Unit,
    onReportClick: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        ActionButton(
            title = "免打扰时段",
            icon = Icons.Filled.Schedule,
            onClick = onQuietPeriodClick,
            modifier = Modifier.weight(1f)
        )

        ActionButton(
            title = "查看拼图",
            icon = Icons.Filled.Dashboard,
            onClick = onReportClick,
            modifier = Modifier.weight(1f)
        )
    }
}

/**
 * 操作按钮项
 */
@Composable
fun ActionButton(
    title: String,
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        onClick = onClick,
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp)
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            Icon(
                imageVector = icon,
                contentDescription = title,
                modifier = Modifier.size(32.dp)
            )

            Spacer(modifier = Modifier.height(8.dp))

            Text(
                text = title,
                textAlign = TextAlign.Center
            )
        }
    }
}

// 移除StatusBar函数，不再需要

/**
 * 当前状态卡片
 */
@Composable
fun CurrentStatusCard(
    serviceStatusState: ServiceStatus,
    batteryOptimizationIgnored: Boolean,
    screenshotsCount: Int,
    onRequestIgnoreBatteryOptimization: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            // 标题和状态
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "当前状态",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.SemiBold
                )

                Box(
                    modifier = Modifier
                        .clip(RoundedCornerShape(50))
                        .background(
                            when (serviceStatusState) {
                                ServiceStatus.Running -> MaterialTheme.colorScheme.secondaryContainer
                                else -> MaterialTheme.colorScheme.errorContainer
                            }
                        )
                        .padding(horizontal = 12.dp, vertical = 4.dp)
                ) {
                    Text(
                        text = when (serviceStatusState) {
                            ServiceStatus.Running -> "正在运行"
                            else -> "已停止"
                        },
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium,
                        color = when (serviceStatusState) {
                            ServiceStatus.Running -> MaterialTheme.colorScheme.onSecondaryContainer
                            else -> MaterialTheme.colorScheme.onErrorContainer
                        }
                    )
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 状态指示器
            StatusIndicatorItem(
                text = "后台服务正常",
                isActive = serviceStatusState == ServiceStatus.Running,
                color = if (serviceStatusState == ServiceStatus.Running) MaterialTheme.colorScheme.secondary else MaterialTheme.colorScheme.error
            )

            Spacer(modifier = Modifier.height(12.dp))

            StatusIndicatorItem(
                text = "截屏功能已开启",
                isActive = serviceStatusState == ServiceStatus.Running,
                color = if (serviceStatusState == ServiceStatus.Running) MaterialTheme.colorScheme.secondary else com.shuyi.discipline.ui.theme.Orange
            )

            Spacer(modifier = Modifier.height(12.dp))

            StatusIndicatorItem(
                text = "所有权限已授予",
                isActive = serviceStatusState != ServiceStatus.Unauthorized,
                color = if (serviceStatusState != ServiceStatus.Unauthorized) MaterialTheme.colorScheme.secondary else com.shuyi.discipline.ui.theme.Orange
            )

            Spacer(modifier = Modifier.height(12.dp))

            // 电池优化状态
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                StatusIndicatorItem(
                    text = "电池优化已忽略",
                    isActive = batteryOptimizationIgnored,
                    color = if (batteryOptimizationIgnored) MaterialTheme.colorScheme.secondary else com.shuyi.discipline.ui.theme.Orange
                )

                if (!batteryOptimizationIgnored) {
                    Card(
                        onClick = onRequestIgnoreBatteryOptimization,
                        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.primary),
                        shape = RoundedCornerShape(50),
                        modifier = Modifier.padding(start = 8.dp)
                    ) {
                        Text(
                            text = "请求忽略",
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium,
                            color = MaterialTheme.colorScheme.onPrimary,
                            modifier = Modifier.padding(horizontal = 12.dp, vertical = 4.dp)
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 今日已截图进度
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "今日已截图",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSecondaryContainer
                )
                Text(
                    text = "$screenshotsCount / 144",
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSecondaryContainer
                )
            }

            Spacer(modifier = Modifier.height(4.dp))

            // 进度条
            LinearProgressIndicator(
                progress = { screenshotsCount.toFloat() / 144f },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(8.dp)
                    .clip(RoundedCornerShape(4.dp)),
                color = MaterialTheme.colorScheme.primary,
                trackColor = MaterialTheme.colorScheme.outline
            )
        }
    }
}

/**
 * 状态指示器项
 */
@Composable
fun StatusIndicatorItem(text: String, isActive: Boolean, color: Color) {
    Row(
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .size(12.dp)
                .clip(CircleShape)
                .background(color)
        )

        Spacer(modifier = Modifier.width(8.dp))

        Text(
            text = text,
            fontSize = 14.sp
        )
    }
}

/**
 * 24小时状态卡片
 */
@Composable
fun DailyStatusCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(16.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = "24小时状态",
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold
            )

            Spacer(modifier = Modifier.height(16.dp))

            // 后台未运行时长
            Column {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Box(
                            modifier = Modifier
                                .size(12.dp)
                                .clip(CircleShape)
                                .background(MaterialTheme.colorScheme.error)
                                .padding(end = 8.dp)
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        Text(
                            text = "后台未运行时长",
                            fontSize = 14.sp
                        )
                    }

                    Text(
                        text = "48分钟",
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.error
                    )
                }

                Spacer(modifier = Modifier.height(8.dp))

                // 进度条
                LinearProgressIndicator(
                    progress = { 0.033f },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(8.dp)
                        .clip(RoundedCornerShape(4.dp)),
                    color = MaterialTheme.colorScheme.error,
                    trackColor = MaterialTheme.colorScheme.outline
                )

                Text(
                    text = "占比: 3.3%",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onSecondaryContainer,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 4.dp),
                    textAlign = TextAlign.End
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // 截图功能未开启时长
            Column {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Box(
                            modifier = Modifier
                                .size(12.dp)
                                .clip(CircleShape)
                                .background(com.shuyi.discipline.ui.theme.Orange)
                                .padding(end = 8.dp)
                        )

                        Spacer(modifier = Modifier.width(8.dp))

                        Text(
                            text = "截图功能未开启时长",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }

                    Text(
                        text = "1小时12分钟",
                        style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium),
                        color = com.shuyi.discipline.ui.theme.Orange
                    )
                }

                Spacer(modifier = Modifier.height(8.dp))

                // 进度条
                LinearProgressIndicator(
                    progress = { 0.05f },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(8.dp)
                        .clip(RoundedCornerShape(4.dp)),
                    color = com.shuyi.discipline.ui.theme.Orange,
                    trackColor = MaterialTheme.colorScheme.outline
                )

                Text(
                    text = "占比: 5%",
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSecondaryContainer,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(top = 4.dp),
                    textAlign = TextAlign.End
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun HomeScreenPreview() {
    MaterialTheme {
        HomeScreen(
            onNavigateToConfig = {},
            onNavigateToQuietPeriod = {},
            onNavigateToReportList = {},
            onNavigateToScreenshots = {},
            onNavigateToPermissions = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
fun CurrentStatusCardPreview() {
    MaterialTheme {
        CurrentStatusCard(
            serviceStatusState = ServiceStatus.Running,
            batteryOptimizationIgnored = false,
            screenshotsCount = 42,
            onRequestIgnoreBatteryOptimization = {}
        )
    }
}