package com.shuyi.discipline.data.database.dao

import androidx.room.*
import com.shuyi.discipline.data.database.entity.RuntimeMetadataEntity

/**
 * 运行时元数据 DAO
 */
@Dao
interface RuntimeMetadataDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertMetadata(metadata: RuntimeMetadataEntity)

    @Update
    suspend fun updateMetadata(metadata: RuntimeMetadataEntity)

    @Delete
    suspend fun deleteMetadata(metadata: RuntimeMetadataEntity)

    @Query("SELECT * FROM runtime_metadata WHERE key = :key")
    suspend fun getMetadata(key: String): RuntimeMetadataEntity?

    @Query("SELECT value FROM runtime_metadata WHERE key = :key")
    suspend fun getValue(key: String): String?

    @Query("SELECT * FROM runtime_metadata ORDER BY updated_at DESC")
    suspend fun getAllMetadata(): List<RuntimeMetadataEntity>

    @Query("INSERT OR REPLACE INTO runtime_metadata (key, value, updated_at) VALUES (:key, :value, :updatedAt)")
    suspend fun setValue(key: String, value: String, updatedAt: Long = System.currentTimeMillis())

    @Query("DELETE FROM runtime_metadata WHERE key = :key")
    suspend fun deleteByKey(key: String)

    @Query("DELETE FROM runtime_metadata")
    suspend fun deleteAllMetadata()

    // 便捷方法：获取长整型值
    suspend fun getLongValue(key: String): Long? {
        return getValue(key)?.toLongOrNull()
    }

    // 便捷方法：设置长整型值
    suspend fun setLongValue(key: String, value: Long) {
        setValue(key, value.toString())
    }

    // 便捷方法：获取布尔值
    suspend fun getBooleanValue(key: String): Boolean? {
        return getValue(key)?.toBooleanStrictOrNull()
    }

    // 便捷方法：设置布尔值
    suspend fun setBooleanValue(key: String, value: Boolean) {
        setValue(key, value.toString())
    }

    // 便捷方法：获取整型值
    suspend fun getIntValue(key: String): Int? {
        return getValue(key)?.toIntOrNull()
    }

    // 便捷方法：设置整型值
    suspend fun setIntValue(key: String, value: Int) {
        setValue(key, value.toString())
    }
}
