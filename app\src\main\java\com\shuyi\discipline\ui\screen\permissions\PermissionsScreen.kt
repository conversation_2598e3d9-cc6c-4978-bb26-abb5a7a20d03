package com.shuyi.discipline.ui.screen.permissions

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material.icons.filled.Info
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import android.widget.Toast
import com.shuyi.discipline.ui.components.PageTitleBar
import timber.log.Timber

/**
 * 权限加固页面
 */
@Composable
fun PermissionsScreen(
    onNavigateToHome: () -> Unit,
    onNavigateToScreenshots: () -> Unit,
    onNavigateToCollages: () -> Unit,
    onNavigateToStatus: () -> Unit,
    onNavigateToConfig: () -> Unit,
    viewModel: PermissionsViewModel = hiltViewModel()
) {
    // 获取权限状态
    val permissionsState by viewModel.permissionsState.collectAsState()

    // 获取上下文
    val context = LocalContext.current



    // 悬浮窗权限设置启动器
    val overlaySettingsLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) {
        // 刷新权限状态
        viewModel.refreshPermissionsState()

        // 延迟1000ms后再次刷新，确保系统设置更新已生效
        Handler(Looper.getMainLooper()).postDelayed({
            viewModel.refreshPermissionsState()
            Timber.d("延迟刷新悬浮窗权限状态")
        }, 1000)
    }

    // 无障碍服务设置启动器
    val accessibilitySettingsLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) {
        // 立即刷新权限状态
        viewModel.refreshPermissionsState()

        // 延迟1000ms后再次刷新，确保系统设置更新已生效
        Handler(Looper.getMainLooper()).postDelayed({
            viewModel.refreshPermissionsState()
            Timber.d("延迟刷新无障碍服务权限状态")
        }, 1000)
    }

    // 自启动设置启动器
    val autoStartSettingsLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) {
        // 刷新权限状态
        viewModel.refreshPermissionsState()
    }

    // 存储权限设置启动器
    val storageSettingsLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) {
        // 刷新权限状态
        viewModel.refreshPermissionsState()
    }

    // 通知权限设置启动器
    val notificationSettingsLauncher = rememberLauncherForActivityResult(
        contract = ActivityResultContracts.StartActivityForResult()
    ) {
        // 立即刷新权限状态
        viewModel.refreshPermissionsState()

        // 延迟1000ms后再次刷新，确保系统设置更新已生效
        Handler(Looper.getMainLooper()).postDelayed({
            viewModel.refreshPermissionsState()
            Timber.d("延迟刷新通知权限状态")
        }, 1000)
    }

    // 启动时刷新权限状态
    LaunchedEffect(key1 = Unit) {
        viewModel.refreshPermissionsState()
    }

    Scaffold(
        containerColor = MaterialTheme.colorScheme.background
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(MaterialTheme.colorScheme.background)
                .verticalScroll(rememberScrollState())
                .padding(horizontal = 16.dp)
        ) {
            // 标题栏
            PageTitleBar(
                title = "权限管理",
                showBackButton = true,
                onBackClick = onNavigateToHome
            )

            // 必要权限
            PermissionCategoryCard(
                title = "必要权限",
                permissions = listOf(
                    PermissionItem(
                        title = "存储空间",
                        description = "允许应用保存截图到设备",
                        isGranted = permissionsState.hasStoragePermission,
                        onClick = {
                            try {
                                storageSettingsLauncher.launch(viewModel.getStorageSettingsIntent())
                            } catch (e: Exception) {
                                Timber.e(e, "启动存储权限设置失败")
                            }
                        }
                    ),
                    PermissionItem(
                        title = "通知权限",
                        description = "允许应用发送前台服务通知",
                        isGranted = permissionsState.hasNotificationPermission,
                        onClick = {
                            try {
                                // 使用正确的通知权限设置Intent
                                val intent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                                    Intent(Settings.ACTION_APP_NOTIFICATION_SETTINGS).apply {
                                        putExtra(Settings.EXTRA_APP_PACKAGE, context.packageName)
                                    }
                                } else {
                                    Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                                        data = Uri.parse("package:${context.packageName}")
                                    }
                                }
                                notificationSettingsLauncher.launch(intent)
                            } catch (e: Exception) {
                                Timber.e(e, "启动通知权限设置失败")
                            }
                        }
                    )
                )
            )

            // 高级权限
            PermissionCategoryCard(
                title = "高级权限",
                permissions = listOf(
                    PermissionItem(
                        title = "无障碍服务",
                        description = "允许应用在特定场景下自动操作",
                        isGranted = permissionsState.hasAccessibilityPermission,
                        onClick = {
                            try {
                                accessibilitySettingsLauncher.launch(viewModel.getAccessibilitySettingsIntent())
                            } catch (e: Exception) {
                                Timber.e(e, "启动无障碍服务设置失败")
                            }
                        }
                    ),
                    PermissionItem(
                        title = "悬浮窗",
                        description = "允许应用在其他应用上层显示内容",
                        isGranted = permissionsState.hasOverlayPermission,
                        onClick = {
                            try {
                                // 使用正确的悬浮窗权限设置Intent
                                val intent = viewModel.getOverlaySettingsIntent()
                                Timber.d("启动悬浮窗权限设置: ${intent.data}")
                                overlaySettingsLauncher.launch(intent)

                                // 提示用户在系统设置中查找应用
                                val appName = context.applicationInfo.loadLabel(context.packageManager)
                                Toast.makeText(
                                    context,
                                    "请在系统设置中找到\"$appName\"并允许其显示在其他应用上层",
                                    Toast.LENGTH_LONG
                                ).show()
                            } catch (e: Exception) {
                                Timber.e(e, "启动悬浮窗设置失败: ${e.message}")
                                Toast.makeText(context, "无法打开悬浮窗设置: ${e.message}", Toast.LENGTH_SHORT).show()
                            }
                        }
                    ),
                    PermissionItem(
                        title = "自动启动",
                        description = "允许应用在设备启动时自动运行",
                        isGranted = permissionsState.hasAutoStartPermission,
                        onClick = {
                            try {
                                autoStartSettingsLauncher.launch(viewModel.getAutoStartSettingsIntent())
                            } catch (e: Exception) {
                                Timber.e(e, "启动自启动设置失败")
                            }
                        }
                    ),
                    PermissionItem(
                        title = "电池优化排除",
                        description = "允许应用不受电池优化限制",
                        isGranted = permissionsState.hasBatteryOptimizationIgnored,
                        onClick = { viewModel.requestIgnoreBatteryOptimization() }
                    )
                )
            )

            // 权限问题排查
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 8.dp),
                colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface),
                elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
                shape = RoundedCornerShape(16.dp)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "权限问题排查",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.SemiBold,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )

                    if (!permissionsState.allPermissionsGranted) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clip(RoundedCornerShape(8.dp))
                                .background(MaterialTheme.colorScheme.errorContainer.copy(alpha = 0.3f))
                                .padding(12.dp)
                                .padding(bottom = 16.dp)
                        ) {
                            Row(
                                verticalAlignment = Alignment.Top
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Warning,
                                    contentDescription = "警告",
                                    tint = com.shuyi.discipline.ui.theme.Orange,
                                    modifier = Modifier
                                        .padding(top = 2.dp)
                                        .size(20.dp)
                                )
                                Spacer(modifier = Modifier.width(12.dp))
                                Column {
                                    Text(
                                        text = "检测到权限问题",
                                        color = MaterialTheme.colorScheme.onErrorContainer,
                                        fontWeight = FontWeight.Medium
                                    )
                                    Spacer(modifier = Modifier.height(4.dp))
                                    Text(
                                        text = "部分权限未授予，可能导致应用无法正常工作。请点击下方按钮一键修复。",
                                        color = MaterialTheme.colorScheme.onErrorContainer,
                                        style = MaterialTheme.typography.bodyMedium
                                    )
                                }
                            }
                        }
                    } else {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clip(RoundedCornerShape(8.dp))
                                .background(Color(0xFFE8F5E9))
                                .padding(12.dp)
                                .padding(bottom = 16.dp)
                        ) {
                            Row(
                                verticalAlignment = Alignment.Top
                            ) {
                                Icon(
                                    imageVector = Icons.Default.Info,
                                    contentDescription = "信息",
                                    tint = Color(0xFF2E7D32),
                                    modifier = Modifier
                                        .padding(top = 2.dp)
                                        .size(20.dp)
                                )
                                Spacer(modifier = Modifier.width(12.dp))
                                Column {
                                    Text(
                                        text = "所有权限已授予",
                                        color = Color(0xFF2E7D32),
                                        fontWeight = FontWeight.Medium
                                    )
                                    Spacer(modifier = Modifier.height(4.dp))
                                    Text(
                                        text = "应用已获得所有需要的权限，可以正常工作。",
                                        color = Color(0xFF388E3C),
                                        fontSize = 14.sp
                                    )
                                }
                            }
                        }
                    }

                    Button(
                        onClick = { viewModel.requestAllPermissions() },
                        modifier = Modifier.fillMaxWidth(),
                        colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.primary),
                        shape = RoundedCornerShape(8.dp),
                        enabled = !permissionsState.allPermissionsGranted
                    ) {
                        Text(
                            text = "一键授予所有权限",
                            modifier = Modifier.padding(vertical = 8.dp),
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }



            // 底部空间，确保内容不被底部导航栏遮挡
            Spacer(modifier = Modifier.height(16.dp))
        }
    }
}

/**
 * 权限类别卡片
 */
@Composable
fun PermissionCategoryCard(
    title: String,
    permissions: List<PermissionItem>
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.surface),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        shape = RoundedCornerShape(16.dp)
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Text(
                text = title,
                style = MaterialTheme.typography.titleLarge,
                modifier = Modifier.padding(bottom = 16.dp)
            )

            permissions.forEachIndexed { index, item ->
                PermissionItemRow(
                    title = item.title,
                    description = item.description,
                    isGranted = item.isGranted,
                    onClick = item.onClick,
                    isLast = index == permissions.size - 1
                )
            }
        }
    }
}

/**
 * 权限项数据类
 */
data class PermissionItem(
    val title: String,
    val description: String,
    val isGranted: Boolean,
    val onClick: () -> Unit
)

/**
 * 权限项行
 */
@Composable
fun PermissionItemRow(
    title: String,
    description: String,
    isGranted: Boolean,
    onClick: () -> Unit,
    isLast: Boolean = false
) {
    Column {
        // 整行可点击
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable(
                    onClick = onClick,
                    indication = rememberRipple(),
                    interactionSource = remember { MutableInteractionSource() }
                )
                .padding(vertical = 12.dp, horizontal = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier
                    .weight(1f)
            ) {
                Text(
                    text = title,
                    style = MaterialTheme.typography.titleMedium
                )
                Text(
                    text = description,
                    color = MaterialTheme.colorScheme.onSecondaryContainer,
                    style = MaterialTheme.typography.bodyMedium
                )
            }

            // 状态标签和箭头图标
            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Box(
                    modifier = Modifier
                        .clip(RoundedCornerShape(50))
                        .background(if (isGranted) MaterialTheme.colorScheme.secondaryContainer else MaterialTheme.colorScheme.errorContainer)
                        .padding(horizontal = 8.dp, vertical = 4.dp)
                ) {
                    Text(
                        text = if (isGranted) "已授权" else "未授权",
                        color = if (isGranted) MaterialTheme.colorScheme.onSecondaryContainer else MaterialTheme.colorScheme.onErrorContainer,
                        style = MaterialTheme.typography.bodySmall.copy(fontWeight = FontWeight.Medium)
                    )
                }
                Spacer(modifier = Modifier.width(8.dp))
                Box(
                    modifier = Modifier
                        .size(36.dp)
                        .clip(CircleShape)
                        .background(MaterialTheme.colorScheme.surfaceVariant)
                        .padding(10.dp)
                ) {
                    Icon(
                        imageVector = Icons.Default.ArrowForward,
                        contentDescription = "前往设置",
                        tint = MaterialTheme.colorScheme.onSurfaceVariant,
                        modifier = Modifier.size(16.dp)
                    )
                }
            }
        }
        if (!isLast) {
            Divider(color = MaterialTheme.colorScheme.outline)
        }
    }
}


