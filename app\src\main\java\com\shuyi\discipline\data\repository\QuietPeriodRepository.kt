package com.shuyi.discipline.data.repository

import com.shuyi.discipline.data.model.QuietPeriod
import kotlinx.coroutines.flow.Flow
import java.util.Calendar

/**
 * 免打扰时段仓库接口
 */
interface QuietPeriodRepository {
    /**
     * 获取所有免打扰时段
     */
    fun getAllQuietPeriods(): Flow<List<QuietPeriod>>
    
    /**
     * 保存免打扰时段
     */
    suspend fun saveQuietPeriod(quietPeriod: QuietPeriod): Long
    
    /**
     * 删除免打扰时段
     */
    suspend fun deleteQuietPeriod(id: Int): Int
    
    /**
     * 更新免打扰时段
     */
    suspend fun updateQuietPeriod(quietPeriod: QuietPeriod): Int
    
    /**
     * 判断当前时间是否在免打扰时段内
     */
    suspend fun isInQuietPeriod(calendar: Calendar = Calendar.getInstance()): Bo<PERSON>an
} 