package com.shuyi.discipline.data.repository.impl

import com.shuyi.discipline.data.model.DeduplicationStats
import com.shuyi.discipline.data.model.ScreenshotDeduplication
import com.shuyi.discipline.data.repository.ScreenshotDeduplicationRepository
import com.shuyi.discipline.data.source.database.ScreenshotDeduplicationDao
import com.shuyi.discipline.utils.SensitivityLevel
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 截图去重仓库实现类
 */
@Singleton
class ScreenshotDeduplicationRepositoryImpl @Inject constructor(
    private val deduplicationDao: ScreenshotDeduplicationDao
) : ScreenshotDeduplicationRepository {
    
    override fun getDeduplication(): Flow<ScreenshotDeduplication> {
        return deduplicationDao.getDeduplication().map { 
            it ?: createDefaultDeduplication()
        }
    }
    
    override suspend fun getDeduplicationSync(): ScreenshotDeduplication {
        return deduplicationDao.getDeduplicationSync() ?: createDefaultDeduplication().also {
            // 如果数据库中没有记录，创建默认记录
            saveDeduplication(it)
        }
    }
    
    override suspend fun saveDeduplication(deduplication: ScreenshotDeduplication) {
        try {
            deduplicationDao.insertDeduplication(deduplication)
            Timber.d("保存去重设置成功")
        } catch (e: Exception) {
            Timber.e(e, "保存去重设置失败")
        }
    }
    
    override suspend fun updateLastScreenshotHash(hash: Long, path: String) {
        try {
            deduplicationDao.updateLastScreenshotHash(hash, path, System.currentTimeMillis())
            Timber.d("更新上一张截图哈希值成功: hash=$hash, path=$path")
        } catch (e: Exception) {
            Timber.e(e, "更新上一张截图哈希值失败")
        }
    }
    
    override suspend fun updateDuplicateStats(spaceSavedMb: Double) {
        try {
            deduplicationDao.updateDuplicateStats(spaceSavedMb, System.currentTimeMillis())
            Timber.d("更新重复统计信息成功: spaceSaved=${spaceSavedMb}MB")
        } catch (e: Exception) {
            Timber.e(e, "更新重复统计信息失败")
        }
    }
    
    override suspend fun updateProcessedCount() {
        try {
            deduplicationDao.updateProcessedCount(System.currentTimeMillis())
            Timber.d("更新处理计数成功")
        } catch (e: Exception) {
            Timber.e(e, "更新处理计数失败")
        }
    }
    
    override suspend fun updateEnabled(enabled: Boolean) {
        try {
            deduplicationDao.updateEnabled(enabled, System.currentTimeMillis())
            Timber.d("更新去重功能启用状态成功: enabled=$enabled")
        } catch (e: Exception) {
            Timber.e(e, "更新去重功能启用状态失败")
        }
    }
    
    override suspend fun updateSensitivityLevel(level: SensitivityLevel) {
        try {
            deduplicationDao.updateSensitivityLevel(level.threshold, System.currentTimeMillis())
            Timber.d("更新敏感度级别成功: level=${level.displayName}")
        } catch (e: Exception) {
            Timber.e(e, "更新敏感度级别失败")
        }
    }
    
    override suspend fun resetStats() {
        try {
            deduplicationDao.resetStats(System.currentTimeMillis())
            Timber.d("重置统计信息成功")
        } catch (e: Exception) {
            Timber.e(e, "重置统计信息失败")
        }
    }
    
    override suspend fun getLastScreenshotHash(): Long {
        return try {
            deduplicationDao.getLastScreenshotHash() ?: 0L
        } catch (e: Exception) {
            Timber.e(e, "获取上一张截图哈希值失败")
            0L
        }
    }
    
    override suspend fun isDeduplicationEnabled(): Boolean {
        return try {
            deduplicationDao.isDeduplicationEnabled() ?: true
        } catch (e: Exception) {
            Timber.e(e, "获取去重功能启用状态失败")
            true // 默认启用
        }
    }
    
    override suspend fun getSensitivityLevel(): SensitivityLevel {
        return try {
            val threshold = deduplicationDao.getSensitivityLevel() ?: SensitivityLevel.MEDIUM.threshold
            SensitivityLevel.fromThreshold(threshold)
        } catch (e: Exception) {
            Timber.e(e, "获取敏感度级别失败")
            SensitivityLevel.MEDIUM
        }
    }
    
    override suspend fun getDeduplicationStats(): DeduplicationStats {
        return try {
            val deduplication = getDeduplicationSync()
            DeduplicationStats.fromDeduplication(deduplication)
        } catch (e: Exception) {
            Timber.e(e, "获取去重统计信息失败")
            DeduplicationStats()
        }
    }
    
    /**
     * 创建默认的去重设置
     */
    private fun createDefaultDeduplication(): ScreenshotDeduplication {
        return ScreenshotDeduplication(
            id = 1,
            lastScreenshotHash = 0L,
            lastScreenshotPath = "",
            isEnabled = true,
            sensitivityLevel = SensitivityLevel.MEDIUM.threshold,
            duplicateCount = 0,
            spaceSavedMb = 0.0,
            lastUpdatedTime = System.currentTimeMillis(),
            totalProcessedCount = 0
        )
    }
}
