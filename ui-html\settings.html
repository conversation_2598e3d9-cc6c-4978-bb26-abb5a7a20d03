<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置 - 自动截屏</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="mobile-container">
        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-bar-time">9:41</div>
            <div class="status-bar-icons">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 10a6 6 0 0 0-12 0v11h12V10z"/><path d="M9 2v1"/><path d="M15 2v1"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 2h12a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2z"/><path d="M5 10h14"/><path d="M9 16h6"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 4h14a1 1 0 0 1 1 1v14a1 1 0 0 1-1 1H5a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1z"/><path d="M4 8h16"/><path d="M8 4v16"/></svg>
            </div>
        </div>
        
        <!-- 内容区域 -->
        <div class="content-area">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-2xl font-bold">设置</h1>
            </div>
            
            <!-- 截屏设置 -->
            <div class="card mb-6">
                <h2 class="text-lg font-semibold mb-4">截屏设置</h2>
                
                <div class="space-y-4">
                    <!-- 截屏间隔 -->
                    <div class="flex flex-col">
                        <label class="text-sm text-gray-600 mb-2">截屏间隔时间</label>
                        <div class="flex items-center">
                            <input type="range" min="1" max="60" value="15" class="w-full mr-3">
                            <span class="text-gray-800 font-medium min-w-[40px]">15分钟</span>
                        </div>
                    </div>
                    
                    <!-- 随机间隔 -->
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="font-medium">随机间隔时间</div>
                            <div class="text-sm text-gray-500">在设定时间基础上随机增减</div>
                        </div>
                        <div class="toggle">
                            <input type="checkbox" checked>
                            <span class="toggle-slider"></span>
                        </div>
                    </div>
                    
                    <!-- 随机范围 -->
                    <div class="flex flex-col">
                        <label class="text-sm text-gray-600 mb-2">随机范围 (±秒)</label>
                        <div class="flex items-center">
                            <input type="range" min="10" max="300" value="120" class="w-full mr-3">
                            <span class="text-gray-800 font-medium min-w-[40px]">120秒</span>
                        </div>
                    </div>
                    
                    <!-- 截屏质量 -->
                    <div class="flex flex-col">
                        <label class="text-sm text-gray-600 mb-2">截屏质量</label>
                        <select class="p-2 border rounded-lg bg-white">
                            <option>高 (原始尺寸)</option>
                            <option selected>中 (压缩50%)</option>
                            <option>低 (压缩75%)</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- 拼图设置 -->
            <div class="card mb-6">
                <h2 class="text-lg font-semibold mb-4">拼图设置</h2>
                
                <div class="space-y-4">
                    <!-- 自动拼图 -->
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="font-medium">每日自动拼图</div>
                            <div class="text-sm text-gray-500">在指定时间自动生成当日拼图</div>
                        </div>
                        <div class="toggle">
                            <input type="checkbox" checked>
                            <span class="toggle-slider"></span>
                        </div>
                    </div>
                    
                    <!-- 拼图时间 -->
                    <div class="flex flex-col">
                        <label class="text-sm text-gray-600 mb-2">拼图时间</label>
                        <input type="time" value="18:00" class="p-2 border rounded-lg">
                    </div>
                    
                    <!-- 拼图布局 -->
                    <div class="flex flex-col">
                        <label class="text-sm text-gray-600 mb-2">拼图布局</label>
                        <select class="p-2 border rounded-lg bg-white">
                            <option selected>网格布局 (4x4)</option>
                            <option>时间轴布局</option>
                            <option>瀑布流布局</option>
                        </select>
                    </div>
                    
                    <!-- 拼图质量 -->
                    <div class="flex flex-col">
                        <label class="text-sm text-gray-600 mb-2">拼图质量</label>
                        <select class="p-2 border rounded-lg bg-white">
                            <option>高 (原始尺寸)</option>
                            <option selected>中 (压缩50%)</option>
                            <option>低 (压缩75%)</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- 通知设置 -->
            <div class="card mb-6">
                <h2 class="text-lg font-semibold mb-4">通知设置</h2>
                
                <div class="space-y-4">
                    <!-- 截屏通知 -->
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="font-medium">截屏通知</div>
                            <div class="text-sm text-gray-500">每次截屏时显示通知</div>
                        </div>
                        <div class="toggle">
                            <input type="checkbox" checked>
                            <span class="toggle-slider"></span>
                        </div>
                    </div>
                    
                    <!-- 拼图通知 -->
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="font-medium">拼图通知</div>
                            <div class="text-sm text-gray-500">拼图完成时显示通知</div>
                        </div>
                        <div class="toggle">
                            <input type="checkbox" checked>
                            <span class="toggle-slider"></span>
                        </div>
                    </div>
                    
                    <!-- 异常通知 -->
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="font-medium">异常通知</div>
                            <div class="text-sm text-gray-500">服务异常时显示通知</div>
                        </div>
                        <div class="toggle">
                            <input type="checkbox" checked>
                            <span class="toggle-slider"></span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 存储设置 -->
            <div class="card mb-6">
                <h2 class="text-lg font-semibold mb-4">存储设置</h2>
                
                <div class="space-y-4">
                    <!-- 存储路径 -->
                    <div class="flex flex-col">
                        <label class="text-sm text-gray-600 mb-2">存储路径</label>
                        <div class="flex">
                            <input type="text" value="/Pictures/AutoScreenshots" class="p-2 border rounded-l-lg flex-1" readonly>
                            <button class="bg-gray-200 p-2 rounded-r-lg">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"/></svg>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 自动清理 -->
                    <div class="flex items-center justify-between">
                        <div>
                            <div class="font-medium">自动清理旧截图</div>
                            <div class="text-sm text-gray-500">保留最近30天的截图</div>
                        </div>
                        <div class="toggle">
                            <input type="checkbox">
                            <span class="toggle-slider"></span>
                        </div>
                    </div>
                    
                    <!-- 存储空间 -->
                    <div>
                        <div class="flex justify-between text-sm mb-1">
                            <span>已用存储空间</span>
                            <span>1.2GB / 8GB</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-bar-fill" style="width: 15%"></div>
                        </div>
                    </div>
                    
                    <button class="w-full py-2 bg-red-100 text-red-700 rounded-lg font-medium">
                        清理所有截图
                    </button>
                </div>
            </div>
            
            <!-- 关于 -->
            <div class="card mb-6">
                <h2 class="text-lg font-semibold mb-4">关于</h2>
                
                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="text-gray-600">版本</span>
                        <span>1.0.0</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600">开发者</span>
                        <span>自律监督团队</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600">反馈邮箱</span>
                        <span class="text-blue-500"><EMAIL></span>
                    </div>
                    
                    <button class="w-full py-2 bg-gray-100 rounded-lg font-medium">
                        检查更新
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 底部导航栏 -->
        <div class="tab-bar">
            <div class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/><polyline points="9 22 9 12 15 12 15 22"/></svg>
                <span>首页</span>
            </div>
            <div class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><circle cx="9" cy="9" r="2"/><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/></svg>
                <span>截图</span>
            </div>
            <div class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><path d="M3 15h18"/><path d="M9 9h.01"/><path d="M15 9h.01"/></svg>
                <span>拼图</span>
            </div>
            <div class="tab-item">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12 20h9"/><path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"/></svg>
                <span>状态</span>
            </div>
            <div class="tab-item active">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/><circle cx="12" cy="12" r="3"/></svg>
                <span>设置</span>
            </div>
        </div>
    </div>
</body>
</html>
