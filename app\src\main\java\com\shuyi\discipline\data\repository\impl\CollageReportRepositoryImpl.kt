package com.shuyi.discipline.data.repository.impl

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import androidx.core.content.ContextCompat
import com.shuyi.discipline.data.model.CollageLayout
import com.shuyi.discipline.data.model.CollageReport
import com.shuyi.discipline.data.model.CollageSettings
import com.shuyi.discipline.data.model.Screenshot
import com.shuyi.discipline.data.repository.CollageReportRepository
import com.shuyi.discipline.data.repository.ScreenshotRepository
import com.shuyi.discipline.data.source.database.AppDatabase
import com.shuyi.discipline.data.source.database.CollageReportDao
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.io.FileOutputStream
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.UUID

/**
 * 拼图报告仓库实现类
 */
class CollageReportRepositoryImpl(
    private val context: Context,
    private val collageReportDao: CollageReportDao,
    private val screenshotRepository: ScreenshotRepository
) : CollageReportRepository {

    private val collageDir: File by lazy {
        // 直接使用内部存储，避免外部存储问题
        val baseDir = context.filesDir
        File(baseDir, "collages").apply {
            if (!exists()) {
                val created = mkdirs()
                Timber.d("创建拼图目录结果: $created")
            }
            Timber.d("拼图目录路径: ${absolutePath}")
        }
    }

    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())

    override fun getAllCollageReports(): Flow<List<CollageReport>> {
        return collageReportDao.getAllCollageReports()
    }

    override suspend fun saveCollageReport(collageReport: CollageReport) {
        collageReportDao.insertCollageReport(collageReport)
    }

    override suspend fun getCollageReportForDate(date: String): CollageReport? {
        val dateObj = dateFormat.parse(date) ?: return null
        return collageReportDao.getCollageReportForDate(dateObj)
    }

    override fun getCollageReportForDateFlow(date: String): Flow<CollageReport?> {
        val dateObj = dateFormat.parse(date) ?: return kotlinx.coroutines.flow.emptyFlow()
        return collageReportDao.getCollageReportForDateFlow(dateObj)
    }

    override suspend fun deleteOldCollageReports(daysToKeep: Int) {
        val calendar = Calendar.getInstance()
        calendar.add(Calendar.DAY_OF_YEAR, -daysToKeep)
        val cutoffDate = calendar.time

        collageReportDao.deleteCollageReportsOlderThan(cutoffDate)
    }

    override suspend fun deleteCollageReportsForDate(date: String): Int = withContext(Dispatchers.IO) {
        try {
            val dateObj = dateFormat.parse(date) ?: return@withContext 0
            Timber.d("解析日期字符串 '$date' 为 Date 对象: $dateObj")

            // 获取该日期的所有拼图报告
            val allReports = collageReportDao.getAllCollageReports().first()
            val reportsToDelete = allReports.filter { report ->
                val reportDateString = dateFormat.format(report.date)
                reportDateString == date
            }

            Timber.d("找到 ${reportsToDelete.size} 个需要删除的拼图报告")

            // 删除对应的拼图文件
            reportsToDelete.forEach { collageReport ->
                // 删除主拼图文件
                if (collageReport.collagePath.isNotEmpty()) {
                    val file = File(collageReport.collagePath)
                    if (file.exists()) {
                        val deleted = file.delete()
                        Timber.d("删除主拼图文件: ${collageReport.collagePath}, 结果: $deleted")
                    }
                }

                // 删除所有拼图文件
                collageReport.collagePaths.forEach { path ->
                    val file = File(path)
                    if (file.exists()) {
                        val deleted = file.delete()
                        Timber.d("删除拼图文件: $path, 结果: $deleted")
                    }
                }

                // 删除缩略图文件
                collageReport.thumbnailPath?.let { thumbnailPath ->
                    val thumbnailFile = File(thumbnailPath)
                    if (thumbnailFile.exists()) {
                        val deleted = thumbnailFile.delete()
                        Timber.d("删除缩略图文件: $thumbnailPath, 结果: $deleted")
                    }
                }
            }

            // 计算当天的开始和结束时间戳
            val calendar = Calendar.getInstance()
            calendar.time = dateObj
            calendar.set(Calendar.HOUR_OF_DAY, 0)
            calendar.set(Calendar.MINUTE, 0)
            calendar.set(Calendar.SECOND, 0)
            calendar.set(Calendar.MILLISECOND, 0)
            val startOfDay = calendar.timeInMillis

            calendar.add(Calendar.DAY_OF_MONTH, 1)
            val endOfDay = calendar.timeInMillis

            Timber.d("删除时间范围: $startOfDay - $endOfDay (${Date(startOfDay)} - ${Date(endOfDay)})")

            // 从数据库中删除拼图报告
            val deletedCount = collageReportDao.deleteCollageReportsForDate(startOfDay, endOfDay)
            Timber.d("删除日期 $date 的拼图报告，删除数量: $deletedCount")

            return@withContext deletedCount
        } catch (e: Exception) {
            Timber.e(e, "删除日期 $date 的拼图报告失败")
            return@withContext 0
        }
    }

    override suspend fun generateDailyCollage(): Result<CollageReport> = withContext(Dispatchers.IO) {
        try {
            // 获取今天的日期
            val today = Date()
            val todayString = dateFormat.format(today)

            Timber.d("开始生成今日拼图: $todayString")

            // 获取今天的截图，并按时间升序排序（从早到晚）
            val unsortedScreenshots = screenshotRepository.getScreenshotsForDay(todayString)
            if (unsortedScreenshots.isEmpty()) {
                Timber.w("今天没有截图，无法生成拼图")
                return@withContext Result.failure(IllegalStateException("今天没有截图"))
            }

            // 按照截图时间排序，确保最早的截图先拼
            val screenshots = unsortedScreenshots.sortedBy { it.timestamp }

            Timber.d("获取到今日截图数量: ${screenshots.size}")

            // 获取拼图设置
            val database = AppDatabase.getInstance(context)
            val collageSettingsDao = database.collageSettingsDao()
            val collageSettings = collageSettingsDao.getCollageSettingsSync() ?: CollageSettings()

            // 计算拼图布局
            val rows: Int
            val cols: Int

            // 根据设置的布局选择行列数
            when (collageSettings.collageLayout) {
                CollageLayout.GRID_3X3 -> {
                    rows = 3
                    cols = 3
                }
                CollageLayout.GRID_4X4 -> {
                    rows = 4
                    cols = 4
                }
            }

            // 计算每张拼图最多包含的截图数量
            val maxScreenshotsPerCollage = rows * cols

            // 根据截图数量和每张拼图最大容量，计算需要生成的拼图数量
            val collageCount = (screenshots.size + maxScreenshotsPerCollage - 1) / maxScreenshotsPerCollage
            Timber.d("需要生成 $collageCount 张拼图")

            // 生成多张拼图
            val collageFiles = mutableListOf<File>()
            for (i in 0 until collageCount) {
                val startIndex = i * maxScreenshotsPerCollage
                val endIndex = minOf(startIndex + maxScreenshotsPerCollage, screenshots.size)
                val collageScreenshots = screenshots.subList(startIndex, endIndex)

                val collageFile = createCollage(collageScreenshots, rows, cols, maxScreenshotsPerCollage)
                collageFiles.add(collageFile)
                Timber.d("拼图文件 ${i+1}/${collageCount} 已创建: ${collageFile.absolutePath}")
            }

            // 为每个拼图文件创建一个拼图报告
            val collageReports = mutableListOf<CollageReport>()

            // 创建主拼图报告（包含所有拼图路径）
            val mainCollageFile = collageFiles.first()
            val collagePaths = collageFiles.map { it.absolutePath }

            val mainCollageReport = CollageReport(
                id = UUID.randomUUID().toString(),
                date = today,
                collagePath = mainCollageFile.absolutePath,
                collagePaths = collagePaths,
                screenshotIds = screenshots.map { it.id },
                createTime = System.currentTimeMillis()
            )

            // 保存主拼图报告
            collageReportDao.insertCollageReport(mainCollageReport)
            collageReports.add(mainCollageReport)
            Timber.d("主拼图报告已保存到数据库: ${mainCollageReport.id}")

            // 为其他拼图文件创建单独的拼图报告（如果有多张拼图）
            if (collageFiles.size > 1) {
                for (i in 1 until collageFiles.size) {
                    val collageFile = collageFiles[i]
                    val collageReport = CollageReport(
                        id = UUID.randomUUID().toString(),
                        date = today,
                        collagePath = collageFile.absolutePath,
                        collagePaths = listOf(collageFile.absolutePath),
                        screenshotIds = screenshots.map { it.id },
                        createTime = System.currentTimeMillis() + i // 确保时间戳不同
                    )

                    // 保存拼图报告
                    collageReportDao.insertCollageReport(collageReport)
                    collageReports.add(collageReport)
                    Timber.d("额外拼图报告已保存到数据库: ${collageReport.id}")
                }
            }

            // 标记截图为已处理
            screenshotRepository.markScreenshotsAsProcessed(screenshots)
            Timber.d("截图已标记为已处理")

            // 返回主拼图报告
            Result.success(mainCollageReport)
        } catch (e: Exception) {
            Timber.e(e, "生成拼图失败: ${e.message}")
            Result.failure(e)
        }
    }

    /**
     * 创建拼图
     * @param screenshots 截图列表
     * @param rows 行数
     * @param cols 列数
     * @param maxScreenshotsPerCollage 每张拼图最多包含的截图数量
     * @return 拼图文件
     */
    private suspend fun createCollage(
        screenshots: List<Screenshot>,
        rows: Int,
        cols: Int,
        maxScreenshotsPerCollage: Int
    ): File = withContext(Dispatchers.IO) {
        // 使用时间戳确保文件名唯一
        val timeStamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val fileName = "collage_${timeStamp}.jpg"
        val file = File(collageDir, fileName)

        // 获取拼图设置
        val database = AppDatabase.getInstance(context)
        val collageSettingsDao = database.collageSettingsDao()
        val collageSettings = collageSettingsDao.getCollageSettingsSync() ?: CollageSettings()

        // 获取设备屏幕尺寸
        val displayMetrics = context.resources.displayMetrics
        val screenWidth = displayMetrics.widthPixels
        val screenHeight = displayMetrics.heightPixels

        // 创建位图 - 根据分辨率设置调整尺寸
        // 分辨率设置：1-100%，对应0.5倍到2倍屏幕分辨率
        val resolutionScale = (collageSettings.collageResolution / 100f) * 1.5f + 0.5f
        val width = (screenWidth * resolutionScale).toInt() // 拼图宽度
        val height = (screenHeight * resolutionScale).toInt() // 拼图高度

        Timber.d("创建拼图，尺寸: ${width}x${height}，布局: ${rows}x${cols}")

        val bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(bitmap)
        val paint = Paint().apply {
            isAntiAlias = true
        }

        // 填充背景
        canvas.drawColor(android.graphics.Color.WHITE)

        // 计算单个图片的大小
        val itemWidth = width / cols
        val itemHeight = height / rows

        // 计算边框宽度
        val borderWidth = (width * 0.005f).toInt() // 边框宽度为总宽度的0.5%
        val borderColor = android.graphics.Color.parseColor("#DDDDDD") // 浅灰色边框
        val borderPaint = Paint().apply {
            color = borderColor
            style = Paint.Style.STROKE
            strokeWidth = borderWidth.toFloat()
            isAntiAlias = true
        }

        // 获取完整分辨率的截图并绘制到画布
        val count = minOf(screenshots.size, maxScreenshotsPerCollage)

        // 根据截图数量和模糊设置优化处理策略
        val shouldOptimizeForBlur = collageSettings.collageBlur > 0 && count > 10
        if (shouldOptimizeForBlur) {
            Timber.d("检测到大量截图($count)需要模糊处理，启用优化策略")
        }

        for (i in 0 until count) {
            try {
                // 解析截图文件
                val screenshotFile = File(screenshots[i].filePath)
                if (!screenshotFile.exists()) {
                    Timber.e("截图文件不存在: ${screenshots[i].filePath}")
                    continue
                }

                // 使用完整分辨率的截图，不再使用inSampleSize降低质量
                var thumbnail = android.graphics.BitmapFactory.decodeFile(screenshotFile.absolutePath)
                if (thumbnail == null) {
                    Timber.e("无法解码截图: ${screenshots[i].filePath}")
                    continue
                }

                // 应用高斯模糊效果（如果设置了模糊）
                if (collageSettings.collageBlur > 0) {
                    Timber.d("对截图 ${i + 1}/$count 应用高斯模糊，模糊值: ${collageSettings.collageBlur}")
                    val originalThumbnail = thumbnail
                    thumbnail = applyGaussianBlur(thumbnail, collageSettings.collageBlur)

                    // 如果模糊处理创建了新的bitmap，回收原始的
                    if (thumbnail != originalThumbnail) {
                        originalThumbnail.recycle()
                    }
                }

                // 计算位置
                val row = i / cols
                val col = i % cols
                val left = col * itemWidth + borderWidth / 2
                val top = row * itemHeight + borderWidth / 2
                val right = left + itemWidth - borderWidth
                val bottom = top + itemHeight - borderWidth

                // 绘制截图 - 保持原始比例，不要拉伸
                val destRect = Rect(left, top, right, bottom)

                // 计算保持原始比例的目标矩形
                val srcWidth = thumbnail.width
                val srcHeight = thumbnail.height
                val destWidth = right - left
                val destHeight = bottom - top

                // 计算缩放比例
                val scale = minOf(destWidth.toFloat() / srcWidth, destHeight.toFloat() / srcHeight)

                // 计算缩放后的宽高
                val scaledWidth = (srcWidth * scale).toInt()
                val scaledHeight = (srcHeight * scale).toInt()

                // 计算居中位置
                val offsetX = (destWidth - scaledWidth) / 2
                val offsetY = (destHeight - scaledHeight) / 2

                // 创建新的目标矩形，保持原始比例并居中
                val centeredRect = Rect(
                    left + offsetX,
                    top + offsetY,
                    left + offsetX + scaledWidth,
                    top + offsetY + scaledHeight
                )

                // 绘制截图 - 使用高质量的绘制选项
                val highQualityPaint = Paint().apply {
                    isAntiAlias = true
                    isFilterBitmap = true
                    isDither = true
                }
                canvas.drawBitmap(thumbnail, null, centeredRect, highQualityPaint)

                // 绘制边框
                canvas.drawRect(destRect, borderPaint)

                // 绘制时间戳
                val timestampPaint = Paint().apply {
                    color = android.graphics.Color.parseColor("#4CAF50") // 绿色
                    textSize = (itemHeight * 0.06f) // 文字大小减小为单元格高度的6%（原来是8%）
                    setShadowLayer(3f, 1f, 1f, android.graphics.Color.BLACK) // 添加阴影使文字更清晰
                    isAntiAlias = true
                    textAlign = Paint.Align.CENTER // 文字居中对齐
                }

                val timeFormat = SimpleDateFormat("HH:mm:ss", Locale.getDefault())
                val timeText = timeFormat.format(Date(screenshots[i].timestamp))
                val textX = left + itemWidth / 2f // 文字水平居中
                val textY = bottom - itemHeight * 0.05f // 文字位置更靠下，距离底部仅5%处（原来是15%）

                canvas.drawText(timeText, textX, textY, timestampPaint)

                // 回收截图位图，释放内存
                thumbnail.recycle()
            } catch (e: Exception) {
                Timber.e(e, "处理截图失败: ${screenshots[i].filePath}")
            }
        }

        // 不再添加拼图标题，移除日期和"活动记录"文字
        // 如果需要页码信息，可以添加到拼图的其他位置或者以其他方式显示
        // 这里我们完全移除了标题，让拼图更加简洁

        // 保存拼图
        try {
            FileOutputStream(file).use { out ->
                // 使用设置中的质量参数
                val quality = collageSettings.collageQuality.coerceIn(1, 100)
                bitmap.compress(Bitmap.CompressFormat.JPEG, quality, out)
            }
            Timber.d("拼图保存成功: ${file.absolutePath}")

            // 更新上次拼图时间
            collageSettingsDao.updateLastCollageTime(System.currentTimeMillis())
        } catch (e: Exception) {
            Timber.e(e, "拼图保存失败")
            throw e
        } finally {
            bitmap.recycle()
        }

        file
    }

    /**
     * 应用高斯模糊效果（优化版本）
     * @param bitmap 原始bitmap
     * @param blurPercent 模糊百分比（0-100）
     * @return 模糊后的bitmap
     */
    private fun applyGaussianBlur(bitmap: Bitmap, blurPercent: Int): Bitmap {
        return try {
            // 将百分比转换为模糊半径（0-15f，降低最大值以提高性能）
            val blurRadius = (blurPercent / 100f * 15f).coerceIn(0.1f, 15f)

            Timber.d("开始应用高斯模糊，半径: $blurRadius, 图片尺寸: ${bitmap.width}x${bitmap.height}")
            val startTime = System.currentTimeMillis()

            // 使用优化的模糊算法
            val result = applyOptimizedBlur(bitmap, blurRadius)

            val endTime = System.currentTimeMillis()
            Timber.d("高斯模糊完成，耗时: ${endTime - startTime}ms")

            result
        } catch (e: Exception) {
            Timber.e(e, "应用高斯模糊失败")
            // 如果模糊失败，返回原始bitmap的副本
            bitmap.copy(bitmap.config ?: Bitmap.Config.ARGB_8888, false)
        }
    }

    /**
     * 优化的模糊算法实现
     * 使用分离式高斯模糊和尺寸优化来提高性能
     */
    private fun applyOptimizedBlur(bitmap: Bitmap, radius: Float): Bitmap {
        val radiusInt = radius.toInt().coerceAtLeast(1)

        // 如果半径太小，直接返回原图副本
        if (radiusInt <= 1) {
            return bitmap.copy(bitmap.config ?: Bitmap.Config.ARGB_8888, false)
        }

        // 性能优化：对于大图片，先缩小再模糊再放大
        val originalWidth = bitmap.width
        val originalHeight = bitmap.height
        val totalPixels = originalWidth * originalHeight

        // 根据像素数量动态调整最大处理尺寸
        val maxDimension = when {
            totalPixels > 2000000 -> 600  // 超过200万像素，大幅缩小
            totalPixels > 1000000 -> 800  // 超过100万像素，中等缩小
            else -> 1000                  // 其他情况，轻微缩小
        }

        val shouldScale = originalWidth > maxDimension || originalHeight > maxDimension
        val scaledBitmap = if (shouldScale) {
            val scale = minOf(maxDimension.toFloat() / originalWidth, maxDimension.toFloat() / originalHeight)
            val scaledWidth = (originalWidth * scale).toInt()
            val scaledHeight = (originalHeight * scale).toInt()
            val scaledRadius = radius * scale

            Timber.d("缩放图片进行模糊处理: ${originalWidth}x${originalHeight} -> ${scaledWidth}x${scaledHeight}, 缩放半径: $scaledRadius")

            val scaled = Bitmap.createScaledBitmap(bitmap, scaledWidth, scaledHeight, true)

            // 根据图片大小和模糊强度选择算法
            val blurred = if (scaledWidth * scaledHeight > 500000 || scaledRadius > 10) {
                Timber.d("使用快速模糊算法")
                applyFastBlur(scaled, scaledRadius)
            } else {
                Timber.d("使用高质量分离式模糊算法")
                applySeparableBlur(scaled, scaledRadius)
            }

            scaled.recycle() // 释放缩放后的临时bitmap

            // 将模糊结果放大回原始尺寸
            val result = Bitmap.createScaledBitmap(blurred, originalWidth, originalHeight, true)
            blurred.recycle() // 释放模糊后的临时bitmap
            result
        } else {
            // 直接对原图进行模糊，根据图片大小选择算法
            if (originalWidth * originalHeight > 1000000 || radius > 10) {
                Timber.d("使用快速模糊算法（原图）")
                applyFastBlur(bitmap, radius)
            } else {
                Timber.d("使用高质量分离式模糊算法（原图）")
                applySeparableBlur(bitmap, radius)
            }
        }

        return scaledBitmap
    }

    /**
     * 分离式高斯模糊实现
     * 先水平模糊，再垂直模糊，复杂度从O(r²)降低到O(r)
     */
    private fun applySeparableBlur(bitmap: Bitmap, radius: Float): Bitmap {
        val radiusInt = radius.toInt().coerceAtLeast(1)
        val width = bitmap.width
        val height = bitmap.height

        // 创建高斯权重数组
        val weights = createGaussianWeights(radiusInt)

        // 获取原始像素
        val pixels = IntArray(width * height)
        bitmap.getPixels(pixels, 0, width, 0, 0, width, height)

        // 第一步：水平模糊
        val horizontalBlurred = IntArray(width * height)
        for (y in 0 until height) {
            for (x in 0 until width) {
                var red = 0f
                var green = 0f
                var blue = 0f
                var alpha = 0f
                var totalWeight = 0f

                for (i in -radiusInt..radiusInt) {
                    val nx = (x + i).coerceIn(0, width - 1)
                    val pixel = pixels[y * width + nx]
                    val weight = weights[i + radiusInt]

                    alpha += ((pixel shr 24) and 0xFF) * weight
                    red += ((pixel shr 16) and 0xFF) * weight
                    green += ((pixel shr 8) and 0xFF) * weight
                    blue += (pixel and 0xFF) * weight
                    totalWeight += weight
                }

                // 归一化
                alpha /= totalWeight
                red /= totalWeight
                green /= totalWeight
                blue /= totalWeight

                horizontalBlurred[y * width + x] =
                    (alpha.toInt() shl 24) or (red.toInt() shl 16) or (green.toInt() shl 8) or blue.toInt()
            }
        }

        // 第二步：垂直模糊
        val verticalBlurred = IntArray(width * height)
        for (x in 0 until width) {
            for (y in 0 until height) {
                var red = 0f
                var green = 0f
                var blue = 0f
                var alpha = 0f
                var totalWeight = 0f

                for (i in -radiusInt..radiusInt) {
                    val ny = (y + i).coerceIn(0, height - 1)
                    val pixel = horizontalBlurred[ny * width + x]
                    val weight = weights[i + radiusInt]

                    alpha += ((pixel shr 24) and 0xFF) * weight
                    red += ((pixel shr 16) and 0xFF) * weight
                    green += ((pixel shr 8) and 0xFF) * weight
                    blue += (pixel and 0xFF) * weight
                    totalWeight += weight
                }

                // 归一化
                alpha /= totalWeight
                red /= totalWeight
                green /= totalWeight
                blue /= totalWeight

                verticalBlurred[y * width + x] =
                    (alpha.toInt() shl 24) or (red.toInt() shl 16) or (green.toInt() shl 8) or blue.toInt()
            }
        }

        // 创建结果bitmap
        val result = Bitmap.createBitmap(width, height, bitmap.config ?: Bitmap.Config.ARGB_8888)
        result.setPixels(verticalBlurred, 0, width, 0, 0, width, height)
        return result
    }

    /**
     * 创建高斯权重数组
     */
    private fun createGaussianWeights(radius: Int): FloatArray {
        val size = radius * 2 + 1
        val weights = FloatArray(size)
        val sigma = radius / 3.0f // 标准差
        val twoSigmaSquared = 2 * sigma * sigma

        for (i in 0 until size) {
            val x = i - radius
            weights[i] = kotlin.math.exp(-(x * x) / twoSigmaSquared)
        }

        return weights
    }

    /**
     * 快速模糊算法（用于极大图片或高模糊值的情况）
     * 使用简化的盒式滤波器，性能更好但质量稍低
     */
    private fun applyFastBlur(bitmap: Bitmap, radius: Float): Bitmap {
        val radiusInt = radius.toInt().coerceAtLeast(1)
        val width = bitmap.width
        val height = bitmap.height

        val pixels = IntArray(width * height)
        bitmap.getPixels(pixels, 0, width, 0, 0, width, height)

        // 简化的盒式滤波器，只进行一次水平和垂直模糊
        val blurred = IntArray(width * height)

        // 水平模糊
        for (y in 0 until height) {
            for (x in 0 until width) {
                var r = 0
                var g = 0
                var b = 0
                var a = 0
                var count = 0

                val start = maxOf(0, x - radiusInt)
                val end = minOf(width - 1, x + radiusInt)

                for (i in start..end) {
                    val pixel = pixels[y * width + i]
                    a += (pixel shr 24) and 0xFF
                    r += (pixel shr 16) and 0xFF
                    g += (pixel shr 8) and 0xFF
                    b += pixel and 0xFF
                    count++
                }

                blurred[y * width + x] = ((a / count) shl 24) or
                                        ((r / count) shl 16) or
                                        ((g / count) shl 8) or
                                        (b / count)
            }
        }

        // 垂直模糊
        val result = IntArray(width * height)
        for (x in 0 until width) {
            for (y in 0 until height) {
                var r = 0
                var g = 0
                var b = 0
                var a = 0
                var count = 0

                val start = maxOf(0, y - radiusInt)
                val end = minOf(height - 1, y + radiusInt)

                for (i in start..end) {
                    val pixel = blurred[i * width + x]
                    a += (pixel shr 24) and 0xFF
                    r += (pixel shr 16) and 0xFF
                    g += (pixel shr 8) and 0xFF
                    b += pixel and 0xFF
                    count++
                }

                result[y * width + x] = ((a / count) shl 24) or
                                       ((r / count) shl 16) or
                                       ((g / count) shl 8) or
                                       (b / count)
            }
        }

        val resultBitmap = Bitmap.createBitmap(width, height, bitmap.config ?: Bitmap.Config.ARGB_8888)
        resultBitmap.setPixels(result, 0, width, 0, 0, width, height)
        return resultBitmap
    }
}