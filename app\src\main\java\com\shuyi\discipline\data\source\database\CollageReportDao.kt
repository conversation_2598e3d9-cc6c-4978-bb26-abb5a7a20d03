package com.shuyi.discipline.data.source.database

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.TypeConverters
import com.shuyi.discipline.data.model.CollageReport
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * 拼图报告数据访问对象
 */
@Dao
@TypeConverters(Converters::class)
interface CollageReportDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCollageReport(collageReport: CollageReport)

    @Query("SELECT * FROM collage_reports ORDER BY date DESC")
    fun getAllCollageReports(): Flow<List<CollageReport>>

    @Query("SELECT * FROM collage_reports WHERE strftime('%Y-%m-%d', date / 1000, 'unixepoch') = strftime('%Y-%m-%d', :date / 1000, 'unixepoch')")
    suspend fun getCollageReportForDate(date: Date): CollageReport?

    @Query("SELECT * FROM collage_reports WHERE strftime('%Y-%m-%d', date / 1000, 'unixepoch') = strftime('%Y-%m-%d', :date / 1000, 'unixepoch')")
    fun getCollageReportForDateFlow(date: Date): Flow<CollageReport?>

    @Query("DELETE FROM collage_reports WHERE date < :date")
    suspend fun deleteCollageReportsOlderThan(date: Date)

    @Query("DELETE FROM collage_reports WHERE date >= :startOfDay AND date < :endOfDay")
    suspend fun deleteCollageReportsForDate(startOfDay: Long, endOfDay: Long): Int

    // 保持向后兼容性的重载方法
    suspend fun deleteCollageReportsForDate(date: Date): Int {
        val calendar = java.util.Calendar.getInstance()
        calendar.time = date
        calendar.set(java.util.Calendar.HOUR_OF_DAY, 0)
        calendar.set(java.util.Calendar.MINUTE, 0)
        calendar.set(java.util.Calendar.SECOND, 0)
        calendar.set(java.util.Calendar.MILLISECOND, 0)
        val startOfDay = calendar.timeInMillis

        calendar.add(java.util.Calendar.DAY_OF_MONTH, 1)
        val endOfDay = calendar.timeInMillis

        return deleteCollageReportsForDate(startOfDay, endOfDay)
    }
}