package com.shuyi.discipline.ui.screen.config

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import com.shuyi.discipline.ui.components.BottomNavBar
import com.shuyi.discipline.ui.components.PageTitleBar
import com.shuyi.discipline.ui.components.SettingsNavigationItem
import com.shuyi.discipline.ui.navigation.Screen

/**
 * 设置分类项数据类
 */
data class SettingsCategoryItem(
    val title: String,
    val description: String,
    val icon: ImageVector,
    val route: String
)

/**
 * 设置主页面 - 显示设置分类列表
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsMainScreen(
    onNavigateBack: () -> Unit,
    onNavigateToHome: () -> Unit = {},
    onNavigateToScreenshots: () -> Unit = {},
    onNavigateToReports: () -> Unit = {},
    navController: androidx.navigation.NavController? = null
) {
    val scrollState = rememberScrollState()
    var selectedTabIndex by remember { mutableStateOf(4) } // 设置页面是第5个选项卡

    // 设置分类列表
    val settingsCategories = listOf(
        SettingsCategoryItem(
            title = "外观设置",
            description = "主题模式、动态颜色、字体大小",
            icon = Icons.Default.Palette,
            route = Screen.AppearanceSettings.route
        ),
        SettingsCategoryItem(
            title = "截图设置",
            description = "截图间隔、质量、分辨率、模糊效果",
            icon = Icons.Default.CameraAlt,
            route = Screen.ScreenshotSettings.route
        ),
        SettingsCategoryItem(
            title = "拼图设置",
            description = "自动拼图、拼图时间、布局、质量",
            icon = Icons.Default.GridView,
            route = Screen.CollageSettings.route
        ),
        SettingsCategoryItem(
            title = "通知设置",
            description = "截屏通知、拼图通知",
            icon = Icons.Default.Notifications,
            route = Screen.NotificationSettings.route
        ),
        SettingsCategoryItem(
            title = "关于",
            description = "版本信息、开发者信息",
            icon = Icons.Default.Info,
            route = Screen.About.route
        )
    )

    Scaffold(
        containerColor = MaterialTheme.colorScheme.background,
        bottomBar = {
            BottomNavBar(
                selectedIndex = 4, // 固定为设置标签
                onItemSelected = { index ->
                    selectedTabIndex = index
                    // 根据选中的标签执行相应的导航
                    when(index) {
                        0 -> onNavigateToHome()
                        1 -> onNavigateToScreenshots()
                        2 -> onNavigateToReports() // 导航到拼图页面
                        3 -> navController?.navigate(Screen.Status.route) {
                            // 避免创建多个实例
                            launchSingleTop = true
                        }
                        // 4是设置页面，已经在此页面，无需导航
                    }
                },
                onNavigateToHome = onNavigateToHome,
                onNavigateToScreenshots = onNavigateToScreenshots,
                onNavigateToCollages = onNavigateToReports,
                onNavigateToStatus = {
                    navController?.navigate(Screen.Status.route) {
                        // 避免创建多个实例
                        launchSingleTop = true
                    }
                },
                onNavigateToConfig = { /* 已在设置页面，无需导航 */ }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .background(MaterialTheme.colorScheme.background)
        ) {
            // 内容区域
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp, vertical = 8.dp)
                    .verticalScroll(scrollState),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // 标题
                PageTitleBar(
                    title = "设置",
                    showBackButton = false,
                    onBackClick = {}
                )

                // 设置分类列表
                settingsCategories.forEach { category ->
                    SettingsNavigationItem(
                        title = category.title,
                        description = category.description,
                        icon = category.icon,
                        onClick = {
                            navController?.navigate(category.route) {
                                launchSingleTop = true
                            }
                        }
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun SettingsMainScreenPreview() {
    MaterialTheme {
        SettingsMainScreen(
            onNavigateBack = {}
        )
    }
}
