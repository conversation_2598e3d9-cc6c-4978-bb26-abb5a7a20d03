package com.shuyi.discipline.data.source.database

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import com.shuyi.discipline.data.model.Screenshot
import kotlinx.coroutines.flow.Flow
import java.time.LocalDate
import java.time.ZoneId

/**
 * 截图数据访问对象
 */
@Dao
interface ScreenshotDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertScreenshot(screenshot: Screenshot)

    @Update
    suspend fun updateScreenshot(screenshot: Screenshot)

    @Delete
    suspend fun deleteScreenshot(screenshot: Screenshot)

    @Query("SELECT * FROM screenshots ORDER BY timestamp DESC")
    fun getAllScreenshots(): Flow<List<Screenshot>>

    @Query("SELECT * FROM screenshots WHERE isProcessed = 0 ORDER BY timestamp DESC")
    fun getUnprocessedScreenshots(): Flow<List<Screenshot>>

    @Query("SELECT * FROM screenshots WHERE timestamp BETWEEN :startOfDay AND :endOfDay ORDER BY timestamp DESC")
    suspend fun getScreenshotsForDay(startOfDay: Long, endOfDay: Long): List<Screenshot>

    @Query("SELECT * FROM screenshots WHERE timestamp BETWEEN :startOfDay AND :endOfDay ORDER BY timestamp DESC")
    fun observeScreenshotsForDay(startOfDay: Long, endOfDay: Long): Flow<List<Screenshot>>

    @Query("UPDATE screenshots SET isProcessed = 1 WHERE id IN (:ids)")
    suspend fun markAsProcessed(ids: List<String>)

    @Query("DELETE FROM screenshots WHERE timestamp < :timestamp")
    suspend fun deleteScreenshotsOlderThan(timestamp: Long)

    /**
     * 根据文件路径删除截图记录
     */
    @Query("DELETE FROM screenshots WHERE filePath = :filePath")
    suspend fun deleteScreenshotByPath(filePath: String): Int
}