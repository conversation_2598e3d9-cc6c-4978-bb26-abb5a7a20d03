package com.shuyi.discipline.ui.screen.permissions

import android.accessibilityservice.AccessibilityServiceInfo
import android.app.Application
import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Environment
import android.provider.Settings
import android.view.accessibility.AccessibilityManager
import androidx.core.content.ContextCompat
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * 权限管理页面的ViewModel
 */
@HiltViewModel
class PermissionsViewModel @Inject constructor(
    application: Application
) : AndroidViewModel(application) {

    // 权限状态
    private val _permissionsState = MutableStateFlow(PermissionsState())
    val permissionsState: StateFlow<PermissionsState> = _permissionsState.asStateFlow()

    // 初始化
    init {
        refreshPermissionsState()
    }

    /**
     * 刷新权限状态
     */
    fun refreshPermissionsState() {
        viewModelScope.launch {
            val context = getApplication<Application>()

            // 检查各项权限状态
            val hasStoragePermission = checkStoragePermission(context)
            val hasNotificationPermission = checkNotificationPermission(context)
            val hasMediaProjectionPermission = checkMediaProjectionPermission()
            val hasBatteryOptimizationIgnored = checkBatteryOptimizationIgnored(context)
            val hasAutoStartPermission = checkAutoStartPermission()
            val hasOverlayPermission = checkOverlayPermission(context)
            val hasAccessibilityPermission = checkAccessibilityPermission(context)

            // 更新权限状态
            _permissionsState.value = PermissionsState(
                hasStoragePermission = hasStoragePermission,
                hasNotificationPermission = hasNotificationPermission,
                hasMediaProjectionPermission = hasMediaProjectionPermission,
                hasBatteryOptimizationIgnored = hasBatteryOptimizationIgnored,
                hasAutoStartPermission = hasAutoStartPermission,
                hasOverlayPermission = hasOverlayPermission,
                hasAccessibilityPermission = hasAccessibilityPermission
            )

            Timber.d("权限状态已刷新: ${_permissionsState.value}")
        }
    }

    /**
     * 检查存储权限
     */
    private fun checkStoragePermission(context: Context): Boolean {
        // 检查实际的存储权限状态
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11及以上使用Environment.isExternalStorageManager()
            Environment.isExternalStorageManager()
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Android 6.0-10使用传统权限检查
            ContextCompat.checkSelfPermission(
                context,
                android.Manifest.permission.WRITE_EXTERNAL_STORAGE
            ) == PackageManager.PERMISSION_GRANTED
        } else {
            // Android 6.0以下默认授予权限
            true
        }
    }

    /**
     * 检查通知权限
     */
    private fun checkNotificationPermission(context: Context): Boolean {
        // 对于Android 13及以上，检查POST_NOTIFICATIONS权限
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            val hasPermission = ContextCompat.checkSelfPermission(
                context,
                android.Manifest.permission.POST_NOTIFICATIONS
            ) == PackageManager.PERMISSION_GRANTED

            Timber.d("通知权限检查结果(Android 13+): $hasPermission")
            return hasPermission
        }

        // 对于Android 8.0-12，检查通知渠道是否启用
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            val areNotificationsEnabled = notificationManager.areNotificationsEnabled()
            Timber.d("通知权限检查结果(Android 8-12): $areNotificationsEnabled")
            return areNotificationsEnabled
        }

        // Android 8.0以下默认启用通知
        return true
    }

    /**
     * 检查媒体投影权限
     * 注意：MediaProjection权限无法直接检查，需要通过服务状态间接判断
     */
    private fun checkMediaProjectionPermission(): Boolean {
        // 这里需要从服务状态或SharedPreferences中获取
        // 暂时返回false，实际实现时需要修改
        return false
    }

    /**
     * 检查电池优化忽略状态
     */
    private fun checkBatteryOptimizationIgnored(context: Context): Boolean {
        val powerManager = context.getSystemService(Context.POWER_SERVICE) as android.os.PowerManager
        return powerManager.isIgnoringBatteryOptimizations(context.packageName)
    }

    /**
     * 检查自启动权限
     * 注意：自启动权限因厂商而异，无法直接检查
     */
    private fun checkAutoStartPermission(): Boolean {
        // 这里需要从SharedPreferences中获取用户设置状态
        // 暂时返回false，实际实现时需要修改
        return false
    }

    /**
     * 检查悬浮窗权限
     */
    private fun checkOverlayPermission(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true
        }
    }

    /**
     * 检查无障碍服务权限
     */
    private fun checkAccessibilityPermission(context: Context): Boolean {
        // 检查应用的无障碍服务是否启用
        val accessibilityManager = context.getSystemService(Context.ACCESSIBILITY_SERVICE) as AccessibilityManager
        val enabledServices = accessibilityManager.getEnabledAccessibilityServiceList(AccessibilityServiceInfo.FEEDBACK_ALL_MASK)

        // 检查我们的服务是否在已启用的服务列表中
        val packageName = context.packageName
        return enabledServices.any { it.resolveInfo.serviceInfo.packageName == packageName }
    }

    /**
     * 请求媒体投影权限
     */
    fun requestMediaProjectionPermission() {
        // 发送广播通知MainActivity请求权限
        val intent = Intent("com.shuyi.discipline.REQUEST_PROJECTION")
        getApplication<Application>().sendBroadcast(intent)
        Timber.d("发送请求媒体投影权限的广播")
    }

    /**
     * 请求忽略电池优化
     */
    fun requestIgnoreBatteryOptimization() {
        // 发送广播通知MainActivity请求权限
        val intent = Intent("com.shuyi.discipline.REQUEST_IGNORE_BATTERY_OPTIMIZATION")
        getApplication<Application>().sendBroadcast(intent)
        Timber.d("发送请求忽略电池优化的广播")
    }

    /**
     * 获取悬浮窗权限设置Intent
     */
    fun getOverlaySettingsIntent(): Intent {
        // 确保使用正确的Intent和包名
        val packageName = getApplication<Application>().packageName
        Timber.d("请求悬浮窗权限，包名: $packageName")

        return Intent(
            Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
            Uri.parse("package:$packageName")
        ).apply {
            // 添加额外的标志，确保正确打开设置页面
            addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        }
    }

    /**
     * 获取无障碍服务设置Intent
     */
    fun getAccessibilitySettingsIntent(): Intent {
        return Intent(Settings.ACTION_ACCESSIBILITY_SETTINGS)
    }

    /**
     * 获取自启动设置Intent
     * 注意：不同厂商的自启动设置页面不同，这里只提供通用的应用详情页
     */
    fun getAutoStartSettingsIntent(): Intent {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
        intent.data = Uri.parse("package:${getApplication<Application>().packageName}")
        return intent
    }

    /**
     * 获取存储权限设置Intent
     * 根据Android版本跳转到不同的设置页面
     */
    fun getStorageSettingsIntent(): Intent {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11及以上跳转到管理所有文件的权限页面
            Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION).apply {
                data = Uri.parse("package:${getApplication<Application>().packageName}")
            }
        } else {
            // Android 10及以下跳转到应用详情页面
            Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.parse("package:${getApplication<Application>().packageName}")
            }
        }
    }

    /**
     * 一键授予所有权限
     */
    fun requestAllPermissions() {
        // 请求媒体投影权限
        requestMediaProjectionPermission()

        // 请求忽略电池优化
        requestIgnoreBatteryOptimization()

        // 其他权限需要用户手动设置，无法一键授予
    }
}

/**
 * 权限状态数据类
 */
data class PermissionsState(
    val hasStoragePermission: Boolean = false,
    val hasNotificationPermission: Boolean = false,
    val hasMediaProjectionPermission: Boolean = false,
    val hasBatteryOptimizationIgnored: Boolean = false,
    val hasAutoStartPermission: Boolean = false,
    val hasOverlayPermission: Boolean = false,
    val hasAccessibilityPermission: Boolean = false
) {
    /**
     * 是否所有必要权限都已授予
     */
    val allRequiredPermissionsGranted: Boolean
        get() = hasStoragePermission && hasNotificationPermission && hasMediaProjectionPermission

    /**
     * 是否所有高级权限都已授予
     */
    val allAdvancedPermissionsGranted: Boolean
        get() = hasBatteryOptimizationIgnored && hasAutoStartPermission &&
                hasOverlayPermission && hasAccessibilityPermission

    /**
     * 是否所有权限都已授予
     */
    val allPermissionsGranted: Boolean
        get() = allRequiredPermissionsGranted && allAdvancedPermissionsGranted
}
