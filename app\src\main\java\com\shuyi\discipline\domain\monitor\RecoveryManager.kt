package com.shuyi.discipline.domain.monitor

import android.content.Context
import android.content.SharedPreferences
import android.os.Build
import androidx.work.WorkInfo
import androidx.work.WorkManager
import com.shuyi.discipline.data.repository.RuntimeRecordRepository
import timber.log.Timber

/**
 * 容错恢复管理器
 * 处理各种异常情况，确保监控数据的完整性
 */
class RecoveryManager(
    private val context: Context,
    private val repository: RuntimeRecordRepository
) {

    private val recoveryPrefs: SharedPreferences by lazy {
        context.getSharedPreferences(RECOVERY_PREFS_NAME, Context.MODE_PRIVATE)
    }

    private val workManager: WorkManager by lazy {
        WorkManager.getInstance(context)
    }

    /**
     * 恢复可能丢失的数据
     */
    suspend fun recoverMissingData() {
        try {
            Timber.d("开始数据恢复检查")
            
            // 1. 检查应用是否异常重启
            checkAbnormalRestart()
            
            // 2. 检查 WorkManager 状态
            checkWorkManagerHealth()
            
            // 3. 检查数据一致性
            checkDataConsistency()
            
            // 4. 更新恢复时间戳
            updateRecoveryTimestamp()
            
            Timber.d("数据恢复检查完成")
            
        } catch (e: Exception) {
            Timber.e(e, "数据恢复失败")
        }
    }

    /**
     * 检查应用是否异常重启
     */
    private suspend fun checkAbnormalRestart() {
        try {
            val lastShutdownTime = recoveryPrefs.getLong(KEY_LAST_SHUTDOWN_TIME, 0)
            val currentTime = System.currentTimeMillis()
            
            if (lastShutdownTime > 0) {
                val downtime = currentTime - lastShutdownTime
                
                // 如果距离上次正常关闭超过阈值，可能是异常重启
                if (downtime > ABNORMAL_RESTART_THRESHOLD) {
                    Timber.w("检测到可能的异常重启:")
                    Timber.w("  上次关闭: ${java.util.Date(lastShutdownTime)}")
                    Timber.w("  当前启动: ${java.util.Date(currentTime)}")
                    Timber.w("  间隔时长: ${downtime}ms")
                    
                    // 检查是否已有记录
                    val hasExistingRecord = repository.hasExitRecordAfter(lastShutdownTime)
                    
                    if (!hasExistingRecord) {
                        // 补充记录未运行时长
                        repository.recordExit(
                            exitTime = lastShutdownTime,
                            exitReason = ExitReason.UNKNOWN,
                            description = "恢复管理器检测到的异常重启",
                            downtimeStart = lastShutdownTime,
                            downtimeEnd = currentTime,
                            downtimeDuration = downtime
                        )
                        
                        Timber.d("已补充记录异常重启的未运行时长")
                    }
                }
                
                // 清除上次关闭时间
                recoveryPrefs.edit().remove(KEY_LAST_SHUTDOWN_TIME).apply()
            }
            
        } catch (e: Exception) {
            Timber.e(e, "检查异常重启失败")
        }
    }

    /**
     * 检查 WorkManager 健康状态
     */
    private suspend fun checkWorkManagerHealth() {
        try {
            // 只在低版本系统检查 WorkManager
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                return
            }
            
            val workInfos = workManager.getWorkInfosForUniqueWork(HEARTBEAT_WORK_NAME).get()
            val workInfo = workInfos.firstOrNull()
            
            val isHealthy = when (workInfo?.state) {
                WorkInfo.State.RUNNING, WorkInfo.State.ENQUEUED -> true
                else -> false
            }
            
            if (!isHealthy) {
                Timber.w("WorkManager 状态异常: ${workInfo?.state}")
                
                // 记录 WorkManager 故障
                val lastWorkManagerCheck = recoveryPrefs.getLong(KEY_LAST_WORKMANAGER_CHECK, 0)
                val currentTime = System.currentTimeMillis()
                
                if (lastWorkManagerCheck > 0) {
                    val gapDuration = currentTime - lastWorkManagerCheck
                    
                    if (gapDuration > WORKMANAGER_GAP_THRESHOLD) {
                        Timber.w("WorkManager 可能停止了 ${gapDuration}ms，检查是否遗漏数据")
                        
                        // 这里可以尝试从其他数据源恢复
                        // 比如检查心跳数据、应用使用统计等
                        tryRecoverFromWorkManagerGap(lastWorkManagerCheck, currentTime)
                    }
                }
                
                // 更新检查时间
                recoveryPrefs.edit()
                    .putLong(KEY_LAST_WORKMANAGER_CHECK, currentTime)
                    .apply()
            }
            
        } catch (e: Exception) {
            Timber.e(e, "检查 WorkManager 健康状态失败")
        }
    }

    /**
     * 尝试从 WorkManager 间隙中恢复数据
     */
    private suspend fun tryRecoverFromWorkManagerGap(gapStart: Long, gapEnd: Long) {
        try {
            Timber.d("尝试恢复 WorkManager 间隙数据: ${java.util.Date(gapStart)} - ${java.util.Date(gapEnd)}")
            
            // 检查心跳数据
            val heartbeatPrefs = context.getSharedPreferences("app_heartbeat", Context.MODE_PRIVATE)
            val lastHeartbeat = heartbeatPrefs.getLong("last_heartbeat", 0)
            
            if (lastHeartbeat > 0 && lastHeartbeat < gapEnd) {
                val possibleExitTime = maxOf(lastHeartbeat, gapStart)
                val downtimeDuration = gapEnd - possibleExitTime
                
                // 检查是否已有记录
                val hasExistingRecord = repository.hasExitRecordAfter(possibleExitTime)
                
                if (!hasExistingRecord && downtimeDuration > MIN_DOWNTIME_TO_RECORD) {
                    repository.recordExit(
                        exitTime = possibleExitTime,
                        exitReason = ExitReason.UNKNOWN,
                        description = "WorkManager 间隙恢复",
                        downtimeStart = possibleExitTime,
                        downtimeEnd = gapEnd,
                        downtimeDuration = downtimeDuration
                    )
                    
                    Timber.d("已恢复 WorkManager 间隙数据，未运行时长: ${downtimeDuration}ms")
                }
            }
            
        } catch (e: Exception) {
            Timber.e(e, "恢复 WorkManager 间隙数据失败")
        }
    }

    /**
     * 检查数据一致性
     */
    private suspend fun checkDataConsistency() {
        try {
            // 检查是否有未完成的会话
            val unfinishedSessions = repository.getUnfinishedSessions()
            
            if (unfinishedSessions.isNotEmpty()) {
                Timber.w("发现 ${unfinishedSessions.size} 个未完成的会话")
                
                val currentTime = System.currentTimeMillis()
                
                unfinishedSessions.forEach { session ->
                    val sessionDuration = currentTime - session.startTime
                    
                    if (sessionDuration > MAX_SESSION_DURATION) {
                        Timber.w("会话时长异常: ${sessionDuration}ms，强制结束")
                        
                        repository.forceEndSession(
                            sessionId = session.sessionId,
                            endTime = currentTime,
                            exitReason = ExitReason.UNKNOWN
                        )
                    }
                }
            }
            
        } catch (e: Exception) {
            Timber.e(e, "检查数据一致性失败")
        }
    }

    /**
     * 记录正常关闭
     */
    fun recordNormalShutdown() {
        try {
            val currentTime = System.currentTimeMillis()
            recoveryPrefs.edit()
                .putLong(KEY_LAST_SHUTDOWN_TIME, currentTime)
                .apply()
            
            Timber.d("记录正常关闭时间: ${java.util.Date(currentTime)}")
            
        } catch (e: Exception) {
            Timber.e(e, "记录正常关闭失败")
        }
    }

    /**
     * 更新恢复时间戳
     */
    private fun updateRecoveryTimestamp() {
        recoveryPrefs.edit()
            .putLong(KEY_LAST_RECOVERY_TIME, System.currentTimeMillis())
            .apply()
    }

    /**
     * 获取恢复统计信息
     */
    fun getRecoveryStats(): RecoveryStats {
        return RecoveryStats(
            lastRecoveryTime = recoveryPrefs.getLong(KEY_LAST_RECOVERY_TIME, 0),
            lastShutdownTime = recoveryPrefs.getLong(KEY_LAST_SHUTDOWN_TIME, 0),
            lastWorkManagerCheck = recoveryPrefs.getLong(KEY_LAST_WORKMANAGER_CHECK, 0)
        )
    }

    companion object {
        private const val RECOVERY_PREFS_NAME = "app_recovery"
        private const val KEY_LAST_SHUTDOWN_TIME = "last_shutdown_time"
        private const val KEY_LAST_RECOVERY_TIME = "last_recovery_time"
        private const val KEY_LAST_WORKMANAGER_CHECK = "last_workmanager_check"
        
        private const val HEARTBEAT_WORK_NAME = "heartbeat_check_work"
        
        // 阈值定义
        private const val ABNORMAL_RESTART_THRESHOLD = 5 * 60 * 1000L      // 5分钟
        private const val WORKMANAGER_GAP_THRESHOLD = 10 * 60 * 1000L      // 10分钟
        private const val MIN_DOWNTIME_TO_RECORD = 2 * 60 * 1000L          // 2分钟
        private const val MAX_SESSION_DURATION = 24 * 60 * 60 * 1000L      // 24小时
    }
}

/**
 * 恢复统计信息
 */
data class RecoveryStats(
    val lastRecoveryTime: Long,
    val lastShutdownTime: Long,
    val lastWorkManagerCheck: Long
)
