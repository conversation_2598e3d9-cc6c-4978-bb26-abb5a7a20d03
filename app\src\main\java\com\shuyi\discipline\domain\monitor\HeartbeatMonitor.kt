package com.shuyi.discipline.domain.monitor

import android.content.Context
import android.content.SharedPreferences
import androidx.work.*
import com.shuyi.discipline.data.repository.RuntimeRecordRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.concurrent.TimeUnit

/**
 * WorkManager 心跳监控器
 * 适用于 Android 10 及以下版本
 */
class HeartbeatMonitor(
    private val context: Context,
    private val repository: RuntimeRecordRepository,
    private val applicationScope: CoroutineScope,
    private val stateManager: PersistentAppStateManager
) {

    private val heartbeatPrefs: SharedPreferences by lazy {
        context.getSharedPreferences(HEARTBEAT_PREFS_NAME, Context.MODE_PRIVATE)
    }

    private val workManager: WorkManager by lazy {
        WorkManager.getInstance(context)
    }

    /**
     * 启动监控
     */
    suspend fun startMonitoring() {
        try {
            Timber.d("启动 WorkManager 心跳监控")
            
            // 1. 启动应用端心跳
            startAppHeartbeat()
            
            // 2. 启动 WorkManager 检查任务
            startWorkManagerCheck()
            
            // 3. 检查上次可能遗漏的退出
            checkMissedExit()
            
        } catch (e: Exception) {
            Timber.e(e, "启动心跳监控失败")
        }
    }

    /**
     * 停止监控
     */
    fun stopMonitoring() {
        try {
            Timber.d("停止 WorkManager 心跳监控")
            
            // 停止 WorkManager 任务
            workManager.cancelUniqueWork(WORK_NAME)
            
            // 记录最后的心跳时间
            updateHeartbeat(isInForeground = false, isExiting = true)
            
        } catch (e: Exception) {
            Timber.e(e, "停止心跳监控失败")
        }
    }

    /**
     * 启动应用端心跳
     */
    private fun startAppHeartbeat() {
        applicationScope.launch {
            try {
                while (isActive) {
                    updateHeartbeat(isInForeground = true) // 这里会根据实际状态更新
                    delay(HEARTBEAT_INTERVAL)
                }
            } catch (e: Exception) {
                Timber.e(e, "应用心跳失败")
            }
        }
    }

    /**
     * 启动 WorkManager 检查任务
     */
    private fun startWorkManagerCheck() {
        // 取消现有任务
        workManager.cancelUniqueWork(WORK_NAME)

        // 创建约束条件
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
            .setRequiresBatteryNotLow(false)
            .setRequiresCharging(false)
            .setRequiresDeviceIdle(false)
            .setRequiresStorageNotLow(false)
            .build()

        // 创建定期任务
        val workRequest = PeriodicWorkRequestBuilder<HeartbeatCheckWorker>(
            CHECK_INTERVAL_MINUTES, TimeUnit.MINUTES,
            FLEX_INTERVAL_MINUTES, TimeUnit.MINUTES
        )
            .setConstraints(constraints)
            .setBackoffCriteria(
                BackoffPolicy.LINEAR,
                WorkRequest.MIN_BACKOFF_MILLIS,
                TimeUnit.MILLISECONDS
            )
            .build()

        // 启动任务
        workManager.enqueueUniquePeriodicWork(
            WORK_NAME,
            ExistingPeriodicWorkPolicy.REPLACE,
            workRequest
        )

        Timber.d("WorkManager 检查任务已启动，每${CHECK_INTERVAL_MINUTES}分钟检查一次")
    }

    /**
     * 更新心跳
     */
    fun updateHeartbeat(isInForeground: Boolean, isExiting: Boolean = false) {
        try {
            val currentTime = System.currentTimeMillis()
            
            heartbeatPrefs.edit()
                .putLong(KEY_LAST_HEARTBEAT, currentTime)
                .putBoolean(KEY_IS_FOREGROUND, isInForeground)
                .putBoolean(KEY_IS_EXITING, isExiting)
                .putLong(KEY_LAST_UPDATE, currentTime)
                .apply()

            if (isExiting) {
                Timber.d("记录退出心跳: ${java.util.Date(currentTime)}")
            } else {
                Timber.v("更新心跳: ${java.util.Date(currentTime)}, 前台: $isInForeground")
            }
            
        } catch (e: Exception) {
            Timber.e(e, "更新心跳失败")
        }
    }

    /**
     * 检查遗漏的退出
     */
    private suspend fun checkMissedExit() {
        try {
            val lastHeartbeat = heartbeatPrefs.getLong(KEY_LAST_HEARTBEAT, 0)
            val wasExiting = heartbeatPrefs.getBoolean(KEY_IS_EXITING, false)
            
            if (lastHeartbeat > 0 && !wasExiting) {
                val currentTime = System.currentTimeMillis()
                val timeSinceLastHeartbeat = currentTime - lastHeartbeat
                
                // 如果距离上次心跳超过阈值，认为应用可能异常退出了
                if (timeSinceLastHeartbeat > MISSED_EXIT_THRESHOLD) {
                    Timber.d("检测到可能的异常退出:")
                    Timber.d("  上次心跳: ${java.util.Date(lastHeartbeat)}")
                    Timber.d("  当前时间: ${java.util.Date(currentTime)}")
                    Timber.d("  间隔时长: ${timeSinceLastHeartbeat}ms")

                    // 🎯 通知状态管理器应用已关闭，检查是否已处理过
                    val shouldRecord = stateManager.recordAppShutdown(lastHeartbeat, "心跳监控检测到的异常退出")

                    if (shouldRecord) {
                        // 记录未运行时长
                        repository.recordExit(
                            exitTime = lastHeartbeat,
                            exitReason = ExitReason.UNKNOWN,
                            description = "心跳监控检测到的异常退出",
                            downtimeStart = lastHeartbeat,
                            downtimeEnd = currentTime,
                            downtimeDuration = timeSinceLastHeartbeat
                        )
                        Timber.d("✅ [HeartbeatMonitor] 已记录异常退出的未运行时长: ${timeSinceLastHeartbeat}ms")
                    } else {
                        Timber.d("🔄 [HeartbeatMonitor] 异常退出时间已被其他监控系统处理，跳过重复记录")
                    }
                }
            }
            
            // 清除退出标记
            heartbeatPrefs.edit().remove(KEY_IS_EXITING).apply()
            
        } catch (e: Exception) {
            Timber.e(e, "检查遗漏退出失败")
        }
    }

    /**
     * 执行健康检查
     */
    suspend fun performHealthCheck() {
        try {
            // 检查 WorkManager 状态
            val workManagerStatus = getWorkManagerStatus()
            
            if (workManagerStatus != WorkManagerStatus.RUNNING) {
                Timber.w("WorkManager 状态异常: $workManagerStatus，尝试重启")
                startWorkManagerCheck()
            }
            
            // 检查心跳是否正常
            val lastHeartbeat = heartbeatPrefs.getLong(KEY_LAST_HEARTBEAT, 0)
            val currentTime = System.currentTimeMillis()
            val heartbeatAge = currentTime - lastHeartbeat
            
            if (heartbeatAge > HEARTBEAT_WARNING_THRESHOLD) {
                Timber.w("心跳异常，距离上次心跳: ${heartbeatAge}ms")
            }
            
        } catch (e: Exception) {
            Timber.e(e, "健康检查失败")
        }
    }

    /**
     * 获取 WorkManager 状态
     */
    fun getWorkManagerStatus(): WorkManagerStatus {
        return try {
            val workInfos = workManager.getWorkInfosForUniqueWork(WORK_NAME).get()
            val workInfo = workInfos.firstOrNull()
            
            when (workInfo?.state) {
                WorkInfo.State.RUNNING, WorkInfo.State.ENQUEUED -> WorkManagerStatus.RUNNING
                WorkInfo.State.FAILED -> WorkManagerStatus.FAILED
                WorkInfo.State.CANCELLED -> WorkManagerStatus.STOPPED
                null -> WorkManagerStatus.STOPPED
                else -> WorkManagerStatus.STOPPED
            }
        } catch (e: Exception) {
            Timber.e(e, "获取 WorkManager 状态失败")
            WorkManagerStatus.FAILED
        }
    }

    /**
     * 获取心跳信息（用于调试）
     */
    fun getHeartbeatInfo(): HeartbeatInfo {
        return HeartbeatInfo(
            lastHeartbeat = heartbeatPrefs.getLong(KEY_LAST_HEARTBEAT, 0),
            isInForeground = heartbeatPrefs.getBoolean(KEY_IS_FOREGROUND, false),
            isExiting = heartbeatPrefs.getBoolean(KEY_IS_EXITING, false),
            lastUpdate = heartbeatPrefs.getLong(KEY_LAST_UPDATE, 0),
            workManagerStatus = getWorkManagerStatus()
        )
    }

    companion object {
        private const val HEARTBEAT_PREFS_NAME = "app_heartbeat"
        private const val KEY_LAST_HEARTBEAT = "last_heartbeat"
        private const val KEY_IS_FOREGROUND = "is_foreground"
        private const val KEY_IS_EXITING = "is_exiting"
        private const val KEY_LAST_UPDATE = "last_update"
        
        private const val WORK_NAME = "heartbeat_check_work"
        
        private const val HEARTBEAT_INTERVAL = 30_000L // 30秒
        private const val CHECK_INTERVAL_MINUTES = 2L // 2分钟
        private const val FLEX_INTERVAL_MINUTES = 1L // 1分钟弹性
        
        private const val MISSED_EXIT_THRESHOLD = 10 * 60 * 1000L // 10分钟
        private const val HEARTBEAT_WARNING_THRESHOLD = 5 * 60 * 1000L // 5分钟
    }
}

/**
 * 心跳信息
 */
data class HeartbeatInfo(
    val lastHeartbeat: Long,
    val isInForeground: Boolean,
    val isExiting: Boolean,
    val lastUpdate: Long,
    val workManagerStatus: WorkManagerStatus
)
