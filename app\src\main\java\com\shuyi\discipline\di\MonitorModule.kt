package com.shuyi.discipline.di

import android.content.Context
import com.shuyi.discipline.data.repository.RuntimeRecordRepository
import com.shuyi.discipline.data.repository.ScheduleRepository
import com.shuyi.discipline.data.repository.SystemMonitorRepository
import com.shuyi.discipline.domain.monitor.AppRuntimeMonitorManager
import com.shuyi.discipline.domain.monitor.ExitInfoMonitor
import com.shuyi.discipline.domain.monitor.HeartbeatMonitor
import com.shuyi.discipline.domain.monitor.MediaProjectionPermissionMonitor
import com.shuyi.discipline.domain.monitor.PersistentAppStateManager
import com.shuyi.discipline.domain.monitor.RecoveryManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import javax.inject.Qualifier
import javax.inject.Singleton

/**
 * 监控系统依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
object MonitorModule {

    /**
     * 提供应用级协程作用域
     */
    @Provides
    @Singleton
    @ApplicationScope
    fun provideApplicationScope(): CoroutineScope {
        return CoroutineScope(SupervisorJob() + Dispatchers.Main)
    }

    /**
     * 提供退出信息监控器
     */
    @Provides
    @Singleton
    fun provideExitInfoMonitor(
        @ApplicationContext context: Context,
        repository: RuntimeRecordRepository,
        stateManager: PersistentAppStateManager
    ): ExitInfoMonitor {
        return ExitInfoMonitor(context, repository, stateManager)
    }

    /**
     * 提供心跳监控器
     */
    @Provides
    @Singleton
    fun provideHeartbeatMonitor(
        @ApplicationContext context: Context,
        repository: RuntimeRecordRepository,
        @ApplicationScope applicationScope: CoroutineScope,
        stateManager: PersistentAppStateManager
    ): HeartbeatMonitor {
        return HeartbeatMonitor(context, repository, applicationScope, stateManager)
    }

    /**
     * 提供持久化应用状态管理器
     */
    @Provides
    @Singleton
    fun providePersistentAppStateManager(
        @ApplicationContext context: Context,
        repository: RuntimeRecordRepository
    ): PersistentAppStateManager {
        return PersistentAppStateManager(context, repository)
    }

    /**
     * 提供恢复管理器
     */
    @Provides
    @Singleton
    fun provideRecoveryManager(
        @ApplicationContext context: Context,
        repository: RuntimeRecordRepository
    ): RecoveryManager {
        return RecoveryManager(context, repository)
    }

    /**
     * 提供应用运行时监控管理器
     */
    @Provides
    @Singleton
    fun provideAppRuntimeMonitorManager(
        @ApplicationContext context: Context,
        repository: RuntimeRecordRepository,
        @ApplicationScope applicationScope: CoroutineScope,
        exitInfoMonitor: ExitInfoMonitor,
        heartbeatMonitor: HeartbeatMonitor,
        recoveryManager: RecoveryManager,
        persistentAppStateManager: PersistentAppStateManager
    ): AppRuntimeMonitorManager {
        return AppRuntimeMonitorManager(context, repository, applicationScope, exitInfoMonitor, heartbeatMonitor, recoveryManager, persistentAppStateManager)
    }

    /**
     * 提供MediaProjection权限监控器
     */
    @Provides
    @Singleton
    fun provideMediaProjectionPermissionMonitor(
        @ApplicationContext context: Context,
        scheduleRepository: ScheduleRepository,
        systemMonitorRepository: SystemMonitorRepository,
        @ApplicationScope applicationScope: CoroutineScope
    ): MediaProjectionPermissionMonitor {
        return MediaProjectionPermissionMonitor(
            context,
            scheduleRepository,
            systemMonitorRepository,
            applicationScope
        )
    }
}

/**
 * 应用级协程作用域限定符
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class ApplicationScope
