package com.shuyi.discipline.domain.usecase

import android.app.Application
import android.content.Context
import androidx.work.Constraints
import androidx.work.ExistingPeriodicWorkPolicy
import androidx.work.ExistingWorkPolicy
import androidx.work.OneTimeWorkRequestBuilder
import androidx.work.PeriodicWorkRequestBuilder
import androidx.work.WorkManager
import androidx.work.workDataOf
import com.shuyi.discipline.data.model.CollageSettings
import com.shuyi.discipline.data.source.database.AppDatabase
import com.shuyi.discipline.domain.worker.DailyCollageWorker
import kotlinx.coroutines.runBlocking
import timber.log.Timber
import java.util.Calendar
import java.util.concurrent.TimeUnit

/**
 * 每日拼图调度用例
 */
class ScheduleDailyCollageUseCase(
    private val context: Context
) {

    /**
     * 调度每日拼图生成任务
     */
    fun schedule() {
        val workManager = WorkManager.getInstance(context)

        // 取消现有的任务
        workManager.cancelUniqueWork(WORK_NAME)

        // 获取拼图设置
        val database = AppDatabase.getInstance(context)
        val collageSettingsDao = database.collageSettingsDao()

        // 在后台线程中执行
        Thread {
            try {
                // 使用runBlocking在后台线程中运行挂起函数
                val settings = runBlocking { collageSettingsDao.getCollageSettingsSync() } ?: CollageSettings()

                // 如果未启用自动拼图，则不调度任务
                if (!settings.isAutoCollageEnabled) {
                    Timber.d("自动拼图未启用，不调度任务")
                    return@Thread
                }

                // 计算今天剩余时间
                val calendar = Calendar.getInstance()
                val currentHour = calendar.get(Calendar.HOUR_OF_DAY)
                val currentMinute = calendar.get(Calendar.MINUTE)

                // 目标时间：从设置中获取
                val targetHour = settings.collageHour
                val targetMinute = settings.collageMinute

                // 计算延迟
                val delay = if (currentHour < targetHour || (currentHour == targetHour && currentMinute < targetMinute)) {
                    // 当天的目标时间
                    val delayHours = targetHour - currentHour - (if (currentMinute > targetMinute) 1 else 0)
                    val delayMinutes = (60 + targetMinute - currentMinute) % 60
                    delayHours * 60L + delayMinutes
                } else {
                    // 第二天的目标时间
                    val delayHours = 24 - currentHour + targetHour - (if (currentMinute > targetMinute) 1 else 0)
                    val delayMinutes = (60 + targetMinute - currentMinute) % 60
                    delayHours * 60L + delayMinutes
                }

                Timber.d("计划在 ${delay} 分钟后生成每日拼图，时间: $targetHour:$targetMinute")

                // 创建工作请求
                val constraints = Constraints.Builder()
                    .setRequiresBatteryNotLow(true)
                    .build()

                val workRequest = OneTimeWorkRequestBuilder<DailyCollageWorker>()
                    .setInitialDelay(delay, TimeUnit.MINUTES)
                    .setConstraints(constraints)
                    .build()

                // 入队工作请求
                workManager.enqueueUniqueWork(
                    WORK_NAME,
                    ExistingWorkPolicy.REPLACE,
                    workRequest
                )

                Timber.d("每日拼图任务已调度")
            } catch (e: Exception) {
                Timber.e(e, "调度每日拼图任务失败")
            }
        }.start()
    }

    /**
     * 取消调度
     */
    fun cancel() {
        WorkManager.getInstance(context).cancelUniqueWork(WORK_NAME)
        Timber.d("已取消每日拼图生成任务")
    }

    companion object {
        private const val WORK_NAME = "daily_collage_generation"
    }
}