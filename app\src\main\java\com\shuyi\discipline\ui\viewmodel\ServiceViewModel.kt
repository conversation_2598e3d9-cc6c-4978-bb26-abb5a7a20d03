package com.shuyi.discipline.ui.viewmodel

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.media.projection.MediaProjectionManager
import android.net.Uri
import android.os.Build
import android.os.PowerManager
import android.provider.Settings
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.shuyi.discipline.data.model.ScheduleRule
import com.shuyi.discipline.data.repository.ScheduleRepository
import com.shuyi.discipline.domain.service.ScreenshotService
import com.shuyi.discipline.domain.usecase.ScheduleDailyCollageUseCase
import com.shuyi.discipline.ui.model.ServiceStatus
import com.shuyi.discipline.ui.model.UiState
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import timber.log.Timber

/**
 * 服务ViewModel
 */
class ServiceViewModel(
    private val context: Context,
    private val scheduleRepository: ScheduleRepository,
    private val scheduleDailyCollageUseCase: ScheduleDailyCollageUseCase
) : ViewModel() {

    // 服务状态
    private val _serviceStatus = MutableStateFlow<ServiceStatus>(ServiceStatus.Stopped)
    val serviceStatus: StateFlow<ServiceStatus> = _serviceStatus

    // 调度规则状态
    private val _scheduleRule = MutableStateFlow<UiState<ScheduleRule>>(UiState.Loading)
    val scheduleRule: StateFlow<UiState<ScheduleRule>> = _scheduleRule

    // 电池优化状态
    private val _batteryOptimizationIgnored = MutableStateFlow<Boolean>(false)
    val batteryOptimizationIgnored: StateFlow<Boolean> = _batteryOptimizationIgnored.asStateFlow()

    // 媒体投影权限
    private var mediaProjectionResultCode: Int = 0
    private var mediaProjectionResultData: Intent? = null

    init {
        // 检查电池优化状态
        checkBatteryOptimizationStatus()

        // 监听调度规则变化
        viewModelScope.launch {
            scheduleRepository.getScheduleRuleFlow().collectLatest { rule ->
                if (rule != null) {
                    _scheduleRule.value = UiState.Success(rule)

                    // 更新服务状态
                    _serviceStatus.value = if (rule.isEnabled) {
                        if (mediaProjectionResultData != null) {
                            ServiceStatus.Running
                        } else {
                            ServiceStatus.Unauthorized
                        }
                    } else {
                        ServiceStatus.Stopped
                    }
                } else {
                    _scheduleRule.value = UiState.Empty
                    _serviceStatus.value = ServiceStatus.Stopped
                }
            }
        }
    }

    /**
     * 检查电池优化状态
     */
    fun checkBatteryOptimizationStatus() {
        val powerManager = context.getSystemService(Context.POWER_SERVICE) as PowerManager
        val packageName = context.packageName

        val isIgnoringBatteryOptimizations = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            powerManager.isIgnoringBatteryOptimizations(packageName)
        } else {
            true // 在Android 6.0以下版本，不需要请求忽略电池优化
        }

        _batteryOptimizationIgnored.value = isIgnoringBatteryOptimizations
        Timber.d("电池优化状态: ${if (isIgnoringBatteryOptimizations) "已忽略" else "未忽略"}")
    }

    /**
     * 请求忽略电池优化
     * @return 包含请求意图的Intent，需要在Activity中启动
     */
    fun getRequestIgnoreBatteryOptimizationIntent(): Intent? {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val packageName = context.packageName
            Intent(Settings.ACTION_REQUEST_IGNORE_BATTERY_OPTIMIZATIONS).apply {
                data = Uri.parse("package:$packageName")
            }
        } else {
            null
        }
    }

    /**
     * 启动服务
     */
    fun startService() {
        viewModelScope.launch {
            try {
                // 获取调度规则
                val rule = scheduleRepository.getScheduleRule()

                // 更新调度规则服务状态
                scheduleRepository.updateServiceStatus(true)

                // 检查电池优化状态
                checkBatteryOptimizationStatus()

                // 如果有媒体投影权限，直接启动服务
                if (mediaProjectionResultData != null) {
                    val serviceIntent = Intent(context, ScreenshotService::class.java).apply {
                        action = ScreenshotService.ACTION_START_SERVICE
                        putExtra("media_projection_result_code", mediaProjectionResultCode)
                        putExtra("media_projection_data", mediaProjectionResultData)
                    }

                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
                        context.startForegroundService(serviceIntent)
                    } else {
                        context.startService(serviceIntent)
                    }

                    // 调度每日拼图生成
                    scheduleDailyCollageUseCase.schedule()

                    _serviceStatus.value = ServiceStatus.Running

                    // 记录日志
                    Timber.d("服务已启动，电池优化状态: ${if (_batteryOptimizationIgnored.value) "已忽略" else "未忽略"}")
                } else {
                    _serviceStatus.value = ServiceStatus.Unauthorized
                }
            } catch (e: Exception) {
                Timber.e(e, "启动服务失败")
                _serviceStatus.value = ServiceStatus.Error(e.message ?: "未知错误")
            }
        }
    }

    /**
     * 停止服务
     */
    fun stopService() {
        viewModelScope.launch {
            try {
                // 更新调度规则服务状态
                scheduleRepository.updateServiceStatus(false)

                // 停止服务
                val serviceIntent = Intent(context, ScreenshotService::class.java).apply {
                    action = ScreenshotService.ACTION_STOP_SERVICE
                }
                context.startService(serviceIntent)

                // 取消每日拼图生成
                scheduleDailyCollageUseCase.cancel()

                _serviceStatus.value = ServiceStatus.Stopped
            } catch (e: Exception) {
                Timber.e(e, "停止服务失败")
                _serviceStatus.value = ServiceStatus.Error(e.message ?: "未知错误")
            }
        }
    }

    /**
     * 更新调度规则
     */
    fun updateScheduleRule(rule: ScheduleRule) {
        viewModelScope.launch {
            try {
                scheduleRepository.saveScheduleRule(rule)

                // 如果服务正在运行，需要更新调度
                if (_serviceStatus.value == ServiceStatus.Running) {
                    val serviceIntent = Intent(context, ScreenshotService::class.java).apply {
                        action = ScreenshotService.ACTION_UPDATE_SCHEDULE
                    }
                    context.startService(serviceIntent)
                }
            } catch (e: Exception) {
                Timber.e(e, "更新调度规则失败")
            }
        }
    }

    /**
     * 请求媒体投影权限
     */
    fun requestMediaProjectionPermission(activity: Activity) {
        val mediaProjectionManager = activity.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
        val permissionIntent = mediaProjectionManager.createScreenCaptureIntent()
        activity.startActivityForResult(permissionIntent, REQUEST_MEDIA_PROJECTION)
    }

    /**
     * 处理活动结果
     */
    fun handleActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == REQUEST_MEDIA_PROJECTION && resultCode == Activity.RESULT_OK && data != null) {
            mediaProjectionResultCode = resultCode
            mediaProjectionResultData = data

            // 如果服务已启用，立即启动服务
            if (_serviceStatus.value == ServiceStatus.Unauthorized) {
                startService()
            }
        }
    }

    companion object {
        private const val REQUEST_MEDIA_PROJECTION = 1001
    }
}