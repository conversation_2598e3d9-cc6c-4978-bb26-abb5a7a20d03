package com.shuyi.discipline.data.dao

import androidx.room.*
import com.shuyi.discipline.data.entity.Screenshot
import kotlinx.coroutines.flow.Flow
import java.time.LocalDateTime

/**
 * 截图数据访问接口
 */
@Dao
interface ScreenshotDao {
    /**
     * 插入截图
     */
    @Insert
    suspend fun insertScreenshot(screenshot: Screenshot): Long

    /**
     * 更新截图
     */
    @Update
    suspend fun updateScreenshot(screenshot: Screenshot)

    /**
     * 删除截图
     */
    @Delete
    suspend fun deleteScreenshot(screenshot: Screenshot)

    /**
     * 根据ID获取截图
     */
    @Query("SELECT * FROM screenshots WHERE id = :id")
    suspend fun getScreenshotById(id: Long): Screenshot?

    /**
     * 获取所有截图
     */
    @Query("SELECT * FROM screenshots ORDER BY timestamp DESC")
    fun getAllScreenshots(): Flow<List<Screenshot>>

    /**
     * 根据时间范围获取截图
     */
    @Query("SELECT * FROM screenshots WHERE timestamp BETWEEN :startTime AND :endTime ORDER BY timestamp DESC")
    fun getScreenshotsByTimeRange(startTime: LocalDateTime, endTime: LocalDateTime): Flow<List<Screenshot>>

    /**
     * 获取已审阅的截图
     */
    @Query("SELECT * FROM screenshots WHERE isReviewed = 1 ORDER BY timestamp DESC")
    fun getReviewedScreenshots(): Flow<List<Screenshot>>

    /**
     * 获取未审阅的截图
     */
    @Query("SELECT * FROM screenshots WHERE isReviewed = 0 ORDER BY timestamp DESC")
    fun getUnreviewedScreenshots(): Flow<List<Screenshot>>

    /**
     * 根据应用包名获取截图
     */
    @Query("SELECT * FROM screenshots WHERE packageName = :packageName ORDER BY timestamp DESC")
    fun getScreenshotsByPackageName(packageName: String): Flow<List<Screenshot>>

    /**
     * 根据分类获取截图
     */
    @Query("SELECT * FROM screenshots WHERE categoryId = :categoryId ORDER BY timestamp DESC")
    fun getScreenshotsByCategory(categoryId: Long): Flow<List<Screenshot>>
} 