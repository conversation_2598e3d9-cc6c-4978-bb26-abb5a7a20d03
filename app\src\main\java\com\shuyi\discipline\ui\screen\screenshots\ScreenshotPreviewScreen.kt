package com.shuyi.discipline.ui.screen.screenshots

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.viewmodel.compose.viewModel
import com.shuyi.discipline.ui.components.ZoomableImage

/**
 * 截图预览屏幕
 */
@OptIn(ExperimentalFoundationApi::class)
@Composable
fun ScreenshotPreviewScreen(
    initialScreenshotIndex: Int = 0,
    onNavigateBack: () -> Unit,
    viewModel: ScreenshotsViewModel = viewModel(
        factory = ScreenshotsViewModelFactory(LocalContext.current.applicationContext as android.app.Application)
    )
) {
    val uiState by viewModel.uiState.collectAsState()

    // 使用统一的截图列表
    val allScreenshots = uiState.allScreenshots

    // 如果没有截图，返回
    if (allScreenshots.isEmpty()) {
        LaunchedEffect(Unit) {
            onNavigateBack()
        }
        return
    }

    // 创建分页状态
    val pagerState = rememberPagerState(
        initialPage = initialScreenshotIndex.coerceIn(0, allScreenshots.size - 1),
        pageCount = { allScreenshots.size }
    )

    // 全屏显示截图，黑色背景
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        // 图片分页器 - 禁止无限循环滑动
        HorizontalPager(
            state = pagerState,
            modifier = Modifier.fillMaxSize(),
            beyondBoundsPageCount = 0 // 禁止预加载边界外的页面
        ) { page ->
            val screenshot = allScreenshots[page]

            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .pointerInput(Unit) {
                        detectTapGestures(
                            onTap = { onNavigateBack() }
                        )
                    }
            ) {
                // 使用可缩放的图片组件
                ZoomableImage(
                    imagePath = screenshot.filePath,
                    contentDescription = "截图预览",
                    modifier = Modifier.fillMaxSize()
                )
            }
        }
    }
}
