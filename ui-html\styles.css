/* 全局样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* 深色模式 */
html.dark body {
    background-color: #000;
    color: #fff;
}

/* 原型容器样式 */
.prototype-container {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 16px;
    margin-bottom: 24px;
    transition: background-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}

/* 深色模式下的原型容器 */
.prototype-container.dark-mode {
    background-color: #1a1a1a;
    color: #fff;
    box-shadow: 0 4px 6px rgba(255, 255, 255, 0.05);
}

/* 深色模式下的标题 */
html.dark h1, html.dark h2 {
    color: #fff;
}

/* 手机框架样式 */
.phone-frame {
    width: 375px;
    height: 812px;
    background-color: white;
    border-radius: 40px;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    position: relative;
    margin: 0 auto;
    border: 12px solid #1a1a1a;
    transition: box-shadow 0.3s ease;
}

/* 深色模式下的手机框架 */
html.dark .phone-frame {
    box-shadow: 0 10px 25px rgba(255, 255, 255, 0.05);
}

.phone-frame iframe {
    width: 100%;
    height: 100%;
    border: none;
}

/* 移动端通用样式 */
.mobile-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background-color: #f8f8f8;
}

/* 状态栏样式 */
.status-bar {
    height: 44px;
    background-color: #f8f8f8;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 16px;
    font-size: 14px;
    font-weight: 600;
    color: #000;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.status-bar-time {
    font-weight: 600;
}

.status-bar-icons {
    display: flex;
    align-items: center;
}

.status-bar-icons svg {
    margin-left: 6px;
}

/* 内容区域样式 */
.content-area {
    flex: 1;
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    padding: 16px;
    padding-bottom: 100px; /* 为固定的底部导航栏留出空间 */
}

/* 隐藏滚动条 */
.content-area::-webkit-scrollbar {
    display: none;
}

/* 底部导航栏样式 */
.tab-bar {
    height: 83px;
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: space-around;
    align-items: center;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding-bottom: 20px; /* 为底部安全区域留出空间 */
    position: fixed; /* 固定定位 */
    bottom: 0; /* 固定在底部 */
    left: 0;
    right: 0;
    z-index: 1000; /* 确保导航栏在最上层 */
}

.tab-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #8e8e93;
    font-size: 10px;
    padding: 8px 0;
}

.tab-item.active {
    color: #007aff;
}

.tab-item svg {
    margin-bottom: 4px;
}

/* 深色模式下的底部导航栏 */
html.dark .tab-bar {
    background-color: rgba(30, 30, 30, 0.9);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 卡片样式 */
.card {
    background-color: white;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    padding: 16px;
    margin-bottom: 16px;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 16px;
    transition: all 0.2s;
}

.btn-primary {
    background-color: #007aff;
    color: white;
}

.btn-secondary {
    background-color: #e9e9eb;
    color: #000;
}

/* 开关样式 */
.toggle {
    position: relative;
    display: inline-block;
    width: 51px;
    height: 31px;
}

.toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #e9e9eb;
    transition: .4s;
    border-radius: 34px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 27px;
    width: 27px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .toggle-slider {
    background-color: #34c759;
}

input:checked + .toggle-slider:before {
    transform: translateX(20px);
}

/* 列表样式 */
.list-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.list-item:last-child {
    border-bottom: none;
}

/* 图片网格样式 */
.image-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

/* 日期选择器样式 */
.date-selector {
    display: flex;
    overflow-x: auto;
    padding: 12px 0;
    margin-bottom: 16px;
    -webkit-overflow-scrolling: touch;
}

.date-selector::-webkit-scrollbar {
    display: none;
}

.date-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 16px;
    margin-right: 8px;
    border-radius: 12px;
    background-color: white;
    min-width: 60px;
}

.date-item.active {
    background-color: #007aff;
    color: white;
}

/* 状态指示器样式 */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-indicator.green {
    background-color: #34c759;
}

.status-indicator.red {
    background-color: #ff3b30;
}

.status-indicator.yellow {
    background-color: #ffcc00;
}

/* 进度条样式 */
.progress-bar {
    height: 8px;
    background-color: #e9e9eb;
    border-radius: 4px;
    overflow: hidden;
    margin: 8px 0;
}

.progress-bar-fill {
    height: 100%;
    background-color: #007aff;
    border-radius: 4px;
}

/* 时间线样式 */
.timeline {
    position: relative;
    margin: 20px 0;
}

.timeline-line {
    position: absolute;
    left: 16px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #e9e9eb;
}

.timeline-item {
    position: relative;
    padding-left: 40px;
    margin-bottom: 16px;
}

.timeline-dot {
    position: absolute;
    left: 12px;
    top: 6px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #007aff;
}

/* 权限项样式 */
.permission-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.permission-item:last-child {
    border-bottom: none;
}

.permission-info {
    flex: 1;
}

.permission-title {
    font-weight: 600;
    margin-bottom: 4px;
}

.permission-desc {
    font-size: 14px;
    color: #8e8e93;
}
