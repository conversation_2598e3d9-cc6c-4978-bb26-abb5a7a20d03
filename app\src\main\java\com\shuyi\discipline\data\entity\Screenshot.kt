package com.shuyi.discipline.data.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverters
import com.shuyi.discipline.data.converter.LocalDateTimeConverter
import java.time.LocalDateTime

/**
 * 截图实体类
 * @property id 唯一标识
 * @property path 截图文件路径
 * @property timestamp 截图时间戳
 * @property appName 应用名称
 * @property packageName 应用包名
 * @property isReviewed 是否已审核
 * @property categoryId 分类ID
 * @property notes 备注
 */
@Entity(tableName = "screenshots")
@TypeConverters(LocalDateTimeConverter::class)
data class Screenshot(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val path: String,
    val timestamp: LocalDateTime = LocalDateTime.now(),
    val appName: String? = null,
    val packageName: String? = null,
    val isReviewed: Boolean = false,
    val categoryId: Long? = null,
    val notes: String? = null
) 