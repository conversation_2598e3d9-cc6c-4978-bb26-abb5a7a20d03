package com.shuyi.discipline.data.database.dao

import androidx.room.*
import com.shuyi.discipline.data.database.entity.RuntimeSessionEntity
import com.shuyi.discipline.domain.monitor.SessionStatus
import kotlinx.coroutines.flow.Flow

/**
 * 运行时会话 DAO
 */
@Dao
interface RuntimeSessionDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertSession(session: RuntimeSessionEntity)

    @Update
    suspend fun updateSession(session: RuntimeSessionEntity)

    @Delete
    suspend fun deleteSession(session: RuntimeSessionEntity)

    @Query("SELECT * FROM runtime_sessions WHERE session_id = :sessionId")
    suspend fun getSessionById(sessionId: String): RuntimeSessionEntity?

    @Query("SELECT * FROM runtime_sessions ORDER BY start_time DESC")
    fun getAllSessions(): Flow<List<RuntimeSessionEntity>>

    @Query("SELECT * FROM runtime_sessions WHERE start_time BETWEEN :startTime AND :endTime ORDER BY start_time DESC")
    suspend fun getSessionsInRange(startTime: Long, endTime: Long): List<RuntimeSessionEntity>

    @Query("SELECT * FROM runtime_sessions WHERE status = :status ORDER BY start_time DESC")
    suspend fun getSessionsByStatus(status: SessionStatus): List<RuntimeSessionEntity>

    @Query("SELECT * FROM runtime_sessions WHERE status IN ('FOREGROUND', 'BACKGROUND') ORDER BY start_time DESC")
    suspend fun getUnfinishedSessions(): List<RuntimeSessionEntity>

    @Query("SELECT * FROM runtime_sessions WHERE status IN ('FOREGROUND', 'BACKGROUND') ORDER BY start_time DESC LIMIT 1")
    suspend fun getCurrentSession(): RuntimeSessionEntity?

    @Query("SELECT * FROM runtime_sessions ORDER BY start_time DESC LIMIT :limit")
    suspend fun getRecentSessions(limit: Int): List<RuntimeSessionEntity>

    @Query("SELECT COUNT(*) FROM runtime_sessions WHERE start_time BETWEEN :startTime AND :endTime")
    suspend fun getSessionCountInRange(startTime: Long, endTime: Long): Int

    @Query("SELECT SUM(total_duration) FROM runtime_sessions WHERE start_time BETWEEN :startTime AND :endTime AND status = 'ENDED'")
    suspend fun getTotalRuntimeInRange(startTime: Long, endTime: Long): Long?

    @Query("SELECT SUM(foreground_duration) FROM runtime_sessions WHERE start_time BETWEEN :startTime AND :endTime")
    suspend fun getTotalForegroundTimeInRange(startTime: Long, endTime: Long): Long?

    @Query("SELECT SUM(background_duration) FROM runtime_sessions WHERE start_time BETWEEN :startTime AND :endTime")
    suspend fun getTotalBackgroundTimeInRange(startTime: Long, endTime: Long): Long?

    @Query("SELECT AVG(total_duration) FROM runtime_sessions WHERE start_time BETWEEN :startTime AND :endTime AND status = 'ENDED'")
    suspend fun getAverageSessionDurationInRange(startTime: Long, endTime: Long): Long?

    @Query("SELECT MAX(end_time) FROM runtime_sessions WHERE status = 'ENDED' AND end_time IS NOT NULL")
    suspend fun getLastSessionEndTime(): Long?

    @Query("UPDATE runtime_sessions SET status = :status, updated_at = :updatedAt WHERE session_id = :sessionId")
    suspend fun updateSessionStatus(sessionId: String, status: SessionStatus, updatedAt: Long)

    @Query("""
        UPDATE runtime_sessions 
        SET end_time = :endTime, 
            status = 'ENDED', 
            exit_reason = :exitReason,
            total_duration = :endTime - start_time,
            updated_at = :updatedAt
        WHERE session_id = :sessionId
    """)
    suspend fun endSession(sessionId: String, endTime: Long, exitReason: String, updatedAt: Long)

    @Query("""
        UPDATE runtime_sessions 
        SET foreground_duration = :foregroundDuration,
            background_duration = :backgroundDuration,
            updated_at = :updatedAt
        WHERE session_id = :sessionId
    """)
    suspend fun updateSessionDurations(sessionId: String, foregroundDuration: Long, backgroundDuration: Long, updatedAt: Long)

    @Query("DELETE FROM runtime_sessions WHERE start_time < :cutoffTime")
    suspend fun deleteOldSessions(cutoffTime: Long): Int

    @Query("DELETE FROM runtime_sessions")
    suspend fun deleteAllSessions()

    @Query("SELECT COUNT(*) FROM runtime_sessions")
    suspend fun getSessionCount(): Int

    @Query("""
        SELECT status, COUNT(*) as count, AVG(total_duration) as avg_duration
        FROM runtime_sessions 
        WHERE start_time BETWEEN :startTime AND :endTime 
        GROUP BY status
    """)
    suspend fun getSessionStatistics(startTime: Long, endTime: Long): List<SessionStatistics>

    @Query("""
        SELECT 
            COUNT(*) as total_sessions,
            SUM(CASE WHEN status = 'ENDED' THEN 1 ELSE 0 END) as completed_sessions,
            SUM(total_duration) as total_runtime,
            AVG(total_duration) as avg_session_duration,
            MAX(total_duration) as max_session_duration,
            MIN(total_duration) as min_session_duration
        FROM runtime_sessions 
        WHERE start_time BETWEEN :startTime AND :endTime
    """)
    suspend fun getDetailedStatistics(startTime: Long, endTime: Long): DetailedSessionStatistics?
}

/**
 * 会话统计数据类
 */
data class SessionStatistics(
    val status: SessionStatus,
    val count: Int,
    val avg_duration: Long
)

/**
 * 详细会话统计数据类
 */
data class DetailedSessionStatistics(
    val total_sessions: Int,
    val completed_sessions: Int,
    val total_runtime: Long,
    val avg_session_duration: Long,
    val max_session_duration: Long,
    val min_session_duration: Long
)
