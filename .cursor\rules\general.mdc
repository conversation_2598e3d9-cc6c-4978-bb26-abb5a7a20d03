---
description: 
globs: 
alwaysApply: true
---
# 通用

## MCP Interactive Feedback 规则

1. 在任何流程、任务、对话进行时，无论是询问、回复、或完成阶段性任务，皆必须调用 MCP mcp-feedback-enhanced。
2. 注意，当你刚完成一次需要修改代码的任务后，应该先执行自动构建项目、修复问题的工作流，等到这个工作流执行完毕后，再调用 MCP mcp-feedback-enhanced
3. 每当收到用户反馈，若反馈内容非空，必须再次调用 MCP mcp-feedback-enhanced，并根据反馈内容调整行为。
4. 仅当用户明确表示「结束」或「不再需要交互」时，才可停止调用 MCP mcp-feedback-enhanced，流程才算结束。
5. 除非收到结束指令，否则所有步骤都必须重复调用 MCP mcp-feedback-enhanced。

6. 完成任务前，必须使用 MCP mcp-feedback-enhanced 工具向用户询问反馈。