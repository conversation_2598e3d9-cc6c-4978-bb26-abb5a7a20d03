package com.shuyi.discipline.data.repository.impl

import com.shuyi.discipline.data.model.MonitorStatistics
import com.shuyi.discipline.data.model.MonitorStatus
import com.shuyi.discipline.data.model.MonitorType
import com.shuyi.discipline.data.model.SystemMonitorRecord
import com.shuyi.discipline.data.repository.SystemMonitorRepository
import com.shuyi.discipline.data.source.database.SystemMonitorDao
import kotlinx.coroutines.flow.Flow
import timber.log.Timber
import java.util.UUID

/**
 * 系统监控仓库实现类
 */
class SystemMonitorRepositoryImpl(
    private val systemMonitorDao: SystemMonitorDao
) : SystemMonitorRepository {

    override suspend fun insertRecord(record: SystemMonitorRecord) {
        try {
            systemMonitorDao.insertRecord(record)
            Timber.d("插入监控记录成功: ${record.id}")
        } catch (e: Exception) {
            Timber.e(e, "插入监控记录失败: ${record.id}")
            throw e
        }
    }

    override suspend fun updateRecord(record: SystemMonitorRecord) {
        try {
            systemMonitorDao.updateRecord(record)
            Timber.d("更新监控记录成功: ${record.id}")
        } catch (e: Exception) {
            Timber.e(e, "更新监控记录失败: ${record.id}")
            throw e
        }
    }

    override suspend fun getRecordById(id: String): SystemMonitorRecord? {
        return try {
            systemMonitorDao.getRecordById(id)
        } catch (e: Exception) {
            Timber.e(e, "获取监控记录失败: $id")
            null
        }
    }

    override suspend fun getRecordsByType(type: MonitorType): List<SystemMonitorRecord> {
        return try {
            systemMonitorDao.getRecordsByType(type)
        } catch (e: Exception) {
            Timber.e(e, "获取监控记录失败: $type")
            emptyList()
        }
    }

    override fun getRecordsByTypeFlow(type: MonitorType): Flow<List<SystemMonitorRecord>> {
        return systemMonitorDao.getRecordsByTypeFlow(type)
    }

    override suspend fun getRecordsByTypeAndTimeRange(
        type: MonitorType,
        startTime: Long,
        endTime: Long
    ): List<SystemMonitorRecord> {
        return try {
            systemMonitorDao.getRecordsByTypeAndTimeRange(type, startTime, endTime)
        } catch (e: Exception) {
            Timber.e(e, "获取时间范围内监控记录失败: $type, $startTime-$endTime")
            emptyList()
        }
    }

    override suspend fun getLatestUnfinishedRecord(
        type: MonitorType,
        status: MonitorStatus
    ): SystemMonitorRecord? {
        return try {
            systemMonitorDao.getLatestUnfinishedRecord(type, status)
        } catch (e: Exception) {
            Timber.e(e, "获取最新未完成记录失败: $type, $status")
            null
        }
    }

    override suspend fun finishRecord(
        id: String,
        endTime: Long,
        duration: Long,
        status: MonitorStatus
    ) {
        try {
            systemMonitorDao.finishRecord(id, endTime, duration, status)
            Timber.d("完成监控记录: $id, 持续时长: ${duration}ms")
        } catch (e: Exception) {
            Timber.e(e, "完成监控记录失败: $id")
            throw e
        }
    }

    override suspend fun recordAbnormalStart(
        type: MonitorType,
        description: String,
        isDeviceShutdown: Boolean
    ): String {
        val recordId = UUID.randomUUID().toString()
        val currentTime = System.currentTimeMillis()

        val record = SystemMonitorRecord(
            id = recordId,
            timestamp = currentTime,
            monitorType = type,
            status = MonitorStatus.ABNORMAL,
            startTime = currentTime,
            endTime = null,
            duration = 0L,
            description = description,
            isDeviceShutdown = isDeviceShutdown
        )

        insertRecord(record)
        Timber.d("记录异常开始: $type - $description")
        return recordId
    }

    override suspend fun recordAbnormalEnd(recordId: String) {
        val record = getRecordById(recordId)
        if (record != null) {
            val endTime = System.currentTimeMillis()
            val duration = endTime - record.startTime
            finishRecord(recordId, endTime, duration, MonitorStatus.RECOVERED)
            Timber.d("记录异常结束: $recordId, 持续时长: ${duration}ms")
        } else {
            Timber.w("未找到要结束的异常记录: $recordId")
        }
    }

    override suspend fun get24HourStatistics(type: MonitorType): MonitorStatistics {
        val endTime = System.currentTimeMillis()
        val startTime = endTime - 24 * 60 * 60 * 1000L // 24小时前

        val records = getRecordsByTypeAndTimeRange(type, startTime, endTime)
        val abnormalRecords = records.filter { it.status == MonitorStatus.ABNORMAL || it.status == MonitorStatus.RECOVERED }

        // 计算异常时间
        val abnormalTime = abnormalRecords.sumOf { record ->
            when {
                record.endTime != null -> record.duration
                record.status == MonitorStatus.ABNORMAL -> endTime - record.startTime
                else -> 0L
            }
        }

        val totalTime = 24 * 60 * 60 * 1000L // 24小时
        val normalTime = totalTime - abnormalTime
        val normalPercentage = (normalTime.toFloat() / totalTime.toFloat()) * 100f

        return MonitorStatistics(
            monitorType = type,
            totalTime = totalTime,
            normalTime = normalTime,
            abnormalTime = abnormalTime,
            normalPercentage = normalPercentage,
            abnormalRecords = abnormalRecords
        )
    }

    override suspend fun cleanOldRecords(cutoffTime: Long) {
        try {
            systemMonitorDao.deleteOldRecords(cutoffTime)
            Timber.d("清理旧监控记录成功，截止时间: $cutoffTime")
        } catch (e: Exception) {
            Timber.e(e, "清理旧监控记录失败")
            throw e
        }
    }

    override suspend fun deleteAllRecords() {
        try {
            systemMonitorDao.deleteAllRecords()
            Timber.d("删除所有监控记录成功")
        } catch (e: Exception) {
            Timber.e(e, "删除所有监控记录失败")
            throw e
        }
    }

    override suspend fun insertTestData() {
        try {
            val currentTime = System.currentTimeMillis()

            // 创建一些测试的异常记录
            val testRecords = listOf(
                // 应用后台运行异常记录
                SystemMonitorRecord(
                    id = UUID.randomUUID().toString(),
                    timestamp = currentTime - 3 * 60 * 60 * 1000L, // 3小时前
                    monitorType = MonitorType.APP_BACKGROUND_RUNNING,
                    status = MonitorStatus.RECOVERED,
                    startTime = currentTime - 3 * 60 * 60 * 1000L,
                    endTime = currentTime - 3 * 60 * 60 * 1000L + 30 * 60 * 1000L, // 持续30分钟
                    duration = 30 * 60 * 1000L,
                    description = "应用后台运行异常，可能被系统强制关闭或服务停止",
                    isDeviceShutdown = false
                ),
                SystemMonitorRecord(
                    id = UUID.randomUUID().toString(),
                    timestamp = currentTime - 12 * 60 * 60 * 1000L, // 12小时前
                    monitorType = MonitorType.APP_BACKGROUND_RUNNING,
                    status = MonitorStatus.RECOVERED,
                    startTime = currentTime - 12 * 60 * 60 * 1000L,
                    endTime = currentTime - 12 * 60 * 60 * 1000L + 18 * 60 * 1000L, // 持续18分钟
                    duration = 18 * 60 * 1000L,
                    description = "应用后台运行异常，可能被系统强制关闭或服务停止",
                    isDeviceShutdown = false
                ),

                // 截屏功能异常记录
                SystemMonitorRecord(
                    id = UUID.randomUUID().toString(),
                    timestamp = currentTime - 3 * 60 * 60 * 1000L, // 3小时前
                    monitorType = MonitorType.SCREENSHOT_FUNCTION,
                    status = MonitorStatus.RECOVERED,
                    startTime = currentTime - 3 * 60 * 60 * 1000L,
                    endTime = currentTime - 3 * 60 * 60 * 1000L + 30 * 60 * 1000L, // 持续30分钟
                    duration = 30 * 60 * 1000L,
                    description = "截屏功能异常，可能缺少权限或服务停止",
                    isDeviceShutdown = false
                ),
                SystemMonitorRecord(
                    id = UUID.randomUUID().toString(),
                    timestamp = currentTime - 18 * 60 * 60 * 1000L, // 18小时前
                    monitorType = MonitorType.SCREENSHOT_FUNCTION,
                    status = MonitorStatus.RECOVERED,
                    startTime = currentTime - 18 * 60 * 60 * 1000L,
                    endTime = currentTime - 18 * 60 * 60 * 1000L + 30 * 60 * 1000L, // 持续30分钟
                    duration = 30 * 60 * 1000L,
                    description = "截屏功能暂停",
                    isDeviceShutdown = false
                ),
                SystemMonitorRecord(
                    id = UUID.randomUUID().toString(),
                    timestamp = currentTime - 20 * 60 * 60 * 1000L, // 20小时前
                    monitorType = MonitorType.SCREENSHOT_FUNCTION,
                    status = MonitorStatus.RECOVERED,
                    startTime = currentTime - 20 * 60 * 60 * 1000L,
                    endTime = currentTime - 20 * 60 * 60 * 1000L + 12 * 60 * 1000L, // 持续12分钟
                    duration = 12 * 60 * 1000L,
                    description = "截屏功能异常，可能缺少权限或服务停止",
                    isDeviceShutdown = false
                )
            )

            // 插入测试记录
            testRecords.forEach { record ->
                insertRecord(record)
            }

            Timber.d("插入测试数据成功，共${testRecords.size}条记录")
        } catch (e: Exception) {
            Timber.e(e, "插入测试数据失败")
            throw e
        }
    }
}
